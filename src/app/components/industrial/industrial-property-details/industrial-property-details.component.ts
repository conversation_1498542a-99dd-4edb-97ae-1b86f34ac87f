import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Property } from '../../../models/Property';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { numericValidator, websitePattern } from '../../../utils';

@Component({
  selector: 'app-industrial-property-details',
  templateUrl: './industrial-property-details.component.html',
  styleUrls: ['./industrial-property-details.component.css']
})
export class IndustrialPropertyDetailsComponent implements OnInit {
  @Input() industrialForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  
  constructor() { }

  ngOnInit(): void {
    this.addControls();
  }

  addControls() {
    Object.keys(IndustrialControls).forEach((controlName: IndustrialControls) => {
      if (IndustrialControls.DockHigh === controlName || IndustrialControls.Truckwell === controlName) {
        this.industrialForm.addControl(controlName, new FormControl('', [Validators.min(0), Validators.max(999)]))
      } else if (IndustrialControls.ClearHeightMax === controlName || IndustrialControls.ClearHeightMin === controlName) {
        this.industrialForm.addControl(controlName, new FormControl('', {
          validators: [Validators.min(3), Validators.max(999)], updateOn: 'blur'
        }))
      } else if ([IndustrialControls.SmallestFloor, IndustrialControls.LargestFloor].includes(controlName)) {
        this.industrialForm.addControl(controlName, new FormControl('', [numericValidator()]));
      } else {
        this.industrialForm.addControl(controlName, new FormControl(''));
      }
    });
  }

}
