import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { IndustrialPropertyDetailsComponent } from './industrial-property-details.component';

describe('IndustrialPropertyDetailsComponent', () => {
  let component: IndustrialPropertyDetailsComponent;
  let fixture: ComponentFixture<IndustrialPropertyDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ IndustrialPropertyDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IndustrialPropertyDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
