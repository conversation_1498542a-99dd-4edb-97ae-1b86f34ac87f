import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { YardLotHeightDetailsComponent } from './yard-lot-height-details.component';

describe('YardLotHeightDetailsComponent', () => {
  let component: YardLotHeightDetailsComponent;
  let fixture: ComponentFixture<YardLotHeightDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ YardLotHeightDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(YardLotHeightDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
