import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Property } from '../../../models/Property';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { numericValidator, websitePattern } from '../../../utils';

@Component({
  selector: 'app-office-property-details',
  templateUrl: './office-property-details.component.html',
  styleUrls: ['./office-property-details.component.css'],
})
export class OfficePropertyDetailsComponent implements OnInit {
  @Input() officeForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;

  constructor() { }

  ngOnInit(): void {
    this.addControls();
  }

  addControls() {
    Object.keys(OfficeControls).forEach((controlName: OfficeControls) => {
      if ([OfficeControls.SmallestFloor, OfficeControls.LargestFloor, OfficeControls.TypicalFloorSizeSM].includes(controlName)) {
        this.officeForm.addControl(controlName, new FormControl('', [numericValidator()]));
      } else {
        this.officeForm.addControl(controlName, new FormControl(''));
      }
    });
  }
  
}
