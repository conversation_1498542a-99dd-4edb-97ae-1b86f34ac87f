<div [formGroup]="officeForm" class="details-wrapper">
  <div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Typical Floor Size</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputTypicalFloorSizeSM">
              <input type="text" numericOnly allowDecimal="true" allowNegative="false" formControlName="TypicalFloorSizeSM" [(ngModel)]="property.TypicalFloorSizeSM" class="form-control"
              [ngClass]="{'error-field': !officeForm.controls['TypicalFloorSizeSM']?.valid}" (paste)="validatePasteInput($event, true)"
              (keypress)="validateIntegerInput($event, true)">
            </ng-container>
            <ng-template #masterRollupInputTypicalFloorSizeSM>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TypicalFloorSize" title="{{rollupMasterFreeholdFieldsObject?.TypicalFloorSize}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Typical Floor Plate Source</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="TypicalFloorSizeSourceID"
              [items]="getDropdownFromLookup('TypicalFloorSizeSourceID')" [virtualScroll]="true" bindLabel="TypicalFloorPlateName"
              bindValue="TypicalFloorPlateID" placeholder="--Select--" [(ngModel)]="property.TypicalFloorSizeSourceID"
              labelForId="HasSprinkler" (change)="onValueChange('TypicalFloorSizeSourceID', $event, 'TypicalFloorPlateName')"
              [ngClass]="{'error-field':(officeForm?.controls['TypicalFloorSizeSourceID']?.enabled && !officeForm?.controls['TypicalFloorSizeSourceID']?.valid)}">
            </ng-select>
            <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TypicalFloorSizeSourceID" *ngIf="condo === EnumCondoTypeNames.Master_Freehold"
              title="{{rollupMasterFreeholdFieldsObject?.TypicalFloorSizeSourceID}}" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Apartments">
          <div class="col-md-5 label"># of Units</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputNoOfUnits">
              <input type="text" numericOnly allowDecimal="false" allowNegative="false" formControlName="NoOfUnits"
                [(ngModel)]="property.NoOfUnits" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputNoOfUnits>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.NoOfUnits" title="{{rollupMasterFreeholdFieldsObject?.NoOfUnits}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinklers</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="HasSprinkler" [items]="getDropdownFromLookup('HasSprinkler')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasSprinkler"
              labelForId="HasSprinkler" (change)="onValueChange('HasSprinkler', $event, 'label')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasSprinkler)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinkler Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="SprinklerTypeID" [items]="getDropdownFromLookup('SprinklerTypeID')"
              [virtualScroll]="true" bindLabel="SprinklerTypeName" bindValue="SprinklerTypeID"
              placeholder="--Select--" [(ngModel)]="property.SprinklerTypeID"
              labelForId="SprinklerType" (change)="onValueChange('SprinklerTypeID', $event, 'SprinklerTypeName')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" readonly title="{{rollupMasterFreeholdFieldsObject?.SprinklerTypeName}}" [value]="rollupMasterFreeholdFieldsObject?.SprinklerTypeName" type="text" class="form-control">
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Amenities Type</div>
          <div class="col-md-7">
            <ng-select formControlName="AmenitiesTypeID" [items]="getDropdownFromLookup('AmenitiesTypeID')" [multiple]="true"
              [virtualScroll]="true" bindLabel="AmenitiesTypeName" bindValue="AmenitiesTypeID"
              placeholder="--Select--" [(ngModel)]="amenitiesType" [ngClass]="{'scrollable-dropdown': condo === EnumCondoTypeNames.Master_Freehold}"
              labelForId="AmenitiesType" (change)="onValueChange('AmenitiesTypeID', $event, 'AmenitiesTypeName')">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Amenities Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="Amenities" [(ngModel)]="property.Amenities" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="BuildingComments" [(ngModel)]="property.BuildingComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Website</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputBuildingWebsite">
              <input type="text" formControlName="BuildingWebsite" [(ngModel)]="property.BuildingWebsite" class="form-control"
                [ngClass]="{'error-field': !isBuildingWebsiteValueValid}">
            </ng-container>
            <ng-template #masterRollupInputBuildingWebsite>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.BuildingWebsite" title="{{rollupMasterFreeholdFieldsObject?.BuildingWebsite}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">Parking</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Reserved Parking</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold"  formControlName="HasReservedParkingSpaces" [items]="getDropdownFromLookup('HasReservedParkingSpaces')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasReservedParkingSpaces"
              labelForId="HasReservedParkingSpaces" (change)="onValueChange('HasReservedParkingSpaces', $event, 'label')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasReservedParkingSpaces)" type="text" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Reserved</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputReservedParkingSpaces">
              <input type="text" numericOnly allowDecimal="false" allowNegative="false" formControlName="ReservedParkingSpaces"
                [(ngModel)]="property.ReservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputReservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Reserved Rate/mo</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputReservedParkingSpacesRatePerMonth">
              <input type="text" numericOnly allowDecimal="false" allowNegative="false" formControlName="ReservedParkingSpacesRatePerMonth"
              [(ngModel)]="property.ReservedParkingSpacesRatePerMonth" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputReservedParkingSpacesRatePerMonth>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ReservedParkingSpacesRatePerMonth" title="{{rollupMasterFreeholdFieldsObject?.ReservedParkingSpacesRatePerMonth}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Unreserved Parking</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="HasUnreservedParkingSpaces" [items]="getDropdownFromLookup('HasUnreservedParkingSpaces')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasUnreservedParkingSpaces"
              labelForId="HasUnreservedParkingSpaces" (change)="onValueChange('HasUnreservedParkingSpaces', $event, 'label')">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasUnreservedParkingSpaces)" type="text" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Unreserved</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputUnreservedParkingSpaces">
              <input type="text" numericOnly allowDecimal="false" allowNegative="false" formControlName="UnreservedParkingSpaces"
                [(ngModel)]="property.UnreservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputUnreservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Unreserved Rate/mo</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputUnreservedParkingSpacesRatePerMonth">
              <input type="text" numericOnly allowDecimal="false" allowNegative="false" formControlName="UnreservedParkingSpacesRatePerMonth"
              [(ngModel)]="property.UnreservedParkingSpacesRatePerMonth" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputUnreservedParkingSpacesRatePerMonth>
              <input type="text" 
              [value]="rollupMasterFreeholdFieldsObject?.UnreservedParkingSpacesRatePerMonth" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.UnreservedParkingSpacesRatePerMonth}}" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">Lifts</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Passenger</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputPassengerElevators">
              <input formControlName="PassengerElevators" [(ngModel)]="property.PassengerElevators" class="form-control" 
              type="text" numericOnly allowDecimal="false" allowNegative="false" >
            </ng-container>
            <ng-template #masterRollupInputPassengerElevators>
              <input [value]="rollupMasterFreeholdFieldsObject?.PassengerElevators" title="{{rollupMasterFreeholdFieldsObject?.PassengerElevators}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Freight</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputFreighElevators">
              <input formControlName="FreighElevators" [(ngModel)]="property.FreighElevators" class="form-control" type="text"
                numericOnly allowDecimal="false" allowNegative="false">
            </ng-container>
            <ng-template #masterRollupInputFreighElevators>
              <input  [value]="rollupMasterFreeholdFieldsObject?.FreighElevators" title="{{rollupMasterFreeholdFieldsObject?.FreighElevators}}" class="form-control" type="text" readonly>
            </ng-template>
            </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Parking</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputFreighParkingElevators">
              <input formControlName="ParkingElevators" [(ngModel)]="property.ParkingElevators" class="form-control"
              type="text" numericOnly allowDecimal="false" allowNegative="false" >
            </ng-container>
            <ng-template #masterRollupInputFreighParkingElevators>
              <input  [value]="rollupMasterFreeholdFieldsObject?.ParkingElevators" title="{{rollupMasterFreeholdFieldsObject?.ParkingElevators}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
