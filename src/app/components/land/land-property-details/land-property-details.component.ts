import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { LoginService } from '../../../services/login.service';
import { Property } from '../../../models/Property';
import { LandControls } from '../../../enumerations/landControlKeys'
import { BindNamesWithLookupName, YesNoFields, YesOrNoList, ZoningClasses } from '../../../common/constants';
import { buildChangeLog, getPreviousData, validateIntegerInput, validatePasteInput } from '../../../utils';
import { IAngularMyDpOptions } from 'angular-mydatepicker';

@Component({
  selector: 'app-land-property-details',
  templateUrl: './land-property-details.component.html',
  styleUrls: ['./land-property-details.component.css']
})
export class LandPropertyDetailsComponent implements OnInit {

  @Input() landForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false
  };
  dateFormat: string;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  constructor(private _loginService:LoginService) { }

  ngOnInit(): void {
    this.dateFormat = this._loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.dateFormat = this.dateFormat;
    this.addControls();
  }

  addControls() {
    Object.keys(LandControls).forEach((controlName: LandControls) => {
      this.landForm.addControl(controlName, new FormControl(''));
    });
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field];
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  onDateChange(type: string, event: any): void {
    this.getSelectedDate( type, event );
  }

  getSelectedDate(type, value) {
    this.onChangeSetAsDirty(type);
    const date = new Date().toISOString();
    const i = this.dataArray.findIndex(x => x.Field === type);
    if (i !== -1) {
      this.dataArray.splice(i, 1);
    }
    const PreviousValue = this.propertyCopy[type]?.singleDate?.formatted;
    const CurrentValue = !!value.singleDate?.date ? value?.singleDate?.date?.year + '-' + value?.singleDate?.date?.month + '-' + value?.singleDate?.date?.day : '';
    if (this.landForm['controls'][type].dirty) {
      this.dataArray.push({
        'Field': type, 'CurrentValue': CurrentValue, 'PreviousValue':
          PreviousValue, 'LoginEntityID': this._loginService?.UserInfo?.EntityID, 'DateTime': date
      });
    }
  }

  onChangeSetAsDirty(control) {
    const data = this.landForm['controls'];
    Object.keys(data).map(i => {
      if (i === control) {
        data[i].markAsDirty();
      }
    });
  }

  onValueChange(type, event, valueName) {
    let value = null;
    if (!!event) {
      value = event[valueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[type];
    const dropdownData = this.getDropdownFromLookup(type);

    const previousData = getPreviousData(type, id, dropdownData)
    buildChangeLog(type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
    
  }

}
