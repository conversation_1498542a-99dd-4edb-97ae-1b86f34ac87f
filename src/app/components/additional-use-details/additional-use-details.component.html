<div class="form-group">
  <div class="col-lg-12 page_sub_head1">
    <b>Additional Uses</b>
  </div>
</div>
<div class="form-group" style="overflow-x:auto; overflow: visible">
  <table class="table table-bordered table-striped table-sm">
    <thead>
      <tr>
        <th style="min-width: 100px">Section</th>
        <th style="min-width: 120px">Property Use</th>
        <th style="min-width: 120px">Specific Use</th>
        <th style="min-width: 50px">Min Floor</th>
        <th style="min-width: 50px">Max Floor</th>
        <th style="min-width: 120px">Floors</th>
        <th style="min-width: 120px">Floor Size</th>
      </tr>
    </thead>
    <tbody *ngFor="let additionalUse of additionalUseList">
      <tr *ngIf="additionalUse.IsActive==1">
        <td>{{additionalUse.Section}}</td>
        <td>{{additionalUse.UseTypeName}}</td>
         <td>
          <ng-select [items]="additionalSpecificUseList[additionalUse.PropertyAdditionalUseID]" [virtualScroll]="true" bindLabel="SpecificUsesName"
            bindValue="SpecificUsesID" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="additionalUse.SpecificUsesID"
            (change)="onChangeAdditionalSpecificUse(additionalUse)">
          </ng-select>
         </td>
        <td>{{additionalUse.MinFloor}}</td>
        <td>{{additionalUse.MaxFloor}}</td>
        <td>{{additionalUse.Floors}}</td>
        <td>{{additionalUse.floorSize}}</td>
      </tr>
    </tbody>
    <tbody *ngFor="let childFreeHoldsAdditionalUse of rollupMasterFreeholdFieldsObject?.childFreeHoldsAdditionalUses">
      <tr *ngIf="propertyLocation.Condo ===  EnumCondoTypeNames.Master_Freehold">
        <td>{{childFreeHoldsAdditionalUse.Section}}</td>
        <td>{{childFreeHoldsAdditionalUse.UseTypeName}}</td>
        <td>{{childFreeHoldsAdditionalUse.SpecificUsesName}}</td>
        <td>{{childFreeHoldsAdditionalUse.MinFloor}}</td>
        <td>{{childFreeHoldsAdditionalUse.MaxFloor}}</td>
        <td>{{childFreeHoldsAdditionalUse.Floors}}</td>
        <td>{{childFreeHoldsAdditionalUse.FloorSizeSM}}</td>
      </tr>
    </tbody>
  </table>
</div>
