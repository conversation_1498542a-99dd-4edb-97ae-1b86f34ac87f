import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AdditionalUseDetailsComponent } from './additional-use-details.component';

describe('AdditionalUseDetailsComponent', () => {
  let component: AdditionalUseDetailsComponent;
  let fixture: ComponentFixture<AdditionalUseDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AdditionalUseDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AdditionalUseDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
