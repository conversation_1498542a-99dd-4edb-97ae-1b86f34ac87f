import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { UseTypes } from '../../enumerations/useTypes';
import { Property } from '../../models/Property';

@Component({
  selector: 'app-property-details',
  templateUrl: './property-details.component.html',
  styleUrls: ['./property-details.component.css']
})
export class PropertyDetailsComponent implements OnInit {
  @Input() propertyForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  propertyUseTypes = UseTypes

  constructor() { }

  ngOnInit(): void {
  }

}
