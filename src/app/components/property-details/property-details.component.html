<div *ngIf="property.UseTypeID === propertyUseTypes.Office || property.UseTypeID == propertyUseTypes.Apartments || property.UseTypeID == propertyUseTypes.SpecialUse">
  <app-office-property-details
    [officeForm]="propertyForm.get('OfficeForm')"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="selectedOption"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-office-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
  <app-industrial-property-details
    [industrialForm]="propertyForm.get('IndustrialForm')"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="selectedOption"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-industrial-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Retail">
  <app-retail-property-details
    [retailForm]="propertyForm.get('RetailForm')"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="selectedOption"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-retail-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Land">
  <app-land-property-details
    [landForm]="propertyForm.get('LandForm')"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="selectedOption"
    [condo]="condo"
  ></app-land-property-details>
</div>
