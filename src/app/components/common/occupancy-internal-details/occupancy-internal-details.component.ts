import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IAngularMyDpOptions } from 'angular-mydatepicker';
import { LoginService } from '../../../services/login.service';
import { Property } from '../../../models/Property';
import { YesOrNoList, YesNoFields, BindNamesWithLookupName } from '../../../common/constants';
import { buildChangeLog, getPreviousData, updateValidation, validateIntegerInput, validatePasteInput } from '../../../utils';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { EnumCondoTypeName } from '../../../enumerations/condoType';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { CommonStrings } from '../../../constants';

@Component({
  selector: 'app-occupancy-internal-details',
  templateUrl: './occupancy-internal-details.component.html',
  styleUrls: ['./occupancy-internal-details.component.css']
})
export class OccupancyInternalDetailsComponent implements OnInit {
  EnumCondoTypeNames = EnumCondoTypeName;
  @Input() useTypeForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  buildSuitSpecList = [];
  hasConstructionDateError = false;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false
  };
  dateFormat: string;
  constructionDateErrorMsg = CommonStrings?.ErrorMessages?.ConstructionStartDateError;
   validateIntegerInput = validateIntegerInput;
    validatePasteInput = validatePasteInput;

  constructor(private _loginService:LoginService,  public yesOrNoService: YesOrNoService) { }

  ngOnInit(): void {
    this.dateFormat = this._loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.dateFormat = this.dateFormat;

    this.useTypeForm?.get(OfficeControls?.HVAC)?.valueChanges?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.HVACTypeID, 'HVACTypeID', OfficeControls,this.property,this.useTypeForm);
      }
    );

    this.useTypeForm?.get(OfficeControls?.ConstructionStartDate)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
    this.useTypeForm?.get(OfficeControls?.EstCompletionDate)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
    this.useTypeForm?.get(OfficeControls?.ActualCompletion)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  onDateChange(type: string, event: any): void {
    this.getSelectedDate( type, event );
  }

  getSelectedDate(type, value) {
    this.onChangeSetAsDirty(type);
    const date = new Date().toISOString();
    const i = this.dataArray.findIndex(x => x.Field === type);
    if (i !== -1) {
      this.dataArray.splice(i, 1);
    }
    const PreviousValue = this.propertyCopy[type]?.singleDate?.formatted;
    const CurrentValue = !!value.singleDate?.date ? value?.singleDate?.date?.year + '-' + value?.singleDate?.date?.month + '-' + value?.singleDate?.date?.day : '';
    if (this.useTypeForm['controls'][type].dirty) {
      this.dataArray.push({
        'Field': type, 'CurrentValue': CurrentValue, 'PreviousValue':
          PreviousValue, 'LoginEntityID': this._loginService?.UserInfo?.EntityID, 'DateTime': date
      });
    }
  }

  onChangeSetAsDirty(control) {
    const data = this.useTypeForm['controls'];
    Object.keys(data).map(i => {
      if (i === control) {
        data[i].markAsDirty();
      }
    });
  }

  onValueChange(type, event, valueName) {
    let value = null;
    if (!!event) {
      value = event[valueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[type];
    const dropdownData = this.getDropdownFromLookup(type);

    const previousData = getPreviousData(type, id, dropdownData)
    buildChangeLog(type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
  }

  checkboxChange(event, field) {
    const value = event.target.checked ? 1 : 0;
    if (field == OfficeControls.IsOwnerOccupied) {
      this.property.IsOwnerOccupied = value;
    } else if (field === OfficeControls.IsADAAccessible) {
      this.property.IsADAAccessible = value;
    } else if (field === OfficeControls.IncludeinAnalytics) {
      this.property.IncludeinAnalytics = value;
    } else if (field === OfficeControls.HasSolar) {
      this.property.HasSolar = value;
    }

  }

  checkForConstructionDateValidation() {
    this.hasConstructionDateError = false;

    const constructionStartRaw = this.useTypeForm.get(OfficeControls?.ConstructionStartDate)?.value;
    const estCompletionRaw = this.useTypeForm.get(OfficeControls?.EstCompletionDate)?.value;
    const actualCompletionRaw = this.useTypeForm.get(OfficeControls?.ActualCompletion)?.value;

    // Extract jsDate and convert to Date object
    const constructionStart = constructionStartRaw?.singleDate?.jsDate ? new Date(constructionStartRaw.singleDate.jsDate) : null;
    const estCompletion = estCompletionRaw?.singleDate?.jsDate ? new Date(estCompletionRaw.singleDate.jsDate) : null;
    const actualCompletion = actualCompletionRaw?.singleDate?.jsDate ? new Date(actualCompletionRaw.singleDate.jsDate) : null;

    if (constructionStart) {
      const isEstCompletionValid = estCompletion ? estCompletion > constructionStart : true;
      const isActualCompletionValid = actualCompletion ? actualCompletion > constructionStart : true;
      this.hasConstructionDateError = !(isEstCompletionValid && isActualCompletionValid);
    }

    // Set the validity of the ConstructionStartDate control
    const constructionStartControl = this.useTypeForm?.get(OfficeControls?.ConstructionStartDate);
    if (this.hasConstructionDateError) {
      constructionStartControl?.setErrors({ invalidDateRange: true }); // Add an error
    } else {
      constructionStartControl?.setErrors(null); // Clear errors
    }
  }

}
