import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { OccupancyInternalDetailsComponent } from './occupancy-internal-details.component';

describe('OccupancyInternalDetailsComponent', () => {
  let component: OccupancyInternalDetailsComponent;
  let fixture: ComponentFixture<OccupancyInternalDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ OccupancyInternalDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OccupancyInternalDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
