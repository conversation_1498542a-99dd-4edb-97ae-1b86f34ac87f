import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';

import { PropertyService } from '../../../services/api-property.service';
import { CommunicationModel, CommunicationService } from '../../../services/communication.service';
import { SharedDataService } from '../../../services/shareddata.service';
import { LoginService } from '../../../services/login.service';

import { Property } from '../../../models/Property';
import { PropertyParcel } from '../../../models/PropertyParcel';
import { mapEditPropertyDTO } from '../../../DTO/mapEditPropertyDTO';

@Component({
  selector: 'app-parcel-list',
  templateUrl: './parcel-list.component.html',
  styleUrls: ['./parcel-list.component.css']
})
export class ParcelListComponent implements OnInit, OnDestroy {
  @Input() isAnExistingProperty: boolean;
  @Input() property: Property;
  @Input() propertyCopy: Property;
  @Input() initialDetails: mapEditPropertyDTO;

  countryId = this.loginService.UserInfo.CountryId;
  entityId = this.loginService.UserInfo.EntityID;

  parcelTabClick = false;
  showParcelDetails = false;
  propertyParcelList: PropertyParcel[] = [];
  selectedParcel: PropertyParcel;
  shouldUpdateLotSize = false;

  private fetchParcelsSubscription: Subscription;

  constructor(
    private sharedDataService: SharedDataService,
    private propertyService: PropertyService,
    private communicationService: CommunicationService,
    private loginService: LoginService
  ) {}

  ngOnInit(): void {
    this.fetchParcelsSubscription = this.communicationService
      .subscribe('fetchParcels')
      .subscribe(({ data }) => {
        if (data) {
          this.shouldUpdateLotSize = data.shouldUpdate;
          this.fetchParcels();
        }
      });
  }

  ngOnDestroy(): void {
    this.fetchParcelsSubscription?.unsubscribe();
  }

  showPropertyParcel(): void {
    this.parcelTabClick = true;
    this.getParcelDetails();
  }

  getParcelDetails(forceFetch = false): void {
    const cachedParcels = this.sharedDataService.selectedPropertyParcel;
    const isMatchingProperty = cachedParcels?.length > 0 && cachedParcels[0].PropertyID === this.property.PropertyID;

    if (!forceFetch && isMatchingProperty) {
      this.propertyParcelList = cachedParcels;
      this.broadcastParcelSizeAndCount();
    } else {
      this.fetchParcels();
    }
  }

  fetchParcels(): void {
    this.propertyService.GetPropertyParcelDetails(this.property.PropertyID).subscribe(result => {
      const parcels = result.body?.responseData;

      if (parcels?.length) {
        this.propertyParcelList = parcels.map(parcel => ({
          ...parcel,
          PropertyID: this.property.PropertyID
        }));

        this.sharedDataService.selectedPropertyParcel = this.propertyParcelList;

        this.property.ParcelNumber = this.propertyParcelList[0].ParcelNo;

        const totalSize = this.getTotalParcelSize();

        if (this.shouldUpdateLotSize) {
          this.propertyCopy.LotSizeSF = this.property.LotSizeSF;
          this.property.LotSizeSM = this.property.LotSizeSF = totalSize;
          this.broadcastPropertySave(totalSize);
        }

        this.broadcastParcelSizeAndCount();
      }
    });
  }

  getTotalParcelSize(): number {
    return Number(this.propertyParcelList?.reduce((acc, parcel) => acc + (parseFloat(parcel.ParcelSizeSM as any) || 0), 0)?.toFixed(2));
  }

  broadcastParcelSizeAndCount(): void {
    const size = this.getTotalParcelSize();
    const count = this.propertyParcelList.length;

    const message = new CommunicationModel();
    message.Key = 'updateParcelSizeAndCount';
    message.data = { size, count };

    this.communicationService.broadcast(message);
  }

  broadcastPropertySave(size: number): void {
    const message = new CommunicationModel();
    message.Key = 'propertySaveOnParcelChange';
    message.data = { size };

    this.communicationService.broadcast(message);
  }

  addParcelDetails(): void {
    this.selectedParcel = new PropertyParcel();
    this.selectedParcel.PropertyID = this.property.PropertyID;

    this.showParcelDetails = true;
  }

  updateParcelDetails(parcel: PropertyParcel): void {
    this.selectedParcel = JSON.parse(JSON.stringify(parcel));
    this.selectedParcel.PropertyID = this.property.PropertyID
    this.showParcelDetails = true;
  }

  closeParcelModal(): void {
    this.showParcelDetails = false;
    this.shouldUpdateLotSize = true;
    this.getParcelDetails(true);
  }

  selectParcels(): void {
    const parcelNos = this.propertyParcelList.map(parcel => parcel.ParcelNo);

    const message = new CommunicationModel();
    message.Key = 'selectParcels';
    message.data = { ...this.initialDetails, parcelNos };

    this.communicationService.broadcast(message);
  }
}
