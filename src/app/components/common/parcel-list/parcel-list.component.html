<!-- Parcel Section -->
<div class="row expandBox" *ngIf="isAnExistingProperty">
  <div class="col-md-12 p-1 position-relative">

    <!-- Toggle Parcel Info -->
    <button
      type="button"
      class="searchBtnActions selectMoreToggle"
      data-toggle="collapse"
      data-target="#ParcelInformation"
      aria-expanded="false"
      (click)="showPropertyParcel()">
      <i class="fab fa-product-hunt"></i>
      Parcel Information
    </button>

    <!-- Action Buttons -->
    <input
      type="button"
      class="btn btn-primary right checkAbslt"
      style="right: 175px;"
      value="Select Parcel"
      (click)="selectParcels()" />

    <input
      type="button"
      class="btn btn-primary right checkAbslt"
      style="right: 55px;"
      value="Enter Parcel"
      (click)="addParcelDetails()" />
  </div>

  <!-- Parcel Information Table -->
  <div id="ParcelInformation" class="collapse mt-2 mb-2 px-2">
    <hr class="mt-0" />
    <div class="form-group row">
      <div class="col-md-12">
        <table class="table table-bordered table-striped table-sm w-100 mb-0">
          <thead>
            <tr>
              <th>Parcel No</th>
              <th>Parcel Size</th>
              <th>Lot</th>
              <th>Block</th>
              <th>Subdivision</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let parcel of propertyParcelList">
              <td>{{ parcel.ParcelNo }}</td>
              <td>{{ parcel.ParcelSizeSM }}</td>
              <td>{{ parcel.Lot }}</td>
              <td>{{ parcel.Block }}</td>
              <td>{{ parcel.SubDivision }}</td>
              <td>
                <img
                  src="assets/images/edit.gif"
                  alt="Edit"
                  (click)="updateParcelDetails(parcel)"
                  style="cursor: pointer;" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Parcel Details Modal -->
<ng-container *ngIf="showParcelDetails">
  <imperium-modal
    [(visible)]="showParcelDetails"
    [title]="'Parcel Details'"
    [size]="tenantModalSize"
    [bodyTemplate]="bodyTemplate"
    [width]="'medium'" [height]="'medium'">
  </imperium-modal>

  <ng-template #bodyTemplate>
    <app-property-parcel-modal
      [CountryId]="countryId"
      [propertyParcel]="selectedParcel"
      [EntityID]="entityId"
      (onClose)="closeParcelModal()"
      [latLng]="{Latitude: initialDetails.latLng.Latitude, Longitude: initialDetails.latLng.Longitude}">
    </app-property-parcel-modal>
  </ng-template>
</ng-container>
