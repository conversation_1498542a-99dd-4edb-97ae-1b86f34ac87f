import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { LoginService } from '../../../services/login.service';
import { Property } from '../../../models/Property';
import  { UseTypes } from '../../../enumerations/useTypes';
import { ResearchType } from '../../../enumerations/researchType';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { IndustrialMultiSelectFields, IndustrialYesNoFields, YesOrNoList, BindNamesWithLookupName } from '../../../common/constants';
import { buildChangeLog, getPreviousData, isValidWebsite, stringToArray } from '../../../utils';
import { EnumCondoTypeName } from '../../../enumerations/condoType'
import { YesOrNoService} from '../../../services/yes-or-no.service';

@Component({
  selector: 'app-other-building-details',
  templateUrl: './other-building-details.component.html',
  styleUrls: ['./other-building-details.component.css']
})
export class OtherBuildingDetailsComponent implements OnInit {

  @Input() useTypeForm: FormGroup;
  @Input() property: Property;
  @Input() dataArray: any[];
  @Input() propertyCopy: Property;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: any;
  features: any = [];
  featuresCopy: any = [];
  propertyUseTypes = UseTypes;
  EnumCondoTypeNames = EnumCondoTypeName;
  isBuildingWebsiteValueValid = true;
  
  constructor(private _loginService:LoginService,  public yesOrNoService: YesOrNoService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.property || changes.condo) {
      this.toggleFeatureBasedOnCondo();
    }
    if (changes.property) {
      this.updateFeatures(changes.property?.currentValue);
    }
  }

  updateFeatures(property) {
    this.features = stringToArray(property.FeatureIDs)
    this.featuresCopy = JSON.parse(JSON.stringify(this.features));
    this.property.Features = this.features && this.features.join(',');
  }

  toggleFeatureBasedOnCondo(){
    if(this.condo === this.EnumCondoTypeNames.Master_Freehold) {
      this.useTypeForm.controls[IndustrialControls.Features]?.disable();
    } else {
      this.useTypeForm.controls[IndustrialControls.Features]?.enable();
    }
  }


  ngOnInit(): void {
    this.updateFeatures(this.property)
    this.useTypeForm.get(IndustrialControls.HasSprinkler).valueChanges.subscribe(
      (HasSprinkler) => {
        this.updateHasSprinklerValidations(HasSprinkler);
      }
    );

    this.useTypeForm.get(IndustrialControls.Lifts).valueChanges.subscribe(
      (Lifts) => {
        this.updateLiftsValidations(Lifts);
      }
    );

    this.useTypeForm?.get(IndustrialControls.BuildingWebsite)?.valueChanges?.subscribe(
      (value) => {
         this.isBuildingWebsiteValueValid = isValidWebsite(value);
      }
    );
  }

  updateHasSprinklerValidations(HasSprinkler) {
    if (HasSprinkler !== 1) {
      this.useTypeForm.controls[IndustrialControls.SprinklerTypeID].disable();
      this.useTypeForm.get(IndustrialControls.SprinklerTypeID).clearValidators();
      this.property.SprinklerTypeID = null;
    } else {
      this.useTypeForm.controls[IndustrialControls.SprinklerTypeID].enable();
      if (this.propertyStatus && this.propertyStatus !== ResearchType.Hidden) {
        this.useTypeForm.get(IndustrialControls.SprinklerTypeID).setValidators([Validators.required]);
      }
    }
    this.useTypeForm.get(IndustrialControls.SprinklerTypeID).updateValueAndValidity();
  }

  updateLiftsValidations(Lifts) {
    if (Lifts !== 1) {
      this.useTypeForm.controls[IndustrialControls.LiftsCount].disable();
      this.useTypeForm.get(IndustrialControls.LiftsCount).clearValidators();
      this.property.LiftsCount = null;
    } else {
      this.useTypeForm.controls[IndustrialControls.LiftsCount].enable();
    }
    this.useTypeForm.get(IndustrialControls.LiftsCount).updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (IndustrialYesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }
  
  onValueChange(type, event, valueName) {
    let value = null;
    if (!!event) {
      value = event[valueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[type];
    let previousData;
    const dropdownData = this.getDropdownFromLookup(type);
    if (dropdownData) {
      if (type === IndustrialMultiSelectFields?.Features) {
        this.property.Features = this.features.join(',');
        let currValue = '', prevValue = '';
        if (!!this.features && this.features.length > 0) {
          this.features.forEach(element => {
            currValue = currValue + dropdownData.filter(x => x.FeatureID === element)[0].FeatureName + ',';
          });
        }
        if (!!this.featuresCopy && this.featuresCopy.length > 0) {
          this.featuresCopy.forEach(element => {
            prevValue = prevValue + dropdownData.filter(x => x.FeatureID === element)[0].FeatureName + ',';
          });
        }
        if (currValue.length !== 0) {
          currValue = currValue.substring(0, currValue.length - 1);
        }
        if (prevValue.length !== 0) {
          prevValue = prevValue.substring(0, prevValue.length - 1);
        }
        value = currValue;
        previousData = prevValue;
      } else {
        previousData = getPreviousData(type, id, dropdownData)
      }
    }
    buildChangeLog(type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
  }
}
