import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PropertyAllocationDetailsComponent } from './property-allocation-details.component';

describe('PropertyAllocationDetailsComponent', () => {
  let component: PropertyAllocationDetailsComponent;
  let fixture: ComponentFixture<PropertyAllocationDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PropertyAllocationDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PropertyAllocationDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
