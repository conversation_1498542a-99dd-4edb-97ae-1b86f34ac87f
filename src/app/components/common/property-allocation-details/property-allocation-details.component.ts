import { Component, Input, OnInit } from '@angular/core';
import { LoginService } from '../../../services/login.service';
import { EnumApplication } from '../../../enumerations/application';
import { PropertyService } from '../../../services/api-property.service';
import { CommunicationModel, CommunicationService } from '../../../services/communication.service';
import { PropertyAllocation } from '../../../models/propertyAllocation';

@Component({
  selector: 'app-property-allocation-details',
  templateUrl: './property-allocation-details.component.html',
  styleUrls: ['./property-allocation-details.component.css']
})
export class PropertyAllocationDetailsComponent implements OnInit {

  @Input() UnitId: number;
  @Input() metricUnit: number;
  @Input() UnitDisplayTextSize: any;
  propertyAllocationList: PropertyAllocation[] = [];
  @Input() propertyTypes: any;
  private _propertyService: PropertyService;
  private _communicationService: CommunicationService;
  constructor(
    private _loginService:LoginService,
    communicationService: CommunicationService,
    propertyService: PropertyService
  ) {
    this._propertyService = propertyService;
    this._communicationService = communicationService;
  }

  ngOnInit(): void {}

  updateAllocationsData(multifloors, propertyId) {
    const totalBuildingSqm = multifloors.reduce((acc, floor) => Number(floor.floorSize) * Number(floor.floorCount) + acc, 0);
    let propertyAllocations = [ ...this.propertyAllocationList ];
    const footprintsAllocated = [];
    
    multifloors.forEach(floor => {
      const isAllocationPresent = propertyAllocations.some(alloc => floor.BuildingFootPrintID ? alloc.SectionID == floor.BuildingFootPrintID : alloc.SectionID == floor.localBuildingFootPrintID);
      //Create new allocation if not present
      if(!isAllocationPresent) {
        const propertyUse = this.propertyTypes.find((useType) => useType.UseTypeID === floor.specificUse)?.UseTypeName;
          const allocation = {
            ...new PropertyAllocation(),
            PropertyAllocationID: -1,
            PropertyID: propertyId,
            UseTypeID: floor.specificUse,
            SpecificUsesID: null,
            UseTypeName: propertyUse,
            Floors: floor.floorCount?.toString(),
            FloorSizeSM: Number(floor.floorSize),
            Notes: floor.description,
            IsDefault: 1,
            IsActive: 1,
            LoginEntityID: this._loginService.UserInfo.EntityID,
            ChangeLogJSON: null,
            ApplicationID: EnumApplication.VST,
            MinFloorNumber: floor.minFloor,
            MaxFloorNumber: floor.maxFloor,
            LocalAllocationId: floor.BuildingFootPrintID ? null : floor.localBuildingFootPrintID,
            SectionID: floor.BuildingFootPrintID ? floor.BuildingFootPrintID : floor.localBuildingFootPrintID,
            allocationPercentage: (Number(floor.floorSize) * Number(floor.floorCount)) / totalBuildingSqm * 100,
          };
          footprintsAllocated.push(floor.BuildingFootPrintID);
          propertyAllocations.push(allocation);
      } else { //Edit the existing allocation
        propertyAllocations.map(alloc => {
          if (floor.BuildingFootPrintID ? alloc.SectionID == floor.BuildingFootPrintID : alloc.SectionID == floor.localBuildingFootPrintID) {
            const propertyUse = this.propertyTypes.find((useType) => useType.UseTypeID === floor.specificUse)?.UseTypeName;
            alloc.UseTypeID = floor.specificUse;
            alloc.UseTypeName = propertyUse;
            alloc.Floors = floor.floorCount?.toString();
            alloc.FloorSizeSM = Number(floor.floorSize),
            alloc.Notes = floor.description,
            alloc.IsDefault = 1;
            alloc.IsActive = 1;
            alloc.MinFloorNumber = floor.minFloor;
            alloc.MaxFloorNumber = floor.maxFloor;
            alloc.allocationPercentage = (Number(floor.floorSize) * Number(floor.floorCount)) / totalBuildingSqm * 100;
          }
          return alloc;
        });
      }
    });

    //Remove the allocations if the corresponding polygon is not present.
    propertyAllocations.map(alloc => {
      const isValidAllocation = multifloors.some(floor => floor.BuildingFootPrintID ? floor.BuildingFootPrintID == alloc.SectionID : floor.localBuildingFootPrintID == alloc.SectionID);
      //Remove duplicate allocations
      if (!isValidAllocation || footprintsAllocated.includes(alloc.SectionID)) {
        alloc.IsActive = 0;
      } else {
        footprintsAllocated.push(alloc.SectionID);
      }
      return alloc;
    });

    //Sorting Allocations
    propertyAllocations = propertyAllocations.sort((a, b) => (a.IsDefault < b.IsDefault) ? 1 : -1);
    //Update the allocations data in edit property component
    this.propertyAllocationList = JSON.parse(JSON.stringify(propertyAllocations));
    let commModel = new CommunicationModel();
    commModel.Key = 'updatePropertyAlocations';
    commModel.data = { allocations: propertyAllocations, isInit: false };
    this._communicationService.broadcast(commModel);
  }

  getPropertyAllocationsAndUses(propertyId) {
    let propertyAllocations = [];
    if (!!propertyId) {
      const response_allocations = this._propertyService.GetPropertyAllocation(propertyId);
      response_allocations.subscribe(result => {
        const data = result.body.responseData;
        if (data && data.length > 0) {
          propertyAllocations = data;
          const totalBuildingSqm = propertyAllocations.reduce((acc, alloc) => Number(alloc.Floors) * Number(alloc.FloorSizeSM) + acc, 0);
          propertyAllocations.map(alloc => {
            alloc.allocationPercentage = (Number(alloc.FloorSizeSM) * Number(alloc.Floors)) / totalBuildingSqm * 100;
            return alloc;
          })
          //Sorting Allocations
          propertyAllocations = propertyAllocations.sort((a, b) => (a.IsDefault < b.IsDefault) ? 1 : -1);
        }
        //Update the allocations data in edit property component
        this.propertyAllocationList = JSON.parse(JSON.stringify(propertyAllocations));
        let commModel = new CommunicationModel();
        commModel.Key = 'updatePropertyAlocations';
        commModel.data = { allocations: propertyAllocations, isInit: true };
        this._communicationService.broadcast(commModel);
      });
    }
  }
}
