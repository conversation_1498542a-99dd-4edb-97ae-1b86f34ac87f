<div [formGroup]="useTypeForm">
    <div class="row">
        <div class="col-md-6">
            <div class="row label-value-wrapper" *ngIf="property.UseTypeID != propertyUseTypes.Retail">
                <div *ngIf="property.UseTypeID == propertyUseTypes.Industrial" class="col-md-5 label" for="text-input">Building Grade</div>
                <div *ngIf="property.UseTypeID != propertyUseTypes.Industrial" class="col-md-5 label" for="text-input">Building Class</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="BuildingClass" [items]="getDropdownFromLookup('BuildingClass')" [virtualScroll]="true"
                        bindLabel="ClassTypeName" bindValue="BuildingClassID" placeholder="--Select--"
                        [(ngModel)]="property.BuildingClass"
                        (change)="selectedValue('BuildingClass',$event,'ClassTypeName')"></ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.ClassTypeName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.ClassTypeName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Specific Use</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="SpecificUse" [items]="getDropdownFromLookup('SpecificUseID')" [virtualScroll]="true"
                        bindLabel="SpecificUsesName" bindValue="SpecificUsesID" placeholder="--Select--"
                        [(ngModel)]="property.SpecificUseID"
                        (change)="selectedValue('SpecificUseID',$event,'SpecificUsesName')"></ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.SpecificUseName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" readonly [value]="rollupMasterFreeholdFieldsObject?.SpecificUseName" type="text" class="form-control">
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Building Size
                    (GBA)
                </div>
                <div class="col-md-7">
                <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputBuildingSF">
                    <input type="text" maxlength="11" numericOnly allowNegative="false" allowDecimal="true" (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)"
                        class="form-control maxnumbervalidation" formControlName="BuildingSF" [(ngModel)]="property.BuildingSF" (blur)="validateSizeFieldsAgainstBuildingSize()"
                        [readonly]="condo === EnumCondoTypeNames.Master ||condo === EnumCondoTypeNames.Master_Freehold"
                        >
                </ng-container>
                <ng-template #masterRollupInputBuildingSF>
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.BuildingSF}}"
                        class="form-control maxnumbervalidation"  [value]="rollupMasterFreeholdFieldsObject?.BuildingSF"
                        [readonly]="condo === EnumCondoTypeNames.Master ||condo === EnumCondoTypeNames.Master_Freehold">
                </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="condo != EnumCondoTypeNames.Master && condo !==EnumCondoTypeNames.Master_Freehold">
                <div class="col-md-5 label">Minimum Floor Size</div>
                <div class="col-md-7">
                  <input type="text" numericOnly allowDecimal="true" allowNegative="false" formControlName="SmallestFloor" [(ngModel)]="property.SmallestFloor" class="form-control"
                  [ngClass]="{'error-field': !useTypeForm.controls['SmallestFloor']?.valid}" (paste)="validatePasteInput($event, true)"
                  (keypress)="validateIntegerInput($event, true)">
                  <div class="prop-validator error" *ngIf="useTypeForm.controls['SmallestFloor']?.errors?.errMsg">{{useTypeForm.controls['SmallestFloor']?.errors?.errMsg}}</div>
                    <div *ngIf="useTypeForm.controls['SmallestFloor']?.invalid && (useTypeForm.controls['SmallestFloor'].dirty || useTypeForm.controls['SmallestFloor'].touched)">
                      <div *ngIf="useTypeForm.controls['SmallestFloor'].errors?.numeric" class="error-message">Please enter a valid number.</div>
                    </div>
                </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo === EnumCondoTypeNames.Master_Freehold">
                <div class="col-md-5 label">Minimum Floor Size</div>
                <div class="col-md-7">
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.SmallestFloor}}" [value]="rollupMasterFreeholdFieldsObject?.SmallestFloor" class="form-control" readonly>
                </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo != EnumCondoTypeNames.Master && condo !==EnumCondoTypeNames.Master_Freehold">
            <div class="col-md-5 label">Maximum Floor Size</div>
            <div class="col-md-7">
                <input type="text" numericOnly allowDecimal="true" allowNegative="false" formControlName="LargestFloor" [(ngModel)]="property.LargestFloor" class="form-control"
                [ngClass]="{'error-field': !useTypeForm.controls['LargestFloor']?.valid}" (paste)="validatePasteInput($event, true)"
                (keypress)="validateIntegerInput($event, true)">
                <div class="prop-validator error" *ngIf="useTypeForm.controls['LargestFloor']?.errors?.errMsg">{{useTypeForm.controls['LargestFloor']?.errors?.errMsg}}</div>
                <div *ngIf="useTypeForm.controls['LargestFloor']?.invalid && (useTypeForm.controls['LargestFloor'].dirty || useTypeForm.controls['LargestFloor'].touched)">
                    <div *ngIf="useTypeForm.controls['LargestFloor'].errors?.numeric" class="error-message">Please enter a valid number.</div>
                </div>
            </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo === EnumCondoTypeNames.Master_Freehold">
                <div class="col-md-5 label">Maximum Floor Size</div>
                <div class="col-md-7">
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.LargestFloor}}" [value]="rollupMasterFreeholdFieldsObject?.LargestFloor" class="form-control" readonly>
                </div>
            </div>
            <app-contributed-fields
                [condo]="condo"
                [useTypeForm]="useTypeForm"
                [property]="property"
                [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
                [lookupDropdowns]="lookupDropdowns"
                [propertyCopy]="propertyCopy"
                [dataArray]="dataArray"
            ></app-contributed-fields>
        </div>

        <div class="col-md-6">
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Year Built</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputYearBuilt">
                        <input type="text" integer numericOnly allowNegative="false" allowDecimal="false" maxLength="4" formControlName="YearBuilt"
                            class="form-control" [(ngModel)]="property.YearBuilt" (paste)="validatePasteInput($event)"
                            (keypress)="validateIntegerInput($event)">
                            <div class="prop-validator error" *ngIf="useTypeForm.get('YearBuilt')?.errors?.errMsg">
                                {{ useTypeForm.get('YearBuilt')?.errors?.errMsg }}
                              </div>
                    </ng-container>
                    <ng-template #masterRollupInputYearBuilt>
                        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.YearBuilt}}" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.YearBuilt" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Year Renovated</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputYearRenovated">
                        <input type="text" integer numericOnly allowNegative="false" allowDecimal="false" maxLength="4" formControlName="YearRenovated"
                            class="form-control" [(ngModel)]="property.YearRenovated" (paste)="validatePasteInput($event)"
                            (keypress)="validateIntegerInput($event)">
                    </ng-container>
                    <ng-template #masterRollupInputYearRenovated>
                        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.YearRenovated}}" class="form-control"  [value]="rollupMasterFreeholdFieldsObject?.YearRenovated" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">NABERS Energy</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="EnergyStarRatingID" [items]="getDropdownFromLookup('EnergyStarRatingID')" [virtualScroll]="true"
                        bindLabel="EnergyStarRatingName" bindValue="NABERSCertified" placeholder="--Select--"
                        [(ngModel)]="property.EnergyStarRatingID" name="EnergyStar" id="EnergyStar"
                        (change)="selectedValue('EnergyStarRatingID',$event,'EnergyStarRatingName')"></ng-select>
                    <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.EnergyStarRatingName}}" [value]="rollupMasterFreeholdFieldsObject?.EnergyStarRatingName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">NABERS Water</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="WaterStarRatingID" [items]="getDropdownFromLookup('WaterStarRatingID')" [virtualScroll]="true"
                        bindLabel="WaterStarRatingName" bindValue="NABERSWaterCertified" placeholder="--Select--"
                        [(ngModel)]="property.WaterStarRatingID" name="WaterStar" labelForId="WaterStar"
                        (change)="selectedValue('WaterStarRatingID',$event,'WaterStarRatingName')">
                    </ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.WaterStarRatingName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.WaterStarRatingName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Green Star Rating</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="GreenStarRatingID" [items]="getDropdownFromLookup('GreenStarRatingID')" [virtualScroll]="true"
                        bindLabel="GreenStarRatingName" bindValue="GreenStarRatingID" placeholder="--Select--"
                        [(ngModel)]="property.GreenStarRatingID" name="GreenStar" labelForId="GreenStar"
                        (change)="selectedValue('GreenStarRatingID',$event,'GreenStarRatingName')">
                    </ng-select>
                    <input *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.GreenStarRatingName}}" [value]="rollupMasterFreeholdFieldsObject?.GreenStarRatingName" class="form-control" type="text" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label GresbScore" for="text-input">GRESB Score </div>
                <div class="col-md-7 row">
                    <div class="col-md-6 GresbScoreMin">
                        <div class="form-control-label" for="text-input">Min</div>
                        <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputGRESBScoreMin">
                            <input type="text" numericOnly allowDecimal="true" allowNegative="false" maxlength="11"
                                formControlName="GRESBScoreMin" name="GRESBScoreMin" class="form-control maxnumbervalidation"
                                allowNegative="false" [(ngModel)]="property.GRESBScoreMin"
                                [ngClass]="{'error-field':( !useTypeForm?.controls['GRESBScoreMin']?.valid)}" (paste)="validatePasteInput($event, true)"
                                (keypress)="validateIntegerInput($event, true)">
                                 <div class="prop-validator error GresbScore-msg" *ngIf="useTypeForm.get('GRESBScoreMin')?.errors?.errMsg">
                                    {{ useTypeForm.get('GRESBScoreMin')?.errors?.errMsg }}</div>
                        </ng-container>
                        <ng-template #masterRollupInputGRESBScoreMin>
                            <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.GRESBScoreMin}}" [value]="rollupMasterFreeholdFieldsObject?.GRESBScoreMin" readonly>
                        </ng-template>
                    </div>
                    <div class="col-md-6 GresbScoreMax">
                        <div class="form-control-label " for="text-input">Max</div>
                        <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputGRESBScoreMax">
                            <input type="text" numericOnly allowDecimal="true" allowNegative="false" maxlength="11"
                                formControlName="GRESBScoreMax" name="GRESBScoreMax" class="form-control maxnumbervalidation"
                                allowNegative="false" [(ngModel)]="property.GRESBScoreMax"
                                [ngClass]="{'error-field':( !useTypeForm?.controls['GRESBScoreMax']?.valid)}"
                                (paste)="validatePasteInput($event, true)"
                                (keypress)="validateIntegerInput($event, true)">
                        </ng-container>
                        <ng-template #masterRollupInputGRESBScoreMax>
                            <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.GRESBScoreMax}}" [value]="rollupMasterFreeholdFieldsObject?.GRESBScoreMax" readonly>
                        </ng-template>
                    </div>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Office Mezzanine</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="OfficeMezzanine" [items]="getDropdownFromLookup('Mezzanine')"
                        [virtualScroll]="true" bindLabel="label" bindValue="value" placeholder="--Select--"
                        [(ngModel)]="property.Mezzanine" name="OfficeMezzanine" id="OfficeMezzanine"
                        (change)="selectedValue('Mezzanine',$event,'label')">
                    </ng-select>
                    <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.Mezzanine)" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Office Mezzanine Size</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputMezzanine_Size_SF">
                        <input type="text" maxlength="11" integer numericOnly allowNegative="false" allowDecimal="true"
                        class="form-control maxnumbervalidation" formControlName="Mezzanine_Size_SF"
                        [(ngModel)]="property.Mezzanine_Size_SF"
                        (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)"
                        [ngClass]="{'error-field':(useTypeForm?.controls['Mezzanine_Size_SF']?.enabled && !useTypeForm?.controls['Mezzanine_Size_SF']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputMezzanine_Size_SF>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.Mezzanine_Size_SF}}" [value]="rollupMasterFreeholdFieldsObject?.Mezzanine_Size_SF" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="Awnings" [items]="getDropdownFromLookup('Awnings')" [virtualScroll]="true"
                    bindLabel="label" bindValue="value" placeholder="--Select--" [(ngModel)]="property.Awnings"
                        name="Awnings" id="Awnings" (change)="selectedValue('Awnings',$event,'label')">
                    </ng-select>
                    <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.Awnings)" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings Count</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputAwningsCount">
                        <input type="text" maxlength="11" numericOnly allowNegative="false" allowDecimal="false"
                            (paste)="validatePasteInput($event)"
                            (keypress)="validateIntegerInput($event)"
                            formControlName="AwningsCount" class="form-control maxnumbervalidation" [(ngModel)]="property.AwningsCount"
                            [ngClass]="{'error-field':(useTypeForm?.controls['AwningsCount']?.enabled && !useTypeForm?.controls['AwningsCount']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputAwningsCount>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.AwningsCount}}" [value]="rollupMasterFreeholdFieldsObject?.AwningsCount" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings Size</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputAwnings_Size_SF">
                        <input type="text" maxlength="11" numericOnly allowNegative="false"
                        formControlName="Awnings_Size_SF" class="form-control maxnumbervalidation"
                        [(ngModel)]="property.Awnings_Size_SF"
                        (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)"
                        [ngClass]="{'error-field':(useTypeForm?.controls['Awnings_Size_SF']?.enabled && !useTypeForm?.controls['Awnings_Size_SF']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputAwnings_Size_SF>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.Awnings_Size_SF}}" [value]="rollupMasterFreeholdFieldsObject?.Awnings_Size_SF" readonly>
                    </ng-template>
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label">Contributed Source Comments</div>
                <div class="col-md-7">
                    <textarea rows="2" formControlName="ContributedSourceComments" [(ngModel)]="property.ContributedSourceComments" class="form-control"></textarea>
                </div>
            </div>

        </div>

    </div>
</div>