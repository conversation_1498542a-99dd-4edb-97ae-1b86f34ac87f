import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PropertyUseDetailsComponent } from './property-use-details.component';

describe('PropertyUseDetailsComponent', () => {
  let component: PropertyUseDetailsComponent;
  let fixture: ComponentFixture<PropertyUseDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PropertyUseDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PropertyUseDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
