<div [formGroup]="useTypeForm">
  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Contributed GBA</div>
    <div class="col-md-7">
      <ng-container *ngIf="(condo !== EnumCondoTypeNames.Master_Freehold && condo !== EnumCondoTypeNames.Master); else masterRollupInputContributedGBA_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="ContributedGBA_SF" class="form-control" name="ContributedGBA_SM" [(ngModel)]="property.ContributedGBA_SF" [ngClass]="{'error-field':(useTypeForm?.controls['ContributedGBA_SF']?.enabled && !useTypeForm?.controls['ContributedGBA_SF']?.valid)}"
          (paste)="validatePasteInput($event, true)"
          (keypress)="validateIntegerInput($event, true)">
      </ng-container>
      <ng-template #masterRollupInputContributedGBA_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.ContributedGBA_SF}}" [value]="rollupMasterFreeholdFieldsObject?.ContributedGBA_SF" class="form-control" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Contributed GBA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="(condo !== EnumCondoTypeNames.Master_Freehold && condo !== EnumCondoTypeNames.Master)" formControlName="ContributedGBASource" [virtualScroll]="true" [items]="contributedSources" placeholder="--Select--" [(ngModel)]="property.ContributedGBASizeSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceID" (change)="selectedValue('ContributedGBASizeSourceID',$event,'SizeSourceName');" [ngClass]="{'error-field':(useTypeForm?.controls['ContributedGBASource']?.enabled && !useTypeForm?.controls['ContributedGBASource']?.valid)}">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.ContributedGBASizeSourceName}}" *ngIf="(condo === EnumCondoTypeNames.Master_Freehold || condo === EnumCondoTypeNames.Master)" [value]="rollupMasterFreeholdFieldsObject?.ContributedGBASizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
    <div class="col-md-5 label" for="text-input">Industrial GLA</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputGLA_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="GLA_SF" class="form-control" [(ngModel)]="property.GLA_SF" [ngClass]="{'error-field':(useTypeForm?.controls['GLA_SF']?.enabled && !useTypeForm?.controls['GLA_SF']?.valid)}"
          (paste)="validatePasteInput($event, true)"
          (keypress)="validateIntegerInput($event, true)"
          (blur)="onchangeLettablefields()"
        >
      </ng-container>
      <ng-template #masterRollupInputGLA_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLA_SF}}" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.GLA_SF" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
    <div class="col-md-5 label" for="text-input">Industrial GLA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="IndustrialGLASource" [virtualScroll]="true" bindLabel="SizeSourceName" bindValue="SizeSourceID" placeholder="--Select--" [(ngModel)]="property.GLASizeSourceID" [items]="contributedSources" (change)="selectedValue('GLASizeSourceID',$event,'SizeSourceName');" [ngClass]="{'error-field':(useTypeForm?.controls['IndustrialGLASource']?.enabled && !useTypeForm?.controls['IndustrialGLASource']?.valid)}">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLASizeSourceName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.GLASizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label">Office NLA</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputOfficeSF">
        <input type="text" integer numericOnly allowNegative="false" class="form-control" formControlName="OfficeSF" name="OfficeSF" id="OfficeSF" [(ngModel)]="property.OfficeSF" [ngClass]="{'error-field':(useTypeForm?.controls['OfficeSF']?.enabled && !useTypeForm?.controls['OfficeSF']?.valid)}"
          (paste)="validatePasteInput($event, true)"
          (keypress)="validateIntegerInput($event, true)"
          (blur)="onchangeLettablefields()"
        >
        <div class="prop-validator error" *ngIf="property.OfficeSF && useTypeForm.get('OfficeSF')?.errors?.errMsg">
          {{ useTypeForm.get('OfficeSF')?.errors?.errMsg }}
        </div>
      </ng-container>
      <ng-template #masterRollupInputOfficeSF>
        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.OfficeSF}}" [value]="rollupMasterFreeholdFieldsObject?.OfficeSF" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Office NLA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="OfficeNLASource" [items]="contributedSources" [virtualScroll]="true" placeholder="--Select--" [(ngModel)]="property.NRASizeSourceId" bindLabel="SizeSourceName" bindValue="SizeSourceID" (change)="selectedValue('NRASizeSourceId',$event,'SizeSourceName');" [ngClass]="{'error-field':(useTypeForm?.controls['OfficeNLASource']?.enabled && !useTypeForm?.controls['OfficeNLASource']?.valid)}">
      </ng-select>
      <input type="text" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" title="{{rollupMasterFreeholdFieldsObject?.SizeSourceName}}" [value]="rollupMasterFreeholdFieldsObject?.SizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Retail GLAR</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputGLAR_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="GLAR_SF" class="form-control" [(ngModel)]="property.GLAR_SF" [ngClass]="{'error-field':!useTypeForm?.controls['GLAR_SF']?.valid}"
          (paste)="validatePasteInput($event, true)"
          (keypress)="validateIntegerInput($event, true)"
          (blur)="onchangeLettablefields()"
        >
      </ng-container>
      <ng-template #masterRollupInputGLAR_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLAR_SF}}" [value]="rollupMasterFreeholdFieldsObject?.GLAR_SF" class="form-control" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Retail GLAR Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="RetailGLARSource" [items]="contributedSources" [virtualScroll]="true" placeholder="--Select--" [(ngModel)]="property.GLARSizeSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceID" (change)="selectedValue('GLARSizeSourceID',$event,'SizeSourceName');" [ngClass]="{'error-field':(useTypeForm?.controls['RetailGLARSource']?.enabled && !useTypeForm?.controls['RetailGLARSource']?.valid)}">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLARSizeSourceName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.GLARSizeSourceName" class="form-control" readonly>
    </div>
  </div>
  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Total Lettable</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.Master_Freehold; else masterRollupInputTotalLettableSize_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="TotalLettableSize_SF" class="form-control" [(ngModel)]="property.TotalLettableSize_SF" [ngClass]="{'error-field':(useTypeForm?.controls['TotalLettableSize_SF']?.enabled && !useTypeForm?.controls['TotalLettableSize_SF']?.valid)}"
          (paste)="validatePasteInput($event, true)"
          (keypress)="validateIntegerInput($event, true)"
        >
      </ng-container>
      <ng-template #masterRollupInputTotalLettableSize_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.TotalLettableSize_SF}}" [value]="rollupMasterFreeholdFieldsObject?.TotalLettableSize_SF" class="form-control" readonly>
      </ng-template>
    </div>
  </div>
  <div class="row label-value-wrapper" *ngIf="condo !== EnumCondoTypeNames.Master_Freehold">
    <div class="col-md-5 label" for="text-input">Override Lettable</div>
    <div class="col-md-7">
        <ui-switch *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" [(ngModel)]="property.IsLettableOverrideEnabled" formControlName="IsLettableOverrideEnabled"></ui-switch>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Ttl Lettable Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.Master_Freehold" formControlName="TotalLettableSourceID" [items]="contributedSources" [virtualScroll]="true" placeholder="--Select--" [(ngModel)]="property.TotalLettableSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceID" (change)="selectedValue('TotalLettableSourceID',$event,'SizeSourceName');" [ngClass]="{'error-field':(useTypeForm?.controls['TotalLettableSourceID']?.enabled && !useTypeForm?.controls['TotalLettableSourceID']?.valid)}">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.TotalLettableSourceName}}" *ngIf="condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.TotalLettableSourceName" class="form-control" readonly>
    </div>
  </div>
</div>
