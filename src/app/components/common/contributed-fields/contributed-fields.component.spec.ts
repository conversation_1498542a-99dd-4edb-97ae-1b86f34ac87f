import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ContributedFieldsComponent } from './contributed-fields.component';

describe('ContributedFieldsComponent', () => {
  let component: ContributedFieldsComponent;
  let fixture: ComponentFixture<ContributedFieldsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ContributedFieldsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ContributedFieldsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
