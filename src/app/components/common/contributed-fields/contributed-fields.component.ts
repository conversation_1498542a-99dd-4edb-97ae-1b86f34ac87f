import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { debounceTime } from 'rxjs/operators';

import { buildChangeLog, getPreviousData, updateValidation, validateIntegerInput, validatePasteInput, numericValidator } from '../../../../app/utils';

import { Property } from '../../../../app/models/Property';

import { BindNamesWithLookupName, YesNoFields, YesOrNoList } from '../../../../app/common/constants';

import { EnumCondoTypeName } from '../../../../app/enumerations/condoType';
import { DebounceTimeConfig } from '../../../../app/enumerations/debounce-time';
import { IndustrialControls } from '../../../../app/enumerations/IndustrialControlKeys';
import { OfficeControls } from '../../../../app/enumerations/officeControlKeys';
import { UseTypes } from '../../../../app/enumerations/useTypes';
import { EnumSizeSource } from '../../../../app/enumerations/sizeSource';
import { LoginService } from '../../../../app/services/login.service';


@Component({
  selector: 'app-contributed-fields',
  templateUrl: './contributed-fields.component.html',
  styleUrls: ['./contributed-fields.component.css']
})
export class ContributedFieldsComponent implements OnInit {
  @Input() condo : any;
  @Input() useTypeForm: FormGroup;
  @Input() property: Property;
  @Input() rollupMasterFreeholdFieldsObject: any;
  @Input() lookupDropdowns: any;
  @Input() propertyCopy: Property;
  @Input() dataArray: any[];
  @Input() isContributedGBARequired = false;
  EnumCondoTypeNames = EnumCondoTypeName;
  propertyUseTypes = UseTypes;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  contributedSources: any[];

  constructor(private _loginService: LoginService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['condo'] && this.condo == EnumCondoTypeName.Master) {
      updateValidation(null, OfficeControls?.ContributedGBASource, 'ContributedGBASizeSourceID', OfficeControls, this.property, this.useTypeForm);
    }
  }

  ngOnInit(): void {
    if (this.isContributedGBARequired) {
      this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.setValidators([Validators.required]);
      this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.updateValueAndValidity();
    }
    this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        if (this.condo !== EnumCondoTypeName.Master) {
          updateValidation(value, OfficeControls?.ContributedGBASource, 'ContributedGBASizeSourceID', OfficeControls, this.property, this.useTypeForm);
        }
      }
    );
    this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.OfficeSF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.OfficeNLASource, 'NRASizeSourceId', OfficeControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(OfficeControls?.OfficeSF)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.GLAR_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.RetailGLARSource, 'GLARSizeSourceID', OfficeControls, this.property, this.useTypeForm);
      }
    );

    this.useTypeForm?.get(OfficeControls?.GLAR_SF)?.updateValueAndValidity();

    this.useTypeForm?.get(IndustrialControls?.GLA_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        updateValidation(value, IndustrialControls?.IndustrialGLASource, 'GLASizeSourceID', IndustrialControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(IndustrialControls?.GLA_SF)?.updateValueAndValidity();

    this.useTypeForm?.get(OfficeControls?.IsLettableOverrideEnabled)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.TotalLettableSize_SF, 'TotalLettableSize_SF', OfficeControls, this.property, this.useTypeForm);
        if (value) {
          this.useTypeForm?.get(OfficeControls?.TotalLettableSize_SF).setValidators([Validators.required, numericValidator(), Validators.min(1)]);
          this.useTypeForm?.get(OfficeControls?.TotalLettableSize_SF)?.updateValueAndValidity();
        }
        this.onLettableOverrideChanged(value);
      }
    );
    this.useTypeForm?.get(OfficeControls?.IsLettableOverrideEnabled)?.updateValueAndValidity();

    this.useTypeForm?.get(OfficeControls?.TotalLettableSize_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfig?.FormControlShortDelayInMilliSec))?.subscribe(
      (value) => {
        if (this.useTypeForm?.get(OfficeControls?.TotalLettableSize_SF)?.enabled) {
          updateValidation(value, OfficeControls?.TotalLettableSourceID, 'TotalLettableSourceID', OfficeControls, this.property, this.useTypeForm);
        }
      }
    );
    this.useTypeForm?.get(OfficeControls?.TotalLettableSize_SF)?.updateValueAndValidity();

    const sizeSources = this.lookupDropdowns['SizeSourceID'] || [];
    this.contributedSources = sizeSources.filter(source => source.SizeSourceID != EnumSizeSource.AerialMeasurement);
  }

  onchangeLettablefields() {
    if (!this.property.IsLettableOverrideEnabled) {
      this.updateTotalLettableSize();
    }
  }

  updateTotalLettableSize() {
    if (this.property.UseTypeID != UseTypes.Land ) {
      const glaSize = parseFloat(this.property.GLA_SF ? this.property.GLA_SF : 0);
      const officeSize = parseFloat(this.property.OfficeSF ? this.property.OfficeSF : 0);
      const glarSize = parseFloat(this.property.GLAR_SF ? this.property.GLAR_SF : 0);
      if (this.property.UseTypeID == UseTypes.Industrial) {
        this.property.TotalLettableSize_SF = glaSize + officeSize + glarSize;
      } else {
        this.property.TotalLettableSize_SF = officeSize + glarSize;
      }
      if (this.property.TotalLettableSize_SF) {
        this.property.TotalLettableSize_SF = this.property.TotalLettableSize_SF ? Number(this.property.TotalLettableSize_SF.toFixed(2)) : null;
        this.useTypeForm.get(OfficeControls?.TotalLettableSize_SF)?.setValue(this.property.TotalLettableSize_SF);
        this.useTypeForm.get(OfficeControls?.TotalLettableSize_SF)?.markAsDirty();
      }
    }
  }
  onLettableOverrideChanged(value) {
    if (!value) {
      updateValidation(value, OfficeControls?.TotalLettableSourceID, 'TotalLettableSourceID', OfficeControls, this.property, this.useTypeForm);
      this.updateTotalLettableSize();
    }
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  selectedValue(Type, event, ValueName) {
    let value = null;
    if (!!event) {
      value = event[ValueName];
    } else {
      value = null;
    }
    const id = this.propertyCopy[Type];
    const dropdownData = this.getDropdownFromLookup(Type);

    const previousData = getPreviousData(Type, id, dropdownData)
    buildChangeLog(Type, this.dataArray, value, previousData, this._loginService.UserInfo.EntityID);
  }

}
