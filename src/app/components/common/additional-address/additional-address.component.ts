import { Component, Input, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { PropertyService } from '../../../services/api-property.service';
import { SharedDataService } from '../../../services/shareddata.service';
import { CommunicationService } from '../../../services/communication.service';
import { NotificationService } from '../../../modules/notification/service/notification.service';
import { Property } from '../../../models/Property';
import { PropertyLocation } from '../../../models/PropertyLocation';
import { PropertyAdditionalAddress } from '../../../models/PropertyAdditionalAddress';
import { confirmConfiguration } from '../../../modules/notification/models/confirmConfiguration';
import { CommonStrings } from '../../../constants';

@Component({
  selector: 'app-additional-address',
  templateUrl: './additional-address.component.html',
  styleUrls: ['./additional-address.component.css']
})
export class AdditionalAddressComponent implements OnInit {
  @Input() property: Property;
  @Input() propertyLocation: PropertyLocation

  showAdditionalAddress: boolean;
  additionalAddressList: Array<PropertyAdditionalAddress>;
  editAdditionalAddress: PropertyAdditionalAddress;
  additionalAddrClick = false;

  fetchAdditionalAddressListener: Subscription


  constructor(
    private _propertyService: PropertyService,
    private _sharedDataService: SharedDataService,
    private _notificationService: NotificationService,
    private communicationService: CommunicationService,
  ) {
    this.fetchAdditionalAddressListener = this.communicationService.subscribe('fetchAdditionalAddress').subscribe(result => {
      if (!!result.data ) {
        this.additionalAddressList = new Array<PropertyAdditionalAddress>();
        this.getAdditionalAddress(result.data, true);
      }
    });
   }

  ngOnInit(): void {
    this.additionalAddressList = new Array<PropertyAdditionalAddress>();
  }

  ngOnDestroy(): void {
    this._sharedDataService.additionalAddressList = [];
    this.fetchAdditionalAddressListener.unsubscribe();
  }

  showAdditionalAddressSection() {
    this.additionalAddrClick = true;
    this.getAdditionalAddress(this.property.PropertyID);
  }

  addAdditionalAddress() {
    this.editAdditionalAddress = new PropertyAdditionalAddress();
    this.showAdditionalAddress = true;
  }

  getAdditionalAddress(propertyId, fetchNew = false) {
    if (!fetchNew && !!this._sharedDataService.additionalAddressList && this._sharedDataService.additionalAddressList.length > 0 && this._sharedDataService.additionalAddressList[0].PropertyID == propertyId) {
      this.additionalAddressList = this._sharedDataService.additionalAddressList;
    } else {
      const response_location = this._propertyService.getPropertyAdditionalAddress(propertyId);
      response_location.subscribe(result => {
        if (!result.body.error) {
          const additionalAddress = result.body.responseData;
          if (additionalAddress.length > 0) {
            this.additionalAddressList = additionalAddress;
            this._sharedDataService.additionalAddressList = this.additionalAddressList;
            this._sharedDataService.additionalAddressList.forEach(element => {
              element.PropertyID = propertyId;
            });
          } else {
            this.additionalAddressList = [];
            this._sharedDataService.additionalAddressList = [];
          }
        }
      });
    }
  }

  closeAdditionalAddressModal(address: PropertyAdditionalAddress) {
    setTimeout(() => {
      this.getAdditionalAddress(this.property.PropertyID, true);
    }, 400);
    this.showAdditionalAddress = false;

  }


  deleteAdditionalAddressDetails(address) {
    const response_address = this._propertyService.PropertyAdditionalAddressDelete(address);
    response_address.subscribe(result => {
      if (!result || result.status === 201) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.AdditionalAddressDeleteFailed);
      } else if (result && result.status === 200) {
        this.getAdditionalAddress(this.property.PropertyID, true);
        this._notificationService.ShowSuccessMessage(CommonStrings.SuccessMessages.AdditionalAddressDeleteMessage)
      }
    });
  }

  deleteAdditionalAddressConfrimation(address) {
    if (address) {
      const configuration: confirmConfiguration = new confirmConfiguration();
      configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteAdditionalAddressConfirmationMessage;
      configuration.Title = CommonStrings.DialogConfigurations.Title.AdditionalAddress;
      configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
      configuration.OkButton.Callback = () => {
        this.deleteAdditionalAddressDetails(address);
      }
      configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
      configuration.CancelButton.Callback = () => {

      }
      this._notificationService.CustomDialog(configuration);
    }

  }

  updateAdditionalAddress(address) {
    this.editAdditionalAddress = address;
    this.showAdditionalAddress = true;
  }

}
