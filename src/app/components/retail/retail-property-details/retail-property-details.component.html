<app-property-use-details [useTypeForm]="retailForm"  [property]="property"  [dataArray]="dataArray" [propertyCopy]="propertyCopy" 
  [lookupDropdowns]="lookupDropdowns" [condo]="condo" [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"> </app-property-use-details>
  <app-height-loading-details
  [retailForm]="retailForm"  
  [property]="property"  
  [dataArray]="dataArray" 
  [propertyCopy]="propertyCopy" 
  [lookupDropdowns]="lookupDropdowns" 
  [condo]="condo"
  [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
></app-height-loading-details>
  <app-other-building-details
  [useTypeForm]="retailForm"
  [property]="property"
  [dataArray]="dataArray"
  [propertyCopy]="propertyCopy"
  [lookupDropdowns]="lookupDropdowns"
  [propertyStatus]="propertyStatus"
  [condo]="condo"
  [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
></app-other-building-details>
  <app-occupancy-internal-details
  [useTypeForm]="retailForm"
  [property]="property"
  [dataArray]="dataArray"
  [propertyCopy]="propertyCopy"
  [lookupDropdowns]="lookupDropdowns"
  [condo]="condo"
  [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
></app-occupancy-internal-details>



