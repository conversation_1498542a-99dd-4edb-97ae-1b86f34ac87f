import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { HeightLoadingDetailsComponent } from './height-loading-details.component';

describe('HeightLoadingDetailsComponent', () => {
  let component: HeightLoadingDetailsComponent;
  let fixture: ComponentFixture<HeightLoadingDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ HeightLoadingDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HeightLoadingDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
