import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AddMultiFreeholdComponent } from './add-multi-freehold.component';

describe('AddMultiFreeholdComponent', () => {
  let component: AddMultiFreeholdComponent;
  let fixture: ComponentFixture<AddMultiFreeholdComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AddMultiFreeholdComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddMultiFreeholdComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
