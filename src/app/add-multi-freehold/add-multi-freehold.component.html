<div>
  <div *ngIf="showMinMaxFields && !showConfirmationText">
    <div class="row form-wrapper" [formGroup]="rangeForm">
      <div class="col-md-5">
        New Child Buildings
      </div>
      <div class="col-md-3 no-padding">
        <label class="col-md-12 form-control-label" for="text-input"> Start                   
        </label>
        <div class="col-md-12">
          <input type="number" class="form-control" formControlName="FreeholdMin" [(ngModel)]="pRange.FreeholdMin" min="0"
          [ngClass]="{'error-field':((!rangeForm.controls['FreeholdMin'].valid) || freeholdMinMaxError)}">
        </div>
      </div>
      <div class="col-md-3 no-padding">
        <label class="col-md-12 form-control-label" for="text-input"> End                   
        </label>
        <div class="col-md-12">
          <input type="number" class="form-control" formControlName="FreeholdMax" [(ngModel)]="pRange.FreeholdMax" min="0"
          [ngClass]="{'error-field':((!rangeForm.controls['FreeholdMax'].valid) || freeholdMinMaxError)}">
        </div>
      </div>
    </div>
    <div class="btn-wrapper">
      <button (click)="onSaveMinMax()" class="btn btn-sm btn-primary-blue action-btn" [disabled]="freeholdMinMaxError">Save</button>
      <button (click)="onCancelMinMax()" class="btn btn-sm btn-primary-blue action-btn">Cancel</button>      
    </div>
  </div>
  <div *ngIf="!showMinMaxFields && showConfirmationText">
    <div>
      You are about to create {{unitsCount}} new Freehold Child Pins.  Do you wish to continue?
    </div>
    <div class="btn-wrapper">
      <button (click)="onYes()" class="btn btn-sm btn-primary-blue action-btn">Yes</button>
      <button (click)="onNo()" class="btn btn-sm btn-primary-blue action-btn">No</button>
      
    </div>
  </div>
</div>

