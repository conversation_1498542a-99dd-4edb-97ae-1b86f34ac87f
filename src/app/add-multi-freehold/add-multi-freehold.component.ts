import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonStrings } from '../constants';
import { NotificationService } from '../modules/notification/service/notification.service';

@Component({
  selector: 'app-add-multi-freehold',
  templateUrl: './add-multi-freehold.component.html',
  styleUrls: ['./add-multi-freehold.component.css']
})
export class AddMultiFreeholdComponent implements OnInit {

  @Input() freeholdMin: string;
  @Output() onSave:EventEmitter<any> = new EventEmitter<any>();
  @Output() onCancel:EventEmitter<any> = new EventEmitter<any>();

  rangeForm: FormGroup;
  pRange: {
    FreeholdMin: string,
    FreeholdMax: string
  } = {
    FreeholdMin: '',
    FreeholdMax: ''
  };
  freeholdMinMaxError = false;
  showMinMaxFields = true;
  showConfirmationText = false;
  unitsCount: number;
  private _notificationService: NotificationService;
  constructor(private formBuilder: FormBuilder,
    notificationService: NotificationService,
  ) { 
    this._notificationService = notificationService;
  }

  ngOnInit() {
    this.rangeForm = this.formBuilder.group({
      FreeholdMin: ['', [Validators.required, Validators.min(0), Validators.maxLength(6)]],
      FreeholdMax: ['', [Validators.required, Validators.min(0), Validators.maxLength(6)]]
    });

    this.rangeForm.valueChanges.subscribe(() => {
      this.checkFreeholdMinMaxError();
    });
    this.pRange.FreeholdMin = this.freeholdMin;
  }

  checkFreeholdMinMaxError() {
    const pRangeMin = this.rangeForm.get('FreeholdMin').value;
    const pRangeMax = this.rangeForm.get('FreeholdMax').value;
    const strataMin = parseInt(pRangeMin);
    const strataMax = parseInt(pRangeMax);
    if (strataMin < 0 || strataMax < 0) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FreeholdMinMaxUnitNegativeMessage);
      this.freeholdMinMaxError = true;
      return;
    }
    if(!strataMin || !strataMax ){
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FreeholdMinMaxUnitMessage);
      this.freeholdMinMaxError = true;
      return;
    }
    const units = strataMax - strataMin;
    
    if(strataMax <= strataMin){
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FreeholdMaxUnitMessage);
      this.freeholdMinMaxError = true;
      return;
    }

    if((units+1) > 30){
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FreeholdsUnitsExceedingMessage);
      this.freeholdMinMaxError = true;
      return
    }

    this.freeholdMinMaxError = false;
  }

  onSaveMinMax() {
    if (this.rangeForm.valid) {
      const pRangeMin = parseInt(this.rangeForm.get('FreeholdMin').value);
      const pRangeMax = parseInt(this.rangeForm.get('FreeholdMax').value);
      this.unitsCount = (pRangeMax - pRangeMin) + 1;
      this.showMinMaxFields = false;
      this.showConfirmationText = true;
    } else {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FreeholdMinMaxMessage);
    }
  }
  
  onCancelMinMax() {
    this.onCancel.emit();
  }

  onYes() {
    const pRangeMin = parseInt(this.rangeForm.get('FreeholdMin').value);
    const pRangeMax = parseInt(this.rangeForm.get('FreeholdMax').value);
    this.onSave.emit({min: pRangeMin, max: pRangeMax});
  }

  onNo() {
    this.showMinMaxFields = true;
    this.showConfirmationText = false;
  }

}
