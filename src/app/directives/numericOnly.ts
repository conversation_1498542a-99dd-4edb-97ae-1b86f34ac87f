import { Directive, HostListener, Input, ElementRef, OnInit } from '@angular/core';

@Directive({
  selector: '[numericOnly]'
})
export class NumericOnlyDirective implements OnInit {

  @Input() allowNegative: boolean = true;
  @Input() allowDecimal: boolean = true;
  @Input() allowZero: boolean = true;


  constructor(private element: ElementRef) {
  }

  ngOnInit(): void {
    this.formatValue();
  }

  @HostListener('input')
  public onChange(): void {
    this.formatValue();
  }

  private formatValue() {
    let allowNeg: boolean = true;
    let allowDec: boolean = true;

    allowNeg = this.convertToBoolean(this.allowNegative);
    allowDec = this.convertToBoolean(this.allowDecimal);

    let regex = /[^\d.-]/g;
    if (!allowNeg) {
      if (allowDec) {
        regex = /[^\d.]/g;
      } else {
        regex = /[^\d]/g;
      }
    } else if (!allowDec) {
      regex = /[^\d-]/g;
    }

    this.element.nativeElement.value = this.element.nativeElement.value ? this.element.nativeElement.value.replace(regex, '') : null;
    let allowZero = this.convertToBoolean(this.allowZero);
    if (!allowZero) {
      if (this.element.nativeElement.value) {
        if (parseInt(this.element.nativeElement.value) == 0) {
          this.element.nativeElement.value = '';
        }
      }

    }
  }

  private convertToBoolean(val: any, defaultValue: boolean = true): boolean {
    let res: boolean = false;
    if (val) {
      if (String(val).toUpperCase() == "TRUE" || String(val) == "1") {
        res = true;
      } else if (String(val) == "") {
        res = defaultValue;
      }
      else {
        res = false;
      }
    }
    else {
      res = defaultValue;
    }
    return res;
  }
}