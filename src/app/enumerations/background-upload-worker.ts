export const enum EventsSentToWorkers {
    masterStart = 'start-master-execution-evt',
    masterStop = 'stop-master-execution-evt',
    s3Upload = 's3-upload-evt',
    saveMediaDb = 'save-media-db-evt'
}

export const enum EventsReceivedFromWorkers {
    startBackgroundSync = 'startSync'
}

export const enum MediaUploadEvents {
    success = 'media-success',
    pending = 'media-pending',
    failure = 'media-fail'
}
