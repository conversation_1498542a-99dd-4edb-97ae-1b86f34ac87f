export enum IndustrialControls {
  BuildingClass = 'BuildingClass',
  SpecificUse = 'SpecificUse',
  BuildingSF = 'BuildingSF',
  ContributedGBA_SF = 'ContributedGBA_SF',
  ContributedGBASource = 'ContributedGBASource',
  GLA_SF = 'GLA_SF',
  IndustrialGLASource = 'IndustrialGLASource',
  OfficeSF = 'OfficeSF',
  OfficeNLASource = 'OfficeNLASource',
  GLAR_SF = 'GLAR_SF',
  RetailGLARSource = 'RetailGLARSource',
  SmallestFloor = 'SmallestFloor',
  LargestFloor = 'LargestFloor',
  YearBuilt = 'YearBuilt',
  YearRenovated = 'YearRenovated',
  EnergyStarRatingID = 'EnergyStarRatingID',
  WaterStarRatingID = 'WaterStarRatingID',
  GreenStarRatingID = 'GreenStarRatingID',
  OfficeMezzanine = 'OfficeMezzanine',
  Mezzanine_Size_SF = 'Mezzanine_Size_SF',
  Awnings = 'Awnings',
  AwningsCount = 'AwningsCount',
  Awnings_Size_SF = 'Awnings_Size_SF',
  GradeLevelDriveIn = 'GradeLevelDriveIn',
  DockHigh = 'DockHigh',
  Truckwell = 'Truckwell',
  ClearHeightMin = 'ClearHeightMin',
  ClearHeightMax = 'ClearHeightMax',
  HasYard = 'HasYard',
  HasYardFenced = 'HasYardFenced',
  YardPaved = 'YardPaved',
  HardstandArea = 'HardstandArea',
  HardstandAreaSource = 'HardstandAreaSource',
  ParkingSpaces = 'ParkingSpaces',
  ParkingRatio = 'ParkingRatio',
  RailServed = 'RailServed',
  CraneServed = 'CraneServed',
  HasPortAccess = 'HasPortAccess',
  HasSprinkler = 'HasSprinkler',
  SprinklerTypeID = 'SprinklerTypeID',
  Lifts = 'Lifts',
  LiftsCount = 'LiftsCount',
  Features = 'Features',
  PowerType = 'PowerType',
  PowerComments = 'PowerComments',
  BuildingComments = 'BuildingComments',
  BuildingWebsite = 'BuildingWebsite',
  TenancyTypeID = 'TenancyTypeID',
  IsOwnerOccupied = 'IsOwnerOccupied',
  GovernmentInterestID = 'GovernmentInterestID',
  BuildSpecStatusID = 'BuildSpecStatusID',
  OccupancyPercent = 'OccupancyPercent',
  Vacancy = 'Vacancy',
  OfficeHVAC = 'OfficeHVAC',
  HVAC = 'HVAC',
  HVACTypeID = 'HVACTypeID',
  RoofTypeID = 'RoofTypeID',
  HasSolar = 'HasSolar',
  BookValue = 'BookValue',
  BookValueDate = 'BookValueDate',
  IsADAAccessible = 'IsADAAccessible',
  ConstructionStartDate = 'ConstructionStartDate',
  EstCompletionDate = 'EstCompletionDate',
  ActualCompletion = 'ActualCompletion',
  IncludeinAnalytics = 'IncludeinAnalytics',
  InternalComments = 'InternalComments',
  CurrentTitle = 'CurrentTitle',
  TitleReferenceDate = 'TitleReferenceDate',
  GRESBScoreMin = 'GRESBScoreMin',
  GRESBScoreMax = 'GRESBScoreMax',
  ContributedSourceComments = 'ContributedSourceComments',
  TIAllowance = 'TIAllowance',
  Phase = 'Phase',
  Volts = 'Volts',
  Amps = 'Amps',
  IsLettableOverrideEnabled = 'IsLettableOverrideEnabled',
  TotalLettableSourceID = 'TotalLettableSourceID',
  TotalLettableSize_SF = 'TotalLettableSize_SF'
}

export const CheckboxFields = ['IsOwnerOccupied', 'IsADAAccessible', 'IncludeinAnalytics'];
