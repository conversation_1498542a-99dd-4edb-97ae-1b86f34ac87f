export enum IndexedDBCollections {
    imageUploadCollection = 'image-upload',
    backgroundSyncTask = 'background-sync-queue',
    pending = 'pending-queue',
    stagingImages = 'staging-images'
}

export enum MetaDataIndexedDBCollections {
    metaData = 'meta-data',
}

export enum MetaDataCollectionKeys {
    VisitedPropertyIds = 'VisitedPropertyIds',
    EditedPropertyIds = 'EditedPropertyIds',
    PropertyList = 'PropertyList',
    StateList = 'StateList',
    CountryList = 'CountryList',
    PropertyTypeList = 'PropertyTypeList',
    ResearchStatusesList = 'ResearchStatusesList',
    CityList = 'CityList',
    ResearchersList = 'ResearchersList',
    AuditStatusList = 'AuditStatusList',
    MultiFloors = 'MultiFloors',
    MultiFloorPolygons = 'MultiFloorPolygons'
}
