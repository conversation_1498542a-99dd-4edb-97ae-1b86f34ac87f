export enum LocationFormControls {
  AddressType = 'AddressType',
  PropertyName = 'PropertyName',
  StreetNumberMin = 'StreetNumberMin',
  StreetNumberMax = 'StreetNumberMax',
  AddressStreetName = 'AddressStreetName',
  StreetPrefix1 = 'StreetPrefix1',
  StreetPrefix2 = 'StreetPrefix2',
  StreetSuffix1 = 'streetSuffix1',
  StreetSuffix2 = 'streetSuffix2',
  Quadrant = 'Quadrant',
  EastWestStreet = 'EastWestStreet',
  NorthSouthStreet = 'NorthSouthStreet',
  UseAddressAsPropertyName = 'UseAddressAsPropertyName',
  Complex = 'Complex',
  PartOfComplex = 'PartOfComplex',
  State = 'State',
  CountryID = 'CountryID',
  Zip = 'Zip',
  City = 'City',
  County = 'County',
  RooftopSourceID = 'RooftopSourceID',
  BuildingNumber = 'BuildingNumber',
  MarketID = 'MarketId',
  SubMarketID = 'SubMarketID',

  // Intersection Info Fields
  PrimaryStreet = 'PrimaryStreet',
  PrimaryAccess = 'PrimaryAccess',
  PrimaryTrafficCount = 'PrimaryTrafficCount',
  PrimaryTrafficCountDate = 'PrimaryTrafficCountDate',
  PrimaryFrontage = 'PrimaryFrontage',
  SecondaryStreet = 'SecondaryStreet',
  SecondaryAccess = 'SecondaryAccess',
  SecondaryTrafficCount = 'SecondaryTrafficCount',
  SecTrafficCntDate = 'SecTrafficCntDate',
  SecondaryFrontage = 'SecondaryFrontage',

  // Legal Information Fields
  LegalDesc = 'LegalDesc',
  LGA = 'LGA',
  GeoscapePropertyID = 'GeoscapePropertyID',
  CounsilTaxID = 'CounsilTaxID',
  ValuerGeneralID = 'ValuerGeneralID',
}
