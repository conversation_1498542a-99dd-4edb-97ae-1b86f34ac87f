export enum Overlays {
  AzureSatelliteMap='Azure-Satellite-Map',
  Zoning = 'Zoning',
  Parcel = 'Parcel',
  Building = 'Building',
  PostalCode = 'Postal Code',
  Strata = 'Strata',
  NotStrata = 'NotStrata',
}

export const ZoningColorData = [
  { zone: 'Office', rgb: [2, 100, 197, 70], fill: '#0264C5' },
  { zone: 'Industrial', rgb: [181, 19, 19, 70], fill: '#B51313' },
  { zone: 'Retail', rgb: [185, 141, 30, 70], fill: '#B98D1E' },
  { zone: 'Mixed-Use', rgb: [20, 170, 186, 70], fill: '#14AABA' },
  { zone: 'Other', rgb: [224, 224, 224, 70], fill: '#E0E0E0' },
];

export const TileLayerColorCodes = {
  zoning: {
    lineColor: [0, 0, 0, 0],
    lineWidth: 0,
  },
  strata: {
    lineColor: [255, 0, 0],
    lineWidth: 0.7,
    fillColor: [0, 255, 255, 0],
    highlightColor: [255, 255, 150, 90],
  },
  notStrata: {
    lineColor: [0, 0, 0],
    lineWidth: 0.7,
    fillColor: [0, 255, 255, 0],
    highlightColor: [255, 255, 150, 90],
  },
  building: {
    lineColor: [255, 0, 0],
    lineWidth: 0,
    fillColor: [255, 0, 0, 98],
  },
  postalCode: {
    lineColor: [0, 0, 0],
    lineWidth: 0,
    fillColor: [0, 0, 0, 0],
    highlightColor: [255, 255, 0, 60],
    highlightLineColor: [255, 255, 0],
  },
};

export const ParcelKeyNameMapping = {
  Lot: 'Lot',
  Lot_Area: 'Lot Area',
  Parcel_No: 'Parcel No',
  Plan: 'Plan',
  Strata_Typ: 'Strata Type',
  General_Use: "General Use",
  Level1_Zoning: "Level1 Zoning",
  Level2_Zoning: "Level2 Zoning",
  first_owne: 'First Owner',
};

export const BuildingKeyNameMapping = {
  Floor_Area: 'Floor Area',
  General_Use: "General Use",
  Gross_Building_Area: "Gross Building Area",
  LGA: "LGA",
  Level1_Zoning: "Level1 Zone",
  Number_Floors_Estimate: "Number of Floors",
  Strata_Type: "Strata Type",
};

export enum LayerInfoCardColor {
  Parcel = 'blue',
  Building = 'red'
}

export enum LayerInfoCardHeading {
  Parcel = 'Parcel Info',
  Building = 'Building Info'
}

export enum ZoomLevels {
  ZoomSeventeen = 17,
}

export enum BuildingTileKeys {
  AL_Building_ID = 'AL_Building_ID',
  BuildingFootPrintID = 'BuildingFootPrintID'
}
