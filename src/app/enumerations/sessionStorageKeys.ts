export enum SessionStorageKeys {
    LogInData = 'LogInData',
    AccessToken = 'access-token',
    ActiveGridRowNumber = 'ActiveGridRowNumber',
    IsNavigationFromSearch = 'IsNavigationFromSearch',
    PropertyId = 'PropertyId',
    SelectedPropertyPos = 'SelectedPropertyPos',
    SortCriteria = 'SortCriteria',
    SearchResultsPageDetails = 'SearchResultsPageDetails',
    VisitedStrataIds = 'VisitedStrataIds',
    EditedStrataIds = 'EditedStrataIds',
    StrataPropertyIds = 'StrataPropertyIds',
    SearchResults = 'SearchResults',
    SearchCriteria = 'SearchCriteria',
    NavigationPreference = 'NavigationPreference',
    LastVisitedStrataProperty = 'LastVisitedStrataProperty',
    FetchProperties = 'FetchProperties',
    isNavigatedFromFAC = 'isNavigatedFromFAC',
    propertyIdFromFAC = 'propertyIDFromFAC',
    LoggedInUserData = 'LoggedInUserData',
    IsNavigatedFromERC = 'IsNavigatedFromERC',
    PropertyDetailsTabStatus = 'PropertyDetailsTabStatus',
    Address = 'Address',
    isNavigatedFromLIT = 'isNavigatedFromLIT',
    Latitude = 'Latitude',
    Longitude = 'Longitude',
    IsCreateProperty = 'IsCreateProperty',
    IsEditProperty = 'IsEditProperty',
    IsFromMapSearch = 'IsFromMapSearch',
    SourceAddress = 'SourceAddress',
    CityName = 'CityName',
}
