export enum LandControls {
  LandUseID = 'LandUseID',
  SpecificUse = 'SpecificUse',
  Zoning = 'Zoning',
  ZoningCode = 'ZoningCode',
  PotentialZoningID = 'PotentialZoningID',
  HasYardFenced = 'HasYardFenced',
  HasYardUnfenced = 'HasYardUnfenced',
  YardPaved = 'YardPaved',
  UtilityComments = 'UtilityComments',
  BookValue = 'BookValue',
  SurroundingLandUse = 'SurroundingLandUse',
  NLA_SF = 'NLA_SF',
  NLAac = 'NLAac',
  Width = 'Width',
  Depth = 'Depth',
  IsFloodPlain = 'IsFloodPlain',
  EarthquakeZoneID = 'EarthquakeZoneID',
  RailServed = 'RailServed',
  HasPortAccess = 'HasPortAccess',
  PropertyComments = 'PropertyComments',
  BookValueDate = 'BookValueDate'
}
