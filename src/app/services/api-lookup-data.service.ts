import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';

@Injectable()
export class LookupDataService extends ApiBaseService {
    public GetAllSpecificUse() {
        const response = this.httpGet(this._serviceURL + 'lookup/specificUse');
        return response;
    }

    public GetAllPropertyType() {
        const response = this.httpGet(this._serviceURL + 'lookup/propertyType');
        return response;
    }

    public GetStateListByCountryId(countryId: number) {
        const response = this.httpGet(this._serviceURL + 'LookUp/state/' + countryId);
        return response;
    }

    public GetCitiesByState(stateId) {
        const response = this.httpGet(this._serviceURL + 'lookup/city/' + stateId);
        return response;
    }
    public GetAllPropertyResearchType() {
        const response = this.httpGet(this._serviceURL + 'research/researchtype');
        return response;
    }
    public GetPropertyAuditStatuses() {
        const response = this.httpGet(this._serviceURL + 'audit/propertystatus/lookup');
        return response;
    }

    public GetAllMarketList(metroID, useTypeID) {
        const response = this.httpGet(this._serviceURL + 'LookUp/market' + '/' + metroID + '/' + useTypeID);
        return response;
    }

    public getAllSubMarketList(marketID) {
        const response = this.httpGet(this._serviceURL + 'LookUp/subMarket/' + marketID);
        return response;
    }

    public getPropertyFieldsDropdownItems(entityId: number) {
        const response = this.httpGet(`${this._serviceURL}/vst/lookupWithFieldName/${entityId}`);
        return response;
      }
}
