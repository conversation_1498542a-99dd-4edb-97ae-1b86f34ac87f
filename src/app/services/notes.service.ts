import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';
import { Notes } from '../models/notes';

@Injectable()
export class NotesService extends ApiBaseService {
   
    public GetNotes(note: Notes){
        const response = this.httpGet(this._serviceURL +'contacts/notes/'+ note.ParentTableID+'/'+note.ParentID);
        return response;
    }

    public SaveNote(note: Notes){
        const response = this.httpPost(this._serviceURL +'contacts/notedetails/',JSON.stringify(note));
        return response;
    }

    public DeleteNote(note: Notes){
        const response = this.httpPost(this._serviceURL +'contacts/notes/',JSON.stringify(note));
        return response;
    }

}