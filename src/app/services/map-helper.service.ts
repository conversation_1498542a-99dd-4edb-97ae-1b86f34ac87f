
import { Injectable } from '@angular/core';
import { ResearchType } from '../enumerations/researchType';
import { LatLng } from '../modules/map-module/models/LatLng';
import {Generaluse, SpecificUses} from '../models/Common'

@Injectable()
export class MapHelperService {
  private baseUrl = 'assets/images/googleMapPins/';
  constructor() { }

  GetNeutralPinUrl() {
    return this.baseUrl + 'marker_neutral.png';
  }

  GetClickedPositionPin() {
    return this.baseUrl + 'marker_click.png';
  }

  GetPropertyPinByResearchType(researchTypeId: number, genUse: number, specificUse: number) {
    let url = this.baseUrl + 'marker_';
    switch (researchTypeId) {
      case ResearchType.NeedsResearch:
        url += 'pink.png';
        break;
      case ResearchType.BaseComplete:
        if(genUse && specificUse && genUse == Generaluse.Apartments && specificUse != SpecificUses.MixedUse){
          url += 'White.png';
        }
        else {
        url += 'yellow.png';
        }
        break;
      case ResearchType.FieldResearchComplete:
        url += 'green.png';
        break;
      case ResearchType.Hidden:
        url += 'black.png';
        break;
      case ResearchType.ExpressComplete:
        url += 'orange.png';
        break;
      case ResearchType.NotStarted:
      default:
        url += 'red.png';
        break;
    }
    return url;
  }

  GetLatLngListFromPolygon(polygonText: string): Array<LatLng> {
    let latlngList: Array<LatLng> = new Array<LatLng>();
    if (polygonText.includes('MULTIPOLYGON')) {
      polygonText = polygonText.replace('MULTIPOLYGON(((', '');
      polygonText = polygonText.replace(')))', '');
    } else {
      polygonText = polygonText.replace('POLYGON((', '');
      polygonText = polygonText.replace('))', ''); 
    }
    polygonText.split(',').forEach(point => {
      let latlng = new LatLng();
      latlng.Latitude = parseFloat(point.split(' ')[1]);
      latlng.Longitude = parseFloat(point.split(' ')[0]);
      latlngList.push(latlng);
    });
    return latlngList;
  }

}
