import { NotesComponent } from './../pages/notes/notes.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { MapHelperService } from './map-helper.service';
import { CommunicationService } from './communication.service';
import { PropertyService } from './api-property.service';
import { LoginService } from './login.service';
import { MediaService } from './media.service';
import { ParcelService } from './parcel.service'
import { SharedDataService } from './shareddata.service';
import { NotesService } from './notes.service';
import { BuildingFootPrintService } from './building-footprint.service';
import { AddressService } from './address.service';
import { LookupDataService } from './api-lookup-data.service';
import { PagerService } from './pager.service';
import { IndexedDBService } from './indexeddb.service';
import { ImageUploadWorkerService } from './image-upload-worker.service'
import { MetaDataIndexedDBService } from './indexed-db-service.service';
import { UserService } from './user.service';
import { AddFloorService } from './add-floor.service';
import {WhatsNewService} from './whats-new-service';

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
  ],
  declarations: [],
  providers: [
    MapHelperService,
    CommunicationService,
    PropertyService,
    LoginService,
    MediaService,
    ParcelService,
    NotesService,
    SharedDataService,
    BuildingFootPrintService,
    AddressService,
    LookupDataService,
    PagerService,
    IndexedDBService,
    ImageUploadWorkerService,
    MetaDataIndexedDBService,
    UserService,
    AddFloorService,
    WhatsNewService
  ]
})
export class ServicesModule { }
