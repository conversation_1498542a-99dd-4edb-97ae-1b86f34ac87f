import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';
import {UserModel} from '../models/login';
import { EnumApplication } from '../enumerations/application';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';
import { SessionStorageKeys } from '../enumerations/sessionStorageKeys';

@Injectable()
export class LoginService extends ApiBaseService {
    

public UserInfo:UserModel=new UserModel();

    public login(userdata: any) {
        userdata.ApplicationID = EnumApplication.VST;
        const response = this.httpPost(this._serviceURL + 'user/login/', JSON.stringify(userdata));
        return response;
    }

    saveLoginSSODetails(details: any) {
        const response = this.httpPost(this._serviceURL + 'login/SSO/save', JSON.stringify(details));
        return response;
    }

    refreshToken(userDetails: any) {
        const response = this.httpPost(this._serviceURL + 'refreshToken', JSON.stringify(userDetails));
        return response
    }
    
    SSOValidateToken(token: any) {
        const response = this.httpPost(this._serviceURL + 'login/sso/validate/token',
            JSON.stringify({ token: token, ApplicationID: EnumApplication.ERC }));
        return response.pipe(map(
            data => {
                const userInfo = data.body;
                if (userInfo != null && userInfo.responseData != null && userInfo.responseData.Response && userInfo.responseData.Response.length > 0) {
                    if (userInfo.responseData.Response[0].ResponseCode > 0 && userInfo.responseData.UserInfo !== null && userInfo.responseData.UserInfo.length > 0) {
                        userInfo.responseData.UserInfo[0].DateFormatForDatePicker = userInfo.responseData.UserInfo[0].DateFormat.replace(/[/]/g, '-').toUpperCase();
                        const loggedInUserDataResponse = userInfo.responseData.UserInfo[0];
                        this.UserInfo.IsLoggedin = true;
                        this.UserInfo.CountryId = loggedInUserDataResponse.CountryID;
                        this.UserInfo.EntityID = loggedInUserDataResponse.EntityID;
                        this.UserInfo.PersonName = loggedInUserDataResponse.PersonName;
                        this.UserInfo.UnitID = loggedInUserDataResponse.UnitId;
                        this.UserInfo.RoleID = loggedInUserDataResponse.RoleID;
                        this.UserInfo.UnitDisplayTextSize = loggedInUserDataResponse.UnitDisplayTextSize;
                        this.UserInfo.MetroCentroidLat = loggedInUserDataResponse.MetroCentroidLat || -37.814;;
                        this.UserInfo.MetroCentroidLong = loggedInUserDataResponse.MetroCentroidLong || 144.96332;
                        this.UserInfo.DateFormat = loggedInUserDataResponse.DateFormat;
                        this.UserInfo.MainPhotoUrl = loggedInUserDataResponse.MainPhotoUrl;
                        this.UserInfo.RoleName = loggedInUserDataResponse.RoleName;
                        sessionStorage.setItem(SessionStorageKeys.AccessToken, userInfo.responseData.Token);
                        sessionStorage.setItem(SessionStorageKeys.LoggedInUserData, JSON.stringify(this.UserInfo));
                        const CryptoJS = require("crypto-js");
                        const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(this.UserInfo), environment.EncryptionKey);
                        sessionStorage.setItem(SessionStorageKeys.LogInData, encryptedData);
                        return true;
                    } else {
                        return userInfo.responseData.Response[0].ErrorMessage;
                    }
                }
                return userInfo.message;
            }));
    }
}
