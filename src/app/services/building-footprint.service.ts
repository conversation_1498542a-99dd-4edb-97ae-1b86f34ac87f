import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';

@Injectable()
export class BuildingFootPrintService extends ApiBaseService  {

  public getBuildingFootPrintTiles(SWLat, SWLng, NELat, NELng) {
    const response = this.httpGet(this._serviceURL + 'property/buildingFootPrintTiles/' + SWLat + '/' + SWLng + '/' + NELat + '/' + NELng);
    return response;
  }

  public createBuildingFootPrint(PolygonLatLong, PropertyID, area) {
    const response = this.httpPost(this._serviceURL + 'property/buildingFootPrints/', { shape: PolygonLatLong, PropertyID, area } );
    return response;
  }

  public updateBuildingFootPrint(buildingFootPrintId, PolygonLatLong, LOTPLAN, PropertyID, area) {
    const response = this.httpPut(this._serviceURL + 'property/buildingFootPrints/', {shape: PolygonLatLong, buildingFootPrintID: buildingFootPrintId,
      lotplan: LOTPLAN, PropertyID, area} );
    return response;
  }

  public deleteBuildingFootPrint(buildingFootPrintIDs: any[], PropertyID: number) {
    const url = this._serviceURL + 'property/buildingFootPrints';
    return this.httpDelete(url, { buildingFootPrintIDs: buildingFootPrintIDs.join(','), PropertyID: PropertyID });
  }

  public getBuildingFootPrintsByPropertyID(propertyID) {
    const response = this.httpGet(this._serviceURL + 'property/buildingFootPrintTiles/' + propertyID);
    return response;
  }

  public saveMultiFootprint(body: any) {
    const response = this.httpPut(this._serviceURL + '/property/buildingFootPrints/update', body );
    return response;
  }
}
