import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable()

export class AddFloorService {
    private floorLabelStatus : BehaviorSubject<boolean>;
    constructor(){
        this.floorLabelStatus = new BehaviorSubject<boolean>(false);
    }

    getValue(): Observable<boolean> {
        return this.floorLabelStatus.asObservable();
      }
  // Method to enable labels
  enableFloorLables(value): void {
    this.floorLabelStatus.next(value);
  }

  // Method to disable labels (optional)
//   disableFloorLables() {
//     this.floorLabelStatus.next(false);
//   }
}