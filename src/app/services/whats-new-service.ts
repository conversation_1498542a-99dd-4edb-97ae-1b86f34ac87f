import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';
import { EnumApplication } from '../enumerations/application';

@Injectable()
export class WhatsNewService extends ApiBaseService  {
 
public getReleaseUpdateNotes() {
  const applicationID = EnumApplication.VST;
  const response = this.httpGet(
      `${this._serviceURL}release-updates?applicationID=${applicationID}`
    );
    return response;
}
}
