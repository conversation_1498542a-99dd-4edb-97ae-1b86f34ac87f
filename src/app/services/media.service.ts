import { Injectable } from '@angular/core';
import * as AWS from 'aws-sdk/global';
import * as S3 from 'aws-sdk/clients/s3';
import { ApiBaseService } from './api-base.service';
import { FileObject, FileTypes } from '../modules/aws/types/types';
import { Observable } from 'rxjs';
import { Media } from '../models/MediaType';
import { EnumApplication } from '../enumerations/application';
import { ContactMedia } from '../models/ContactMedia';


@Injectable()
export class MediaService extends ApiBaseService {

  public SaveMediaToDatabase(media: Media): Observable<any> {
    let mediaRequest = {
      P_MediaID: media.MediaID
      , P_MediaName: media.MediaName
      , P_Height: media.Height
      , P_Width: media.Width
      , P_Size: media.Size
      , P_Path: media.Path
      , P_Ext: media.Ext
      , P_Description: media.Description
      , P_EntityID: media.CreatedBy
      , P_MediaRelationTypeID: media.RelationshipTypeID
      , P_RelationID: media.RelationID
      , P_MediaTypeID: media.MediaTypeID
      , P_MediaSubTypeID: media.MediaSubTypeID
      , P_PropertyID: media.PropertyID
      , P_IsDefault: media.IsDefault
      , P_IsOwnMedia: media.IsOwnMedia
      , P_MediaSourceID: media.MediaSourceID
      , P_SourceComments: media.SourceComments
      , P_ChangeLogJSON: media.ChangeLogJSON
      , P_ApplicationID: EnumApplication.VST
    }

    const response = this.httpPost(this._serviceURL + 'media/mediaInsert/', JSON.stringify(mediaRequest), true);
    return response;
  }

  public GetAllPropertyMedia(propertyId: number): Observable<any> {
    const response = this.httpGet(this._serviceURL + 'media/getAllPropMedia/' + propertyId);
    return response;
  }

  public GetMediaSource(): Observable<any> {
    const response = this.httpGet(this._serviceURL + '/media/mediaSource');
    return response;
  }

  public GetMediaTypes(): Observable<any> {
    const response = this.httpGet(this._serviceURL + '/media/mediaType');
    return response;
  }

  public GetMediaSubTypes(): Observable<any> {
    const response = this.httpGet(this._serviceURL + '/media/mediaSubType');
    return response;
  }

  public DeletePropertyMedia(media: Media): Observable<any> {
    let mediaRequest = {
      P_EntityID: media.ModifiedBy,
      P_MediaRelationshipID: media.MediaRelationshipID,
      P_ApplicationID: EnumApplication.VST
    }

    const response = this.httpPost(this._serviceURL + 'media/deleteMedia/', JSON.stringify(mediaRequest));
    return response;
  }

  public SetDefaultMedia(media: Media): Observable<any> {
    let mediaRequest = {
      P_EntityID: media.ModifiedBy
      , P_RelationID: media.RelationID
      , P_MediaID: media.MediaID
      , P_MediaRelationshipID: media.MediaRelationshipID
      , P_PropertyID: media.PropertyID
      , P_IsDefault: 1
    }

    const response = this.httpPost(this._serviceURL + 'media/setdefaultMedia/', JSON.stringify(mediaRequest));
    return response;
  }

  public uploadToS3(fileName, base64, isMarketBrief?: boolean, isClientLogo?: boolean, isSignature?: boolean) {
    if (isSignature) {
      const guid = this.generateUUID();
      fileName = guid + '.jpeg';
    }
    const UploadedData = { fileName: fileName, base64: base64, isMarketBrief: isMarketBrief, isClientLogo: isClientLogo, isSignature: isSignature };
    const response = this.httpPost(this._serviceURL + 'image/imageUpload/', UploadedData);
    return response;
  }

  generateUUID() {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      // tslint:disable-next-line: no-bitwise
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      // tslint:disable-next-line: no-bitwise
      return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
  }

  public mediaCheck(media, isImageOnly?: boolean): boolean {
    if (isImageOnly) {
      if (media.Ext.toLowerCase() !== FileTypes.PNG && media.Ext.toLowerCase() !== FileTypes.JPEG && media.Ext.toLowerCase() !== FileTypes.JPG) {
        return false;
      }
    }
    if (media.Ext.toLowerCase() !== FileTypes.PDF && media.Ext.toLowerCase() !== FileTypes.TEXT && media.Ext.toLowerCase() !== FileTypes.WORD
      && media.Ext.toLowerCase() !== FileTypes.DOC && media.Ext.toLowerCase() !== FileTypes.EXCEL && media.Ext.toLowerCase() !== FileTypes.CSV
      && media.Ext.toLowerCase() !== FileTypes.PNG && media.Ext.toLowerCase() !== FileTypes.JPEG && media.Ext.toLowerCase() !== FileTypes.JPG) {
      return false;
    }
  }

  public fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PDF) {
      media.URL = 'assets/images/pdf.png';
    } else if (media.Ext.toLowerCase() === FileTypes.TEXT) {
      media.URL = 'assets/images/text.png';
    } else if (media.Ext.toLowerCase() === FileTypes.WORD || media.Ext.toLowerCase() === FileTypes.DOC) {
      media.URL = 'assets/images/wordImg.png';
    } else if (media.Ext.toLowerCase() === FileTypes.EXCEL) {
      media.URL = 'assets/images/excel.png';
    } else if (media.Ext.toLowerCase() === FileTypes.CSV) {
      media.URL = 'assets/images/csv.png';
    }
  }
  public getAllMediaRelation(contactMedia: ContactMedia): Observable<any> {
    const response = this.httpGet(this._serviceURL + 'media/getAllMediaRelation/' +
      contactMedia.RelationshipTypeID + ' / ' + contactMedia.RelationID, { applicationID: EnumApplication.VST });
    return response;
  }
  public GetAllStrataPropertyMediaByPropertyId(property_Id, loggedInUserId): any {
    return this.httpGet(this._serviceURL + 'media/getAllStrataPropMedia/' + property_Id + '/' + loggedInUserId)
  }
}
