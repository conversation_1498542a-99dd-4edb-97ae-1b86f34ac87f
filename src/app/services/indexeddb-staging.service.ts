import { Injectable } from '@angular/core';
import { FileObject } from '../modules/aws/types/types'
import { Media } from '../models/MediaType';
import { IndexedDBCollections } from '../enumerations/indexeddb'
import { MediaUploadEvents } from '../enumerations/background-upload-worker'

export interface IMediaInfo {
    fileObject?: FileObject;
    media: Media;
    fileName: string;
    attachments: { fileName: string, media: Media }[]
}
export interface IIndexedDBMedia {
    mediaIdentifier: string;
    mediaInformation: IMediaInfo
}

export type Task = {
    taskID: string,
    taskIdentifier: string,
    mediaIdentifier: string,
    isAttachment: boolean,
    attachmentIndex: number | null,
    path?: string | undefined,
    mediaID?: number | undefined
}

export interface IBackgroungSync {
    key: string,
    taskQueue: Array<Task>

}

@Injectable()
export class StagingIndexedDBService {
    private database = 'AL-VST-Staging';
    private version = 1;
    private dbInstance: IDBDatabase;
    private indexDBInitializationStatus: boolean;
    s3UploadTaskIdentifier = 's3-upload';
    saveMediaTaskIdentifier = 'save-media-db';
    private backgroundSyncTaskQueueKey = 'to-execute';
    static uploadMediaMap: Map<string, number> = new Map<string, number>();

    constructor() {
    
        this.indexDBInitializationStatus = false;
        !this.indexDBInitializationStatus && this.initializeIndexedDB(this.version).then(status => this.indexDBInitializationStatus = true).catch(() => this.indexDBInitializationStatus = false);
    }

    /**
    * Method to Initialize IndexedDB
    * @param { number } [_version=1] - The version number for the IndexedDB (optional, defaults to 1)
    */
    private initializeIndexedDB = async (version: number): Promise<boolean> => {
        return new Promise<boolean>((resolve, reject) => {

            const dbRequest: IDBOpenDBRequest = indexedDB.open(this.database, version);
            dbRequest.onerror = () => {
                reject(false);
            };

            dbRequest.onupgradeneeded = () => {
                const dbInstance: IDBDatabase = dbRequest.result;
                // Initialize 'image-upload' Object Store
                try {
                    if (!dbInstance.objectStoreNames.contains(IndexedDBCollections.stagingImages)) {
                        dbInstance.createObjectStore(IndexedDBCollections.stagingImages, { keyPath: 'mediaIdentifier' });
                    }
                } catch (error) {
                    reject(false);
                }
            };

            dbRequest.onsuccess = () => {
                this.dbInstance = dbRequest.result;
                resolve(true);
            }
        });
    };

    /**
     * Method to Get IndexedDB Initialization Status 
    */
    getIndexedDBInitializationStatus = () => {
        return this.indexDBInitializationStatus;
    }

    /**
    * Method to Insert Data Into IndexedDB
    * @param { any } info Information to Insert
    * @param { string } store Object Store Name
    */
    saveMedia = async (info: IIndexedDBMedia, store: string = IndexedDBCollections.stagingImages): Promise<void> => {
        return new Promise<void>(async (reslove, reject) => {

          if(store === IndexedDBCollections.stagingImages){
            const transaction = this.dbInstance.transaction(store, 'readwrite');
            const storeInstance = transaction.objectStore(store);

            const response = storeInstance.put(info);
            response.onsuccess = () => {
                reslove();
            };
            response.onerror = (error) => {
                reject(error);
            };
          }
        });
    };

    saveStagingMedia = async (info: IIndexedDBMedia): Promise<void> => {
        return this.saveMedia(info, IndexedDBCollections.stagingImages);
    }

    clearCollection = (store: string ) => {
        const instance = this;
        const request = indexedDB.open(this.database);
        request.onsuccess = function(event: any) {
            const db = event && event.target && event.target.result;
            if (db && db.objectStoreNames.contains(store)) {
                const transaction = instance.dbInstance.transaction(store, 'readwrite');
                const storeInstance = transaction.objectStore(store);
                const response = storeInstance.clear();
        
                response.onsuccess = () => {
                    return;
                }
            }
        }
        
    }

    fetchAllStagingMedia = async (): Promise<IIndexedDBMedia[]> => {
        return new Promise<IIndexedDBMedia[]>((resolve, reject) => {
            const transaction = this.dbInstance.transaction(IndexedDBCollections.stagingImages, 'readonly');
            const storeInstance = transaction.objectStore(IndexedDBCollections.stagingImages);
            const mediaList: IIndexedDBMedia[] = [];

            const request = storeInstance.openCursor();

            request.onsuccess = (event) => {
                const cursor = (event.target as IDBRequest).result as IDBCursorWithValue;
                if (cursor) {
                    mediaList.push(cursor.value);
                    cursor.continue();
                } else {
                    resolve(mediaList);
                }
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    removeMedia = (key: string, store: string = IndexedDBCollections.imageUploadCollection) => {
        const transaction = this.dbInstance.transaction(store, 'readwrite');
        const storeInstance = transaction.objectStore(store);
        const response = storeInstance.delete(key);

        response.onsuccess = () => {
            return;
        }
    }
}
