import { Injectable } from '@angular/core';
import { MetaDataIndexedDBCollections } from '../enumerations/indexeddb';


export interface IMetaData {
    key: string;
    value: any;
}


@Injectable()
export class MetaDataIndexedDBService {
    private database = 'AL-VST-Meta-Data';
    private version = 1;
    static dbInstance: IDBDatabase;
    private metaDataDBInitializationStatus: boolean;
    private dbPromise: Promise<IDBDatabase>;

    constructor() {
        this.metaDataDBInitializationStatus = false;
        this.dbPromise = this.initializeMetaDataIndexedDB(this.version)
            .then(status => {
                this.metaDataDBInitializationStatus = status;
                return MetaDataIndexedDBService.dbInstance;
            })
            .catch(() => {
                this.metaDataDBInitializationStatus = false;
                return null;
            });
    }

    private initializeMetaDataIndexedDB = async (version: number): Promise<boolean> => {
        return new Promise<boolean>((resolve, reject) => {
            const dbRequest: IDBOpenDBRequest = indexedDB.open(this.database, version);
            dbRequest.onerror = () => {
                console.log(dbRequest.error, 'dbRequest.error');
                reject(false);
            };
            dbRequest.onupgradeneeded = () => {
                const dbInstance: IDBDatabase = dbRequest.result;
                if (!dbInstance.objectStoreNames.contains(MetaDataIndexedDBCollections.metaData)) {
                    dbInstance.createObjectStore(MetaDataIndexedDBCollections.metaData, { keyPath: 'key' });
                }
            };
            dbRequest.onsuccess = () => {
                MetaDataIndexedDBService.dbInstance = dbRequest.result;
                resolve(true);
            };
        });
    };

    async saveDataInMetaData(data: IMetaData) {
        const dbInstance = await this.dbPromise;
        if (dbInstance) {
            const transaction = dbInstance.transaction(MetaDataIndexedDBCollections.metaData, 'readwrite');
            const storeInstance = transaction.objectStore(MetaDataIndexedDBCollections.metaData);
            const response = storeInstance.put(data);
            return new Promise<void>((resolve, reject) => {
                response.onsuccess = () => resolve();
                response.onerror = () => reject();
            });
        } else {
            throw new Error('IndexedDB initialization failed');
        }
    }

    async retriveDataFromMetaData(key: string) {
        const dbInstance = await this.dbPromise;
        if (dbInstance) {
            return new Promise<IMetaData | null>((resolve) => {
                const storeInstance = dbInstance.transaction(MetaDataIndexedDBCollections.metaData, 'readonly').objectStore(MetaDataIndexedDBCollections.metaData);
                const response = storeInstance.get(key);
                response.onsuccess = () => resolve(response.result);
                response.onerror = () => resolve(null);
            });
        } else {
            throw new Error('IndexedDB initialization failed');
        }
    }

    async deleteDataFromMetaData(key: string): Promise<void> {
        const dbInstance = await this.dbPromise;
        if (dbInstance) {
            return new Promise<void>((resolve, reject) => {
                const transaction = dbInstance.transaction(MetaDataIndexedDBCollections.metaData, 'readwrite');
                const storeInstance = transaction.objectStore(MetaDataIndexedDBCollections.metaData);
                const response = storeInstance.delete(key);
                response.onsuccess = () => resolve();
                response.onerror = () => reject(new Error(`Failed to delete data for key '${key}' from IndexedDB`));
            });
        } else {
            throw new Error('IndexedDB initialization failed');
        }
    }

}
