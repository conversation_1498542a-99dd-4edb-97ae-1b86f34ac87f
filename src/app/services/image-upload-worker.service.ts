import { Injectable } from '@angular/core'
import { EventsSentToWorkers, EventsReceivedFromWorkers, MediaUploadEvents } from '../enumerations/background-upload-worker'
import { IndexedDBCollections } from '../enumerations/indexeddb'
import { IndexedDBService, Task, IIndexedDBMedia, IMediaInfo } from './indexeddb.service';
import { environment } from '../../environments/environment';

export interface Iworker {
    worker: Worker,
    available: boolean
}

@Injectable()
export class ImageUploadWorkerService {
    private master: Worker = null;
    private masterBusyStatus = false;
    private workers = environment.UploadWorkersFrequency;
    private workerPool: Iworker[] = [];
    private IndexedDBService: IndexedDBService

    constructor() {
        (async () => {
            await this.initWorkerPool();
            await this.initiateMasterExecution();
            this.IndexedDBService = new IndexedDBService();
        })();

    };

    private constructMaster = async (): Promise<Worker> => {
        return new Promise<Worker>((resolve, reject) => {
            const masterScript = `
                self.onmessage = async function (event) {
                    if(event.data.masterWorkerEvent === '${EventsSentToWorkers.masterStart}') {
                        checkAndProcesstasks();
                    }
                }


                function checkAndProcesstasks() {
                    checkTasksPeriodically();
                }
                function checkTasksPeriodically() {
                   
                    const checkTasks = function () {
                        self.postMessage({ taskIdentifier: '${EventsReceivedFromWorkers.startBackgroundSync}'});
                        setTimeout(checkTasks, ${environment.MasterWorkerExecutionDuration}); // Check every 1 seconds
                    }
                    checkTasks();
                }
               
            `

            const workerBlob = new Blob([masterScript], { type: 'application/javascript' });
            const worker = new Worker(window.URL.createObjectURL(workerBlob));
            if (worker) {
                resolve(worker);
            } else {
                resolve(null);
            }
        })
    }
    private constructMediaUploadWorker = async (): Promise<Worker | null> => {
        return new Promise<Worker | null>((resolve, reject) => {
            const workerScript = `
            self.onmessage = async function (event) {
                if (event.data.eventName === '${EventsSentToWorkers.s3Upload}') {
                    try {
                        const { url, token } = event.data.urlParameters;

                        const response = await fetch(url, {
                            method: "POST",
                            headers: {
                                "Content-Type": 'application/json',
                                "Authorization": 'Bearer ' + token
                            },
                            body: JSON.stringify(event.data.apiPayload)
                        });
                        
                        if (response.ok) {
                            const actualResponse =  await response.json()
                            if(!actualResponse.error) {
                                self.postMessage({ s3UploadStatus: true, s3UploadResponse: actualResponse?.responseData?.fileName});
                            } else {
                                self.postMessage({ s3UploadStatus: false, s3UploadResponse: undefined });
                            }
                        } else {
                            self.postMessage({ s3UploadStatus: false, s3UploadResponse: undefined });
                        }
                    } catch (error) {
                        self.postMessage({ s3UploadStatus: false, s3UploadResponse: undefined })
                    }
                } else if (event.data.eventName === '${EventsSentToWorkers.saveMediaDb}') {
                    try {
        
                        const { url, token } = event.data.urlParameters;

                        const response = await fetch(url, {
                            method: "POST",
                            headers: {
                                "Content-Type": 'application/json',
                                "Authorization": 'Bearer ' + token
                            },
                            body: JSON.stringify(event.data.apiPayload)
                        });
            
                        if (response.ok) {
                            const actualResponse =  await response.json();
                            if(!actualResponse.error) {
                                self.postMessage({ saveMediaStatus: true, saveMediaResponse: actualResponse?.responseData[0][0]?.MediaID });
                            } else {
                                self.postMessage({ saveMediaStatus: false, saveMediaResponse: undefined });
                            }
                        } else {
                            self.postMessage({ saveMediaStatus: false, saveMediaResponse: undefined });
                        }
                    } catch (error) {
                        self.postMessage({ saveMediaStatus: false, saveMediaResponse: undefined });
                    }
                }

            };
            
        `
            const workerBlob = new Blob([workerScript], { type: 'application/javascript' });
            const worker = new Worker(window.URL.createObjectURL(workerBlob));
            if (worker) {
                resolve(worker);
            } else {
                resolve(null);
            }
        })

    }


    /* Method to Start Master Worker */
    private initiateMasterExecution = async () => {
        this.master = await this.constructMaster();
        this.masterBusyStatus = true;
        this.master.postMessage({ masterWorkerEvent: EventsSentToWorkers.masterStart })
        this.master.addEventListener('message', (event) => {
            if (event.data.taskIdentifier === EventsReceivedFromWorkers.startBackgroundSync) {
                this.assignMediaUploadTaskToWorkers();
            }
        })
    }

    // Stops master worker execution. 
    toggleMasterExecution = () => {
        this.master.postMessage({ masterWorkerEvent: EventsSentToWorkers.masterStop });
        this.masterBusyStatus = false;
    }

    private initWorkerPool = async () => {
        // Construct Worker pool for S3 Upload
        for (let i = 0; i < this.workers; i++) {
            this.workerPool.push({ worker: await this.constructMediaUploadWorker(), available: true })
        }

    }


    /* Method to fetch available 'Save Media' worker */
    protected getAvailableMediaUploadWorker = () => {
        return this.workerPool.find((worker) => worker.available);
    }

    /**
     * Generates the URL and token parameters for making an API request based on the task information.
     * @param { string } taskIdentifier  
    */
    private generateRequestConfig = (taskIdentifier: string) => {
        let token: string;
        if (!!(sessionStorage.getItem('access-token'))) {
            token = (sessionStorage.getItem('access-token'));
        }
        const url = environment.baseUrl + (taskIdentifier === this.IndexedDBService.s3UploadTaskIdentifier ? 'image/imageUpload' : 'media/mediaInsert');
        const urlParameters = { url, token };
        return urlParameters;
    }

    protected assignMediaUploadTaskToWorkers = async () => {
        // Retrive an availale 'Media Upload' worker
        const availableWorker = this.getAvailableMediaUploadWorker();
        if (availableWorker) {
            /*
             1. Check if there is a task available to be executed.
 
             2. If a task is available:
                a. Assign the task to an upload worker.
                b. Update the status of the assigned worker to 'false' (indicating Not Available).  
            */
            const taskInformation = await this.IndexedDBService.updateBackgroundSyncQueues();
            if (taskInformation) {
                availableWorker.available = false;
                const urlParameters = this.generateRequestConfig(taskInformation.taskIdentifier);
                const mediaObject: IIndexedDBMedia = await this.IndexedDBService.fetchMedia(IndexedDBCollections.imageUploadCollection, taskInformation.mediaIdentifier);
                const mediaToUpload: any = !taskInformation.isAttachment ? mediaObject?.mediaInformation : mediaObject.mediaInformation.attachments[taskInformation.attachmentIndex];
                taskInformation.taskIdentifier === this.IndexedDBService.s3UploadTaskIdentifier ? this.uploadToS3(availableWorker, taskInformation, mediaToUpload, urlParameters) : this.saveMediaToDb(availableWorker, taskInformation, mediaToUpload, urlParameters);
            }
        }
    }

    /**
     *  Method to Upload Image to AWS S3 Bucket
     * @param { Iworker } availableWorker
     * @param { Task } taskInforamtion
        1. Retrieve complete media information based on a specified 'propertyID'.
            - This step involves fetching all media details associated with the provided 'propertyID'.

        2. Filter the media based on the task information provided as an argument.
        3. Perform the required operation based on the taskInformation
            if taskInformation.taskIdentifier === this.IndexedDBService.s3UploadTaskIdentifier:
                perform uploadToS3() operation
            else:
                perform saveMediaToDb() operation
    */
    protected uploadToS3 = (availableWorker: Iworker, taskInformation: Task, mediaToUpload: IMediaInfo, urlParameters: { url: string; token: string; }) => {
        const apiPayload = { fileName: mediaToUpload.fileName, base64: mediaToUpload.media.UploadPathURL };
        availableWorker.worker.postMessage({ eventName: EventsSentToWorkers.s3Upload, apiPayload, urlParameters });
        availableWorker.worker.onmessage = async (event) => {

            const s3UploadStatus: boolean = event.data.s3UploadStatus;
            const uploadedImagePath: string = event.data.s3UploadResponse;
            if (s3UploadStatus) {
                // Update the 'uploaded image path within the 'taskInformation' object before saving it to the database
                taskInformation.path = uploadedImagePath;

                // Set the taskIdentifier to 'save-media-db' after successfully uploading the image to Amazon S3
                taskInformation.taskIdentifier = this.IndexedDBService.saveMediaTaskIdentifier;

                // Remove the completed 's3-upload' task from the 'pending' queue
                this.IndexedDBService.updatePendingQueue(true, taskInformation);
                // Move the updated task to the 'background-sync-queue'
                await this.IndexedDBService.moveToBackgroundTaskQueue(taskInformation);
                // Mark the worker as available for task assignment
                availableWorker.available = true;
            } else {
                const uploadFailureEvent = new CustomEvent(MediaUploadEvents.failure, {
                    detail: {
                        propertyID: taskInformation.mediaIdentifier.split('-')[0],
                        filename: taskInformation.isAttachment ? mediaToUpload.media.File.name : mediaToUpload.fileObject.file.name,
                        uniqueFilename: !taskInformation.isAttachment ? mediaToUpload.fileName : mediaToUpload.attachments[taskInformation.attachmentIndex].fileName
                    }
                })
                document.dispatchEvent(uploadFailureEvent);
            }
        }


    }

    protected saveMediaToDb = (availableWorker: Iworker, taskInformation: Task, mediaToUpload: IMediaInfo, urlParameters: { url: string; token: string; }) => {
        // Fix this apiPayload for saveMedia operation take reference from 'mediaService.SaveMediaToDatabase()'
        // also before making the request update the path from the taskInformation.path 
        // need to store the 'MediaId' comming as response after the saveMedia operation 
        // so that before saving the attachments to the database the P_RelationID will be populated
        const apiPayload = {
            P_MediaID: mediaToUpload?.media.MediaID
            , P_MediaName: mediaToUpload?.media.MediaName
            , P_Height: mediaToUpload?.media.Height
            , P_Width: mediaToUpload?.media.Width
            , P_Size: mediaToUpload?.media.Size
            , P_Path: taskInformation.path
            , P_Ext: mediaToUpload?.media.Ext
            , P_Description: mediaToUpload?.media.Description
            , P_EntityID: mediaToUpload?.media.CreatedBy
            , P_MediaRelationTypeID: mediaToUpload?.media.RelationshipTypeID
            , P_RelationID: taskInformation.mediaID ? taskInformation.mediaID : mediaToUpload?.media.RelationID
            , P_MediaTypeID: mediaToUpload?.media.MediaTypeID
            , P_MediaSubTypeID: mediaToUpload?.media.MediaSubTypeID
            , P_PropertyID: mediaToUpload?.media.PropertyID
            , P_IsDefault: mediaToUpload?.media.IsDefault
            , P_IsOwnMedia: mediaToUpload?.media.IsOwnMedia
            , P_MediaSourceID: mediaToUpload?.media.MediaSourceID
            , P_SourceComments: mediaToUpload?.media.SourceComments
            , P_ChangeLogJSON: mediaToUpload?.media.ChangeLogJSON
            , P_ApplicationID: mediaToUpload?.media.ApplicationID
        }
        availableWorker.worker.postMessage({ eventName: EventsSentToWorkers.saveMediaDb, apiPayload, urlParameters });
        availableWorker.worker.onmessage = async (event) => {
            const saveMediaStatus: boolean = event.data.saveMediaStatus;
            const MediaID = event.data.saveMediaResponse;

            if (saveMediaStatus) {
                // Update the MediaID 
                taskInformation.mediaID = MediaID
                // Check if there are any attachments associated with the main image
                if (!(taskInformation.isAttachment) && mediaToUpload.attachments.length > 0) {
                    // Update the media upload map
                    const mediaCount = IndexedDBService.uploadMediaMap.get(taskInformation.mediaIdentifier) || 1;
                    IndexedDBService.uploadMediaMap.set(taskInformation.mediaIdentifier, mediaCount + mediaToUpload.attachments.length);

                    // Move each attachment to the 'background-sync-queue' for subsequent uploading to AWS S3 Bucket
                    for (let attachment = 0; attachment < mediaToUpload.attachments.length; attachment++) {
                        await this.IndexedDBService.moveToBackgroundTaskQueue({ taskID: this.IndexedDBService.generateUniqueTaskID(), taskIdentifier: this.IndexedDBService.s3UploadTaskIdentifier, mediaIdentifier: taskInformation.mediaIdentifier, isAttachment: true, attachmentIndex: attachment, path: undefined, mediaID: MediaID });
                        const uploadStartEvent = new CustomEvent(MediaUploadEvents.pending, {
                            detail: {
                                propertyID: taskInformation.mediaIdentifier.split('-')[0],
                                filename: mediaToUpload.attachments[attachment].media.File.name,
                                uniqueFilename: mediaToUpload.attachments[attachment].fileName
                            }
                        });
                        document.dispatchEvent(uploadStartEvent);
                    }
                }


                const mediaCount = IndexedDBService.uploadMediaMap.get(taskInformation.mediaIdentifier.trim());

                if (mediaCount && (mediaCount - 1 > 0)) {
                    IndexedDBService.uploadMediaMap.set(taskInformation.mediaIdentifier, mediaCount - 1);
                } else {
                    IndexedDBService.uploadMediaMap.delete(taskInformation.mediaIdentifier);
                    this.IndexedDBService.removeMedia(taskInformation.mediaIdentifier);
                }

                // Remove the completed 'save-media-db' task from the 'pending' queue
                this.IndexedDBService.updatePendingQueue(true, taskInformation);

                // Mark the worker as available for task assignment
                availableWorker.available = true;
                const uploadCompleteEvent = new CustomEvent(MediaUploadEvents.success, {
                    detail: {
                        propertyID: taskInformation.mediaIdentifier.split('-')[0],
                        filename: taskInformation.isAttachment ? mediaToUpload.media.File.name : mediaToUpload.fileObject.file.name,
                        uniqueFilename: mediaToUpload.fileName
                    }
                });
                document.dispatchEvent(uploadCompleteEvent);
            } else {
                const uploadFailureEvent = new CustomEvent(MediaUploadEvents.failure, {
                    detail: {
                        propertyID: taskInformation.mediaIdentifier.split('-')[0],
                        filename: taskInformation.isAttachment ? mediaToUpload?.media.File.name : mediaToUpload?.fileObject.file.name,
                        uniqueFilename: mediaToUpload?.fileName
                    }
                })
                document.dispatchEvent(uploadFailureEvent);
            }
        }
    }
}
