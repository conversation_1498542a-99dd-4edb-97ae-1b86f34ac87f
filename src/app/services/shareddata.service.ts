import { Injectable } from '@angular/core';
import { mapEditPropertyDTO } from '../DTO/mapEditPropertyDTO';
import { Media } from '../models/MediaType';
import { PropertySearchResult } from '../models/PropertySearchResult';
import { environment } from '../../environments/environment';

@Injectable()
export class SharedDataService {

    private _quadrants: any;
    private _prefix: any;
    private _buildingClass: any;
    private _suffix: any;
    private _condoType: any;
    private _complexType: any;
    private _rooftopsource: any;
    private _propertyType: any;
    private _propertyList: any;
    private _propertyListCopy: any;
    private _constructStatus: any;
    private _allroofType: any;
    private _sizesource: any;
    private _constructType: any;
    private _country: any;
    private _zoningclass: any;
    private _allSpecificUse:any;
    private _researchstatus:any;
    private _specificUse: any;
    private _sprinklerTypes:any;
    private _NABERSList: any;
    private _propertyID: any;
    private _propertyMarker: any;
    private _mapSearchPropertyList: any;
    private mapEditSharedObject: mapEditPropertyDTO;
    private _fromPropertyView: any;
    private _propertyParcelDetails: any;
    private _listingDetails: any;
    private _propertyMedia: any;
    private _additionalAddressList: any;
    private _selectPropertyParcel: any;
    private _propertyResearchStatus: any;
    private _selectedState: any;
    private _cities: any;
    private _counties: any;
    private _stateList: any;
    private _mediaTypes: any;
    private _mediaSubTypes: any;
    private _mediaSource: any;
    private _selectedUseType: any;
    private _selectedFloor:any;
    private _mediaPath: Media = new Media();
    private _selectedlisting: any;
    private _postalCode: any;
    private _searchCriteria: any;
    private _propertySearchResults: PropertySearchResult;
    private _propertyResearchTypes: any;
    private _parcelInfo: any;
    private _buildingFootPrintDataToDelete: any;
    private _deleteBuildingFloorFootprintId:any[]=[];
    private _buildingFootPrintId: any;
    private _clearPolygon: boolean;
    private _deleteBuildingFootPrintIds: any[];
    private _maxZoom: number | null = null;
    private _AzureMapURL:string = environment.AzureMapBaseURL;
    private _isAzureMapOn:boolean = false;
    private _checkedList:string[] = [];
    private _currentZoomLevel:number | null = null;
    private _lookupDropdowns: any;
    constructor() {
    }



        // All quadrants.
        get sprinklerTypes(): any {
            return this._sprinklerTypes;
        }
        set sprinklerTypes(_sprinklerTypes: any) {
            this._sprinklerTypes = _sprinklerTypes;
        }

    // All quadrants.
    get quadrantList(): any {
        return this._quadrants;
    }
    set quadrantList(_quadrantList: any) {
        this._quadrants = _quadrantList;
    }
    // All prefixes.
    get prefixList(): any {
        return this._prefix;
    }
    set prefixList(_prefixList: any) {
        this._prefix = _prefixList;
    }

    // All building class.
    get buildingClassList(): any {
        return this._buildingClass;
    }
    set buildingClassList(_buildingClassList: any) {
        this._buildingClass = _buildingClassList;
    }

    // All Suffixes.
    get suffixList(): any {
        return this._suffix;
    }
    set suffixList(_suffixList: any) {
        this._suffix = _suffixList;
    }

    // All CondoType.
    get condoTypeList(): any {
        return this._condoType;
    }
    set condoTypeList(_condoTypeList: any) {
        this._condoType = _condoTypeList;
    }

    // All complex type..
    get complexTypeList(): any {
        return this._complexType;
    }
    set complexTypeList(_complexTypeList: any) {
        this._complexType = _complexTypeList;
    }

    // All rooftop source.
    get roofTopSourceList(): any {
        return this._rooftopsource;
    }
    set roofTopSourceList(_rooftopsourceList: any) {
        this._rooftopsource = _rooftopsourceList;
    }

    // All property types;.
    get propertyTypeList(): any {
        return this._propertyType;
    }
    set propertyTypeList(_propertyTypeList: any) {
        this._propertyType = _propertyTypeList;
    }

    get propertyList(): any {
        return this._propertyList;
    }
    set propertyList(results: any) {
        this._propertyList = results;
    }
    get propertyListCopy(): any {
        return this._propertyListCopy;
    }
    set propertyListCopy(results: any) {
        this._propertyListCopy = results;
    }

    // All construction status.
    get constructStatusList(): any {
        return this._constructStatus;
    }
    set constructStatusList(_constructStatusList: any) {
        this._constructStatus = _constructStatusList;
    }
    // All roof type.
    get roofTypeList(): any {
        return this._allroofType;
    }
    set roofTypeList(_allroofTypeList: any) {
        this._allroofType = _allroofTypeList;
    }
    // All size source.
    get sizeSourceList(): any {
        return this._sizesource;
    }
    set sizeSourceList(_sizesourceList: any) {
        this._sizesource = _sizesourceList;
    }

    // All construction types.
    get constructTypeList(): any {
        return this._constructType;
    }
    set constructTypeList(_constructTypeList: any) {
        this._constructType = _constructTypeList;
    }

    // All countries.
    get allCountryList(): any {
        return this._country;
    }
    set allCountryList(_countryList: any) {
        this._country = _countryList;
    }

    // All zoning class.
    get zoningClassList(): any {
        return this._zoningclass;
    }
    set zoningClassList(_zoningclassList: any) {
        this._zoningclass = _zoningclassList;
    }

    get mediaTypes(): any {
        return this._mediaTypes;
    }
    set mediaTypes(type: any) {
        this._mediaTypes = type;
    }
    get mediaSubTypes(): any {
        return this._mediaSubTypes;
    }
    set mediaSubTypes(subType: any) {
        this._mediaSubTypes = subType;
    }
    get mediaSource(): any {
        return this._mediaSource;
    }
    set mediaSource(source: any) {
        this._mediaSource = source;
    }
    get mediaData(): Media {
        return this._mediaPath;
    }
    set mediaData(path: Media) {
        this._mediaPath = path;
    }
    get selectedListing(): any {
        return this._selectedlisting;
    }
    set selectedListing(listing: any) {
        this._selectedlisting = listing;
    }
    //  All specific uses.
      get allSpecificUseList(): any {
        return this._allSpecificUse;
    }
    set allSpecificUseList(_allSpecificUseList: any) {
        this._allSpecificUse = _allSpecificUseList;
    }
// All research status.
    get researchStatusList(): any {
        return this._researchstatus;
    }
    set researchStatusList(_researchstatusList: any) {
        this._researchstatus = _researchstatusList;
    }

    get NABERSList(): any {
        return this._NABERSList;
    }
    set NABERSList(NABERSList: any) {
        this._NABERSList = NABERSList;
    }

    get propertyID(): any {
        return this._propertyID;
    }
    set propertyID(value: any) {
        this._propertyID = value;
    }
    get buildingFootPrintId(): any {
        return this._buildingFootPrintId;
    }
    set buildingFootPrintId(value: any) {
        this._buildingFootPrintId = value;
    }
    get buildingFootPrintDataToDelete(): any {
        return this._buildingFootPrintDataToDelete;
    }
    set buildingFootPrintDataToDelete(value: any) {
        this._buildingFootPrintDataToDelete = value;
    }
    get clearPolygon(): any {
        return this._clearPolygon;
    }
    set clearPolygon(value: any) {
        this._clearPolygon = value;
    }
    get propertyMarker(): any {
        return this._propertyMarker;
    }
    set propertyMarker(value: any) {
        this._propertyMarker = value;
    }

    get mapSearchPropertyList(): any {
        return this._mapSearchPropertyList;
    }
    set mapSearchPropertyList(property: any) {
        this._mapSearchPropertyList = property;
    }

    get mapEdit(): mapEditPropertyDTO {
        return this.mapEditSharedObject;
    }
    set mapEdit(value: mapEditPropertyDTO) {
        this.mapEditSharedObject = value;
    }

    get fromPropertyView(): any {
        return this._fromPropertyView;
    }
    set fromPropertyView(_fromPropertyView: any) {
        this._fromPropertyView = _fromPropertyView;
    }

    get propertyParcelDetails(): any {
        return this._propertyParcelDetails;
    }
    set propertyParcelDetails(parcel: any) {
        this._propertyParcelDetails = parcel;
    }
    get listingDetails(): any {
        return this._listingDetails;
    }
    set listingDetails(listing: any) {
        this._listingDetails = listing;
    }
    get propertyMedia(): any {
        return this._propertyMedia;
    }
    set propertyMedia(media: any) {
        this._propertyMedia = media;
    }
    get additionalAddressList(): any {
        return this._additionalAddressList;
    }
    set additionalAddressList(address: any) {
        this._additionalAddressList = address;
    }
    get selectedPropertyParcel(): any {
        return this._selectPropertyParcel;
    }
    set selectedPropertyParcel(parcel: any) {
        this._selectPropertyParcel = parcel;
    }
    get propertyResearchStatus(): any {
        return this._propertyResearchStatus;
    }
    set propertyResearchStatus(research: any) {
        this._propertyResearchStatus = research;
    }
    get selectedState(): any {
        return this._selectedState;
    }
    set selectedState(state: any) {
        this._selectedState = state;
    }
    get cities(): any {
        return this._cities;
    }
    set cities(city: any) {
        this._cities = city;
    }
    get counties(): any {
        return this._counties;
    }
    set counties(county: any) {
        this._counties = county;
    }
    get stateList(): any {
        return this._stateList;
    }
    set stateList(states: any) {
        this._stateList = states;
    }
    get selectedUseType(): any {
        if (!this._selectedUseType && localStorage.getItem('selectedUseType') != null && localStorage.getItem('selectedUseType') !== 'undefined') {
            this._selectedUseType = JSON.parse(localStorage.getItem('selectedUseType'));
        }
        return this._selectedUseType;
    }
    set selectedUseType(type: any) {
        this._selectedUseType = type;
        localStorage.setItem('selectedUseType', JSON.stringify(type));
    }

    get selectedFloor(): any {
        if (!this._selectedFloor && localStorage.getItem('selectedFloor') != null && localStorage.getItem('selectedFloor') !== 'undefined') {
            this._selectedFloor = JSON.parse(localStorage.getItem('selectedFloor'));
        }
        return this._selectedFloor;
    }
    set selectedFloor(floor: any) {
        this._selectedFloor = floor;
        localStorage.setItem('selectedFloor', JSON.stringify(floor));
    }
    get specificUseList(): any {
        return this._specificUse;
    }
    set specificUseList(_specificUseList: any) {
        this._specificUse = _specificUseList;
    }
    get searchPostalCode(): any {
        return this._postalCode;
    }
    set searchPostalCode(postalCode: any) {
        this._postalCode = postalCode
    }
    get searchCriteria(): any {
        return this._searchCriteria;
    }
    set searchCriteria(criteria: any) {
        this._searchCriteria = criteria;
    }
    get searchResults(): any {
        return this._propertySearchResults;
    }
    set searchResults(results: any) {
        this._propertySearchResults = results;
    }
    get researchTypeList(): any {
        return this._propertyResearchTypes;
    }
    set researchTypeList(results: any) {
        this._propertyResearchTypes = results;
    }
    get parcelInfoPickedFromTileLayer(): any {
        return this._parcelInfo;
    }
    set parcelInfoPickedFromTileLayer(results: any) {
        this._parcelInfo = results;
    }
    get deleteBuildingFootPrintIds(): any[] {
        return this._deleteBuildingFootPrintIds ? this._deleteBuildingFootPrintIds : [];
    }
    set deleteBuildingFootPrintIds(results: any[]) {
        this._deleteBuildingFootPrintIds = results;
    }
    get maxZoom() {
        return this._maxZoom;
    }
    set maxZoom(maxZoom:number | null) {
        this._maxZoom = maxZoom;
    }

    get AzureMapURL() {
        return this._AzureMapURL;
    }

    set AzureMapURL(azureMapURL: string) {
        this._AzureMapURL = azureMapURL;
    }

    get IsAzureMapOn() {
        return this._isAzureMapOn;
    }
    set IsAzureMapOn(isOn:boolean) {
        this._isAzureMapOn = isOn;
    }

    get checkedList() {
        return this._checkedList;
    }

    set checkedList(checkedList: string[]) {
        this._checkedList = checkedList;
    }

    get currentZoomLevel() {
        return this._currentZoomLevel;
    }

    set currentZoomLevel(currentZoom:number | null) {
        this._currentZoomLevel = currentZoom;
    }

    getLookupDropdowns() {
        return this._lookupDropdowns;
    }

    setLookupDropdowns(lookup: any) {
        this._lookupDropdowns = lookup;
    }

    yesNoList = [{ID:0,Item:"No"},{ID:1,Item:"Yes"}];
}
