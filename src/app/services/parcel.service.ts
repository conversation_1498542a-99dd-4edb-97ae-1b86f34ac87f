import { Injectable } from '@angular/core';
import { ApiBaseService } from './api-base.service';

@Injectable()
export class ParcelService extends ApiBaseService  {
 
  public GetParcelShape(latitude: number, longitude: number){
    const response = this.httpGet(`${this._serviceURL}/gis/parcelShapes/${latitude}/${longitude}`);
    return response;
}

public getParcelShapeTiles(SWLat, SWLng, NELat, NELng) {
  const response = this.httpGet(this._serviceURL + 'gis/parcelShapeTiles/' + SWLat + '/' + SWLng + '/' + NELat + '/' + NELng);
  return response;
}
}