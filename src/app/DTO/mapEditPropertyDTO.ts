import { LatLng } from "../modules/map-module/models/LatLng";
import { Parcel } from "../models/Parcel";
import { Property } from "../models/Property";
import { PropertyLocation } from "../models/PropertyLocation";

export class mapEditPropertyDTO{
    latLng: LatLng;
    propertyId: number;
    locationData: any;
    parcels:Parcel[]=[];
    selectedParcel:Parcel;
    isNewProperty:boolean;
    parcelProperties :any;
    shapes:Parcel[]=[];
    address: any;
    fromMasterStrata: boolean;
    masterStrataObj: masterStrataDTO;
}

export class masterStrataDTO {
    property: Property;
    location: PropertyLocation;
    minStrataUnit: any;
    isMultiStrata: boolean;
    strataList: any[];
    isFreehold: boolean;
}

