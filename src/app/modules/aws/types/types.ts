export enum ContainerEvents {
  Upload,
  Cancel,
  Delete
}

export enum FileTypes {
  WORD = 'docx',
  DOC = 'doc',
  EXCEL = 'xlsx',
  PDF = 'pdf',
  JPG = 'jpg',
  JPEG = 'jpeg',
  PNG = 'png',
  TEXT = 'txt',
  CSV = 'csv'
}

export enum FileObjectStatus {
  NotStarted,
  Uploading,
  Uploaded,
  Canceled,
  Deleted,
  Failed
}

export class FileObject {
  propertyId:number;
  personId:number;
  EntityID:number;
  mediaId:number;
  status = FileObjectStatus.NotStarted;
  guid : any;
  fileName : any;
  MediaTypeId:number = 0 ;
  MediaSubTypeId:number = 0;
  Description:any = "";
  IsDefault:boolean;
  IsSaved:boolean;
  FileName:string;
  URL:any;
  Height:any;
  Width:any;
  ThumbURL:string;
  ThumbFile:Blob;
  constructor(public file: File) { 

    this.guid = this.generateUUID();
    this.fileName = this.guid+"."+this.getExtension();
  }
  

  generateUUID() {
    var d = new Date().getTime();
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (d + Math.random()*16)%16 | 0;
        d = Math.floor(d/16);
        return (c=='x' ? r : (r&0x3|0x8)).toString(16);
    });
    return uuid;
}

getExtension()
{
  return this.file.name.split('.').pop();
}

}

export interface S3ConfigParams {
  bucketName: string;
  folderPath?: string;
}

export class S3Config {
  bucketName: string;
  folderPath?: string;
  constructor(params: S3ConfigParams) {
    this.bucketName = params.bucketName;
    // Making sure folderPath ends with only one /
    this.folderPath = params.folderPath ? params.folderPath.replace(/\/*$/, '/') : '';
  }
}
