import { Component, OnInit, ChangeDetectorRef, ElementRef, Renderer2 } from '@angular/core'
import { MediaUploadEvents } from '../../../enumerations/background-upload-worker'

interface IMediaInformation {
    propertyID: number,
    filename: string,
    uniqueFilename: string,
    uploadStatus: string
}

@Component({
    selector: 'backgroung-media-upload-toaster',
    templateUrl: './background-media-upload-toaster.component.html',
    styleUrls: ['./background-media-upload-toaster.component.scss']
})
export class BackgroundMediaUploadToasterComponent implements OnInit {

    isMinimizeButtonClicked: boolean = false;
    isVisible: boolean = false;
    totalMediaCount: number = 0;
    totalUplodedMediaCount: number = 0;
    totalFailureCount: number = 0;
    enableBackgroundMediaUploadToaster: boolean = false;
    mediaArray: Array<IMediaInformation> = [];

    constructor( private cdr: ChangeDetectorRef ) { }
    ngOnInit(): void {

        // Set up event listeners to listen for media upload events.

        // Event triggered upon media upload
        document.addEventListener(MediaUploadEvents.pending, (event: CustomEvent) => this.mediaUploadPending(event))

        // Event triggered upon successful media upload.
        document.addEventListener(MediaUploadEvents.success, (event: CustomEvent) => this.mediaUploadSuccess(event))

        // Event triggered upon media upload failure.
        document.addEventListener(MediaUploadEvents.failure, (event: CustomEvent) => this.mediaUploadFailure(event))
    }

    ngOnDestroy(): void {
        document.removeEventListener(MediaUploadEvents.success, () => { });
        document.removeEventListener(MediaUploadEvents.pending, () => { });
    }


    // Minimize Button Handler
    minimizeHandler = () => {
        this.isMinimizeButtonClicked = !this.isMinimizeButtonClicked;
    }

    // Background Media Upload Toaster Visibility Handler
    visibilityHandler = (value: boolean) => {
        if (value !== this.isVisible) {
            this.isVisible = value;
        }
    }

    // Background Media Upload Toaster Close Handler
    closeToasterHandler = () => {
        this.enableBackgroundMediaUploadToaster = false;
        this.mediaArray = [];
        this.totalFailureCount = 0;
        this.totalUplodedMediaCount = 0;
        this.totalMediaCount = 0;
        this.cdr.detectChanges();
    }

    mediaUploadPending = (event: CustomEvent) => {

        const { propertyID, filename, uniqueFilename } = event.detail
        
        const mediaInformation: IMediaInformation = {
            propertyID: propertyID,
            filename: filename,
            uniqueFilename: uniqueFilename,
            uploadStatus: 'pending'
        }
        
        this.totalMediaCount += 1;
        this.mediaArray.push(mediaInformation);
        this.cdr.detectChanges();
    }

    mediaUploadSuccess = (event: CustomEvent) => {

        const { propertyID, uniqueFilename } = event.detail
        const index = this.mediaArray.findIndex(media => media.propertyID === propertyID && media.uniqueFilename === uniqueFilename)

        if (index !== -1) {
            this.mediaArray[index].uploadStatus = 'success';
            this.totalUplodedMediaCount += 1;
            if((this.totalFailureCount + this.totalUplodedMediaCount) === this.totalMediaCount) {
                this.enableBackgroundMediaUploadToaster = true;
            }
            this.cdr.detectChanges();
        }
    }

    mediaUploadFailure = ( event : CustomEvent ) => {
        const { propertyID, uniqueFilename } = event.detail;
        const index = this.mediaArray.findIndex(media => media.propertyID === propertyID && media.uniqueFilename === uniqueFilename);

        if(index !== -1) {
            this.mediaArray[index].uploadStatus = 'failure';
            this.totalFailureCount += 1;
            if((this.totalFailureCount + this.totalUplodedMediaCount) === this.totalMediaCount) {
                this.enableBackgroundMediaUploadToaster = true;
            }
            this.cdr.detectChanges();
        }
    }
}