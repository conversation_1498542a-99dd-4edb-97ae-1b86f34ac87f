$color: #f0ad4e;
.background-media-upload-main {
    display: flex;
    flex-direction: column;
    position: relative;
    width: inherit;
}

#info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--primary-lightColor);
}

.media-upload-status {
    position: relative;
    font-size: 0.95rem;
    left: 0.8rem;
}

.popup-controls {
    display: flex;
}

.action-button {
    border-radius: 50%;
    background: none !important;
    border: none;
    padding-inline: 0.35rem !important;
}

.action-button:hover {
    background: var(--color-ash) !important;
}

.action-button i {
    margin: 0.44rem;
}

.media-upload-container {
    flex-direction: column;
    position: relative;
    width: 100%;
    // border: 2px solid black;
    overflow-y: auto;
    background-color: #fff;
    padding: 0.2rem 0.2rem 0.2rem 0.2rem;
    box-shadow: 0rem 0.7rem 7px rgba(0, 0, 0, 0.3);
    &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--primary-ash);
        border-radius: 10px;
        height: 10px;
    }

    &::-webkit-scrollbar-track {
        background-color: #e4e4e4;
        border-radius: 25px;
    }
}

.media-item {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0.4rem;
    cursor: pointer;
}

.media-item .icon i {
    color: var(--color-ash);
    font-size: 0.9rem;
}

.name {
    white-space: nowrap;
    overflow-y: auto;
    width: 11rem;
    max-width: 12rem;
    font-size: 0.9rem;
    &::-webkit-scrollbar {
        display: none;
    }
}

.success i {
    color: var(--green-color);
    font-size: 0.9rem;
}

.failure i {
    color: var(--red-color);
    font-size: 0.9rem;
}

.pending {
    background-color: var(--orange-color);
    width: 0.5em;
    height: 0.5em;
    border-radius: 50%;
    box-shadow:  0 0 0 0.3em rgba($color, 0.3);
}
