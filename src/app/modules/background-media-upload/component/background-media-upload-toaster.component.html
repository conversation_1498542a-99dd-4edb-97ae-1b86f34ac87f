<div class="background-media-upload-main" *ngIf="totalMediaCount > 0">
    <div id="info-header">
        <div class="media-upload-status">{{totalUplodedMediaCount + '/' + totalMediaCount + ' upload complete' }}</div>
        <div class="popup-controls">
            <button class="action-button" (click)="minimizeHandler()">
                <i class="fas fa-angle-down" *ngIf="!isMinimizeButtonClicked"></i>
                <i class="fas fa-angle-up" *ngIf="isMinimizeButtonClicked"></i>
            </button>
            <button class="action-button" *ngIf="enableBackgroundMediaUploadToaster" (click)="closeToasterHandler()">
                <i class="fas fa-times" style="font-size: 0.9rem !important;"></i>
            </button>
        </div>
    </div>
    <div class="media-upload-container"
        [ngStyle]="{'max-height': !isMinimizeButtonClicked ? '0rem' : '15rem', 'display': (isMinimizeButtonClicked && totalMediaCount > 0 )? 'flex': 'none'}">
        <div class="media-item" *ngFor="let media of mediaArray" [attr.id]="media.propertyID+media.filename">
            <div class="icon">
                <i class="fas fa-file"></i>
            </div>
            <div class="name">{{ media.propertyID + ' ' + media.filename }}</div>
            <ng-container *ngIf="media.uploadStatus === 'success'; then successContent else notSucess"></ng-container>

            <!-- Template for when uploadStatus is success -->
            <ng-template #successContent>
                <div class="success">
                    <i class="fas fa-check-circle"></i>
                </div>
            </ng-template>

            <!-- Template for when uploadStatus is not success -->
            <ng-template #notSucess>
                <ng-container
                    *ngIf="media.uploadStatus === 'pending'; then pendingContent else failureContent"></ng-container>
            </ng-template>

            <!-- Template for when uploadStatus is pending -->
            <ng-template #pendingContent>
                <div class="pending"></div>
            </ng-template>


            <!-- Template for when uploadStatus is failure -->
            <ng-template #failureContent>
                <div class="failure">
                    <i class="fas fa-times-circle"></i>
                </div>
            </ng-template>
        </div>
    </div>
</div>
