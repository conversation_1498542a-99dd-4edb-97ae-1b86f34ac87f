import { LatLng } from "./LatLng";
import * as MapEnum from './MapEnum';
export class StreetViewParams{
    panoramaOptions : any;
    public MapType: MapEnum.MapType;
    public MapId: string;
    public ZoomLevel: number;
    public CenterLat: number;
    public CenterLng: number;
    public IsMapTypeChangable: boolean;
    public IsDraggable: boolean;
    public FullscreenControl: boolean;
    public RequireCtrlToZoom: boolean;
    public FeaturesToHide: Array<MapEnum.MapFeatures>;

    constructor(mapId: string) {
        this.MapId = mapId;
        this.MapType = MapEnum.MapType.Roadmap;
        this.ZoomLevel = 5;
        this.CenterLat = 32.7568257;
        this.CenterLng = -97.0215761;
        this.IsMapTypeChangable = false;
        this.IsDraggable = true;
        this.FullscreenControl = true;
        this.RequireCtrlToZoom = true;
        this.FeaturesToHide = new Array<MapEnum.MapFeatures>();
    }
}