import { NgModule }            from '@angular/core';
import { CommonModule }        from '@angular/common';

import {FileSizePipe}         from './../aws/utils/file-size.pipe';
import { MultiSelectDropdownComponent } from '../../pages/multi-select-dropdown/multi-select-dropdown.component';
import { FormsModule } from '@angular/forms';

@NgModule({
  imports:      [ CommonModule,FormsModule ],
  declarations: [ FileSizePipe ,MultiSelectDropdownComponent],
  exports:      [ FileSizePipe ,MultiSelectDropdownComponent]
})
export class SharedModule { }
