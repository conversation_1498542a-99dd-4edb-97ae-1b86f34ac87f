import { confirmSettings } from "./confirmSettings";

export class confirmConfiguration{
    Title?: string;
    Message: string;
    OkButton: confirmSettings;
    CancelButton: confirmSettings;
    AdditionalButton?: confirmSettings;

    constructor(){
        this.OkButton = new confirmSettings();
        this.OkButton.Label = "Ok";
        this.OkButton.Classname = 'btn btn-primary';
        this.OkButton.Callback = () => {};

        this.CancelButton = new confirmSettings();
        this.CancelButton.Label = "Cancel";
        this.CancelButton.Classname = 'btn btn-warning';
        this.CancelButton.Callback = () => {};
        this.CancelButton.Visible = true;

    }
}