import { Injectable } from '@angular/core';
import { confirmConfiguration } from '../models/confirmConfiguration';
import { CommonStrings } from '../../../constants';

declare var bootbox: any;
declare var $: any;
@Injectable()
export class NotificationService {

  constructor() { }

  ShowAlert(message: string, onClose?: () => any) {
    let options: any = {};
    options.closeButton = false;
    options.message = message;
    options.callback = onClose;
    options.animate = true;
    bootbox.alert(options);
  }

  Confirm(message: string, onResult: (result: boolean) => void) {
    let option: any = {};
    option.message = message;
    option.animate = true;
    option.callback = onResult;
    bootbox.confirm(option);
  }

  CustomDialog(configuration: confirmConfiguration) {
    let option: any = {};
    option.message = configuration.Message;
    option.title = configuration.Title;
    option.closeButton = false;
    option.animate = true;
    option.buttons = {};
    option.buttons.ok = {
      label: configuration.OkButton.Label ? configuration.OkButton.Label : CommonStrings.DialogConfigurations.ButtonLabels.Ok,
      className: configuration.OkButton.Classname,
      callback: configuration.OkButton.Callback
    }

    if (configuration.CancelButton.Visible != false) {
      option.buttons.cancel = {
        label: configuration.CancelButton.Label ? configuration.CancelButton.Label : CommonStrings.DialogConfigurations.ButtonLabels.Cancel,
        className: configuration.CancelButton.Classname,
        callback: configuration.CancelButton.Callback
      }

    }
    if (configuration.AdditionalButton != null) {
      option.buttons.noclose = {
        label: configuration.AdditionalButton.Label ? configuration.AdditionalButton.Label : CommonStrings.DialogConfigurations.ButtonLabels.No,
        className: configuration.AdditionalButton.Classname,
        callback: configuration.AdditionalButton.Callback
      }
    }
    bootbox.dialog(option);
  }

  ShowSuccessMessage(message) {
    this.showMessage('divSuccess', message, 'alert-success');
  }

  ShowErrorMessage(message) {
    this.showMessage('divError', message, 'alert-danger');
  }

  ShowWarningMessage(message) {
    this.showMessage('divWarning', message, 'alert-warning');
  }

  ShowInfoMessage(message) {
    this.showMessage('divWarning', message, 'alert-info');
  }

  private showMessage(id: string, message: string, className: string) {
    let jid = '#' + id;
    if ($(jid).length == 0) {
      $('html').append('<div id="' + id + '" class="' + className + ' custom-absolute-alert animated fadeInRight" style="display:none"></div>');
    }
    $(jid).fadeIn(600);
    $(jid).text(message);
    setTimeout(function () { $(jid).fadeOut(600) }, 2500);
  }
}
