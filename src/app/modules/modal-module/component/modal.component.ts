import { Component, EventEmitter, HostListener, Input, NgZone, Output, TemplateRef } from "@angular/core";

@Component({
    selector: "imperium-modal",
    templateUrl: './modal.component.html',
    styleUrls: ['./modal.component.scss']
})
export class ModalComponent {
    @Input() public title: string;
    @Input() public closeOnEscape: boolean = true;
    @Input() public closeOnClickOutside: boolean = true;
    @Input() public styleClass: string;
    @Input() public size: string;
    @Input() public width: string;
    @Input() public height: string = '';
    @Input() public bodyTemplate: TemplateRef<any>;
    @Output() public visibleChange = new EventEmitter();

    public backDropId: string = `backdrop-${(+new Date)}`;
    private _visible: boolean = false;

    constructor(private zone: NgZone) {
    }

    public get visible(): boolean {
        return this._visible;
    }

    @Input()
    public set visible(value: boolean) {
        this._visible = value;
    }

    @HostListener("window:keyup", ["$event"])
    public onKeyUp($event: KeyboardEvent): void {
        if ($event.keyCode === 27 && this.closeOnEscape) {
            this.close();
        }
    }

    public onModalClick($event: MouseEvent): void {
        this.zone.run(() => {
            this.visible = !this.closeOnClickOutside || ((<HTMLElement>$event.target).id !== this.backDropId && this.closeOnClickOutside);

            if (!this.visible) {
                this.visibleChange.emit(false);
            }
        });
    }

    public close($event: MouseEvent = null): void {
        if ($event) {
            $event.stopPropagation();
        }

        this.zone.run(() => {
            this.visible = false;
            this.visibleChange.emit(false);
        });
    }
}

/**
 Usage:
 <div *ngIf="<Visibility flag>">
    <intrepid-modal [(visible)]="<Visibility flag>" [title]="'<Title of the modal>'" [size]="<Modal width in px>" [bodyTemplate]="bodyTemplate">
        <ng-template #bodyTemplate>
            <!-- Place the popup content or component here  -->
        </ng-template>
    </intrepid-modal>
</div>
 */