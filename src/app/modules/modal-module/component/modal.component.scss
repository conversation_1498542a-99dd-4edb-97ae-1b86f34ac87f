.intrepid-modal {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1000;
    .modal-dialog {
        margin-top: 50px;
        animation: slideDown linear .250s;

        .modal-content {
            z-index: 998;
        }
    }

    .modal-backdrop {
        z-index: 997;
        opacity: .5;
    }

    .modal-backdrop {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: black;
      }

    .close{
        position: absolute;
        right: 10px;
    }
    .close:after{
        content: 'x'
    }
}
.modal-header{
    height: 50px!important;
}
.modal-body-x-medium {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 15px;
}

@keyframes slideDown {
    0% {
        transform: translate3d(0, -300%, 0);
    }
    100% {
        transform: translate3d(0, 0, 0);
    }    
}
.modal-xl {
    min-width: 75%;
}
.modal-md {
  max-width: 750px !important;
}

  /* ...................Model Popup  Media Queries................... */

  @media (min-width: 992px) {
    .modal-lg {
      max-width: 65% !important;
    }
    .model-xl-md {
      max-width: 65% !important;
    }
    .modal-xl {
      min-width: 75%;
    }
    .modal-body-small {
      height: 170px;
    }

    .modal-body-medium {
      height: 350px;
    }

    .modal-body-log {
      height: 275px;
    }

    .modal-body-x-sm {
      height: 230px;
    }

    .modal-body-x-medium {
      height: 400px;
    }

    .modal-body-media {
      height: 500px;
      // overflow: auto;
    }
  }

  @media (min-width:1500px) {
    .modal-lg {
      max-width: 65% !important;
    }
    .modal-xl {
      min-width: 75%;
    }
  }

  @media (min-width:1700px) {
    .modal-lg {
      max-width: 65% !important;
    }
    .modal-xl {
      min-width: 75%;
    }
    .model-xl-md {
      max-width: 800px !important;
    }
  }

  @media (min-width:1900px) {
    .modal-lg {
      max-width: 65% !important;
    }
    .model-xl-md {
      max-width: 45% !important;
    }
    .modal-xl {
      min-width: 75%;
    }
  }
