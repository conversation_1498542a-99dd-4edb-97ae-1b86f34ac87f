.preview-container {
    display: flex;
    flex-direction: column;
    height: 500px;
    width: 100%;
    font-size: 18px;
    color: black;
}

.map-wrapper {
    width: 100%;
}

.map-container {
    width: 93%;
    height: 85%;
}

.checkbox-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.checkbox-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.checkbox-container input {
    margin-right: 4px
}

.pinColorWrap {
    background-color: transparent;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    height: 40px;
    margin-top: 10px;
    margin-left: 0;
    padding: 0;
    z-index: 999;
    position: absolute;
    right: 25px;
}

.pinBox {
    background-color: white;
    display: flex;
    align-items: center;
    gap: 7px;
    height: 100%;
    padding: 0 8px;
    margin-right: 25px;
}

.floor-container {
    display: flex;
    gap: 1rem;
}

.header-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.header-container p {
    margin-bottom: 0px;
}
