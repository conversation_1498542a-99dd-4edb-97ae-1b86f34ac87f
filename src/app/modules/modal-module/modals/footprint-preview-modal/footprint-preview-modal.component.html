<div class="preview-container">
  <div class="header-container">
    <div class="floor-container">
      <p>Old floor - {{oldFloor}}</p>
      <p>New floor - {{newFloor}}</p>
    </div>
    <div class="checkbox-container">
      <div class="checkbox-wrapper"><input type="checkbox" [checked]="showNewFootprint"
          (change)="showHideNewFootprint(!showNewFootprint)" />
        <span>Show New Footprint</span>
      </div>
      <div class="checkbox-wrapper"><input type="checkbox" [checked]="showPreviousFootprint"
          (change)="showHidePreviousFootprint(!showPreviousFootprint)" />
        <span>Show Previous Footprint</span>
      </div>
    </div>
  </div>
  <div class="map-wrapper">
    <div id="propertyMap" style="position: absolute;" class="map-container"></div>
    <div class="pinColorWrap" id="legend">      
      <div class="pinBox">
        <span>
          <svg width="19" height="19">
            <circle cx="9" cy="9" r="9" stroke-width="4" [attr.fill]="'#fffb00'" />
            </svg>
        </span>
        <span class="label">Old Footprint</span>
  
        <span>
          <svg width="19" height="19">
            <circle cx="9" cy="9" r="9" stroke-width="4" [attr.fill]="'#0828e2'" />
            </svg>
        </span>
        <span class="label">New Footprint</span>
      </div>          
    </div>
  </div>
</div>
