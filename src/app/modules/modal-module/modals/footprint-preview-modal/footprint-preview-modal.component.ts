import { Component, Input, OnInit } from '@angular/core';
import { LatLng } from '../../../../modules/map-module/models/LatLng';
import { MapOptions } from '../../../../modules/map-module/models/MapOptions';
import { MapService } from '../../../../modules/map-module/service/map-service.service';
import { MapHelperService } from '../../../../services/map-helper.service';
import * as MapEnum from '../../../../modules/map-module/models/MapEnum';


@Component({
  selector: 'app-footprint-preview-modal',
  templateUrl: './footprint-preview-modal.component.html',
  styleUrls: ['./footprint-preview-modal.component.css']
})
export class FootprintPreviewModalComponent implements OnInit {

  @Input() location: LatLng;
  @Input() selectedFootprintLog: any;
  
  public mapOptions: MapOptions;
  public map: any;
  previousFootprint: any;
  newFootprint: any;
  oldLatLngList = [];
  newLatLngList = [];
  oldFloor: string;
  newFloor: string;
  showPreviousFootprint = true;
  showNewFootprint = true;
  showBuildingFootprintPreviewModal: boolean = false;
    constructor(private mapService: MapService, private mapHelperService: MapHelperService) {
    }

  ngOnInit() {
    this.initMap()
  }

  // Helper function to format the floor values
  formatFloor(floor: string): string {
    const trimmedFloor = floor.trim();
    return trimmedFloor.charAt(0) === trimmedFloor.charAt(2) ? trimmedFloor.charAt(0) : trimmedFloor;
  }

  initMap() {
    this.mapOptions = new MapOptions('propertyMap');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 9, 7, null, this.location.Latitude, this.location.Longitude);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.ZoomLevel = 20;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
    MapEnum.MapFeatures.ArterialRoad, MapEnum.MapFeatures.HighwayRoad,
    MapEnum.MapFeatures.LocalRoad, MapEnum.MapFeatures.ControlledAccessHighwayRoad,
    MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
    MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
    MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
    MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
    MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
    MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this.mapService.CreateMap(this.mapOptions);
    this.mapService.AddController(this.map, "legend", MapEnum.GoogleMapControlPosition.Top_Right);
    this.mapService.PlaceMarker(this.map, this.location.Latitude, this.location.Longitude);
    const oldValue = JSON.parse(this.selectedFootprintLog.OldValue);
    const newValue = JSON.parse(this.selectedFootprintLog.NewValue);

    // Extract latitude-longitude lists for old and new footprints
    this.oldLatLngList = oldValue.footprint && this.mapHelperService.GetLatLngListFromPolygon(oldValue.footprint);
    this.newLatLngList = newValue.footprint && this.mapHelperService.GetLatLngListFromPolygon(newValue.footprint);

    // Assign old and new floor values
    this.oldFloor = this.formatFloor(oldValue.Floor);
    this.newFloor = this.formatFloor(newValue.Floor);

    // Draw the previous footprint
    this.drawPreviousFootprint();
    // Draw the current footprint
    this.drawNewFootprint();
  }

  drawPreviousFootprint = () => {
    this.previousFootprint = this.oldLatLngList && this.mapService.DrawPolygonOnMap(this.map, this.oldLatLngList, this.previousFootprint, false, '#fffb00');
  }

  drawNewFootprint = () => {
    this.newFootprint = this.newLatLngList && this.mapService.DrawPolygonOnMap(this.map, this.newLatLngList, this.newFootprint, false,  '#0828e2');
  }

  showHideNewFootprint(showNewFootprint) {
    this.showNewFootprint = showNewFootprint;
    if (showNewFootprint) {
      this.drawNewFootprint();
    } else {
      this.newFootprint = this.newFootprint && this.mapService.ClearPolygons(this.newFootprint);
    }
  }
  showHidePreviousFootprint(showPreviousFootprint) {
    this.showPreviousFootprint = showPreviousFootprint;
    if (showPreviousFootprint) {
      this.drawPreviousFootprint();
    } else {
      this.previousFootprint = this.previousFootprint && this.mapService.ClearPolygons(this.previousFootprint);
    }
  }

}
