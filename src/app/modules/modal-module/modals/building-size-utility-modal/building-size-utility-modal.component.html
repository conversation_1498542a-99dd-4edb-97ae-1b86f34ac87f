<div class="propertymap" #aerialViewCapture
  [style.height]=" isBuildingModalInFullScreenMode ? '100vh' : isExpressMapInFullScreenMode ? '0vh' : '27em'"
  [style.width]=" isBuildingModalInFullScreenMode ? '100vw' : ''">
  <div class="searchBarBox" *ngIf="visible">
    <label>Area : {{areaSM | number: '.2-2'}} SqM / {{areaSF | number: '.2-2'}} SF</label>
    <!-- <a (click)="onSaveBuildingSize()"><img src="assets/images/tick.png"></a> -->
    <a (click)="clearShapes()"><img src="assets/images/Close.png"></a>
  </div>
  <div id="PropertyMap"  class="map" >
  </div>
  <!-- To display the deck.gl layers info -->
  <div id="layerInfoCardInBuildingModal" class="info-card-pos" [ngStyle]="{'margin-top': isBuildingModalInFullScreenMode? '35px' : '0px'}"></div>
  <div id="dropdownLayers"  class="layers-dropdown">
    <app-multi-select-dropdown [list]="list" [checkedList]="checkedList"
        (shareCheckedList)="shareCheckedList($event)">
    </app-multi-select-dropdown>
  </div>
  <!-- Full-screen-expand-icon -->
  <div class="btn-fs" id="fullScreenExpandBtn" (click)="goBuildingModalFullScreen()"
    [ngStyle]="{'display': isBuildingModalInFullScreenMode ? 'none' : 'inline'}">
    <i class="fas fa-expand fs-icon-pos"></i>
  </div>
  <!-- Full-screen-compress-icon -->
  <div id="fullScreenCompressBtn" class="btn-fs" (click)="exitBuildingModalFullScreen()"
    [ngStyle]="{'display': isBuildingModalInFullScreenMode ? 'inline' : 'none'}">
    <i class="fas fa-compress fs-icon-pos" aria-hidden="true"></i>
  </div>
   <!-- Camera-Icon -->
  <div id="aerialVieCam" class="cam-icon" title="Select map" (click)="onBuildingModalCameraClick()"
    [ngStyle]="{'display': isBuildingModalInFullScreenMode? 'flex' : 'none'}"><i
      class="fa fa-camera icon-pos"></i>
  </div>
  <!-- Capture-and-close-buuton -->
  <div id="saveAndCloseInBM" class="save-and-close-btn cam-icon"
    [ngStyle]="{'display': isBuildingModalInFullScreenMode ? 'flex' : 'none'}" (click)="saveAndClose()">
    Confirm & Exit</div>
    <button id="clearShapesInBM" class="clear-shapes-btn cam-icon"
      [disabled]="polygons == undefined || polygons[selectedFloorID] == undefined"
      [ngStyle]="{'display': (isBuildingModalInFullScreenMode && condoType != EnumCondoTypeNames.Master) ? 'flex' : 'none'}"
      (click)="clearPolygons()">
      Clear Polygon</button>
    <button id="clearAllShapesInBM" class="clear-all-shapes-btn cam-icon" [disabled]="!isPolygonsPresent()"
      [ngStyle]="{'display': (isBuildingModalInFullScreenMode && condoType != EnumCondoTypeNames.Master) ? 'flex' : 'none'}"
      (click)="clearAllPolygons()">
      Clear All Polygons</button>
  <!--for street view -->
   <!-- Full-screen-expand-icon -->
  <div class="btn-fs screen-mode-change-icons-pos street-view-btns-bg" (click)="goBuildingModalFullScreen()"
    [ngStyle]="{'display': (!isBuildingModalInFullScreenMode && isBuildingModalStreetViewVisible )  ? 'inline' : 'none'}">
    <i class="fas fa-expand fs-icon-pos fs-icon-pos-sv"></i>
  </div>
    <!-- Full-screen-compress-icon -->
  <div class="btn-fs screen-mode-change-icons-pos street-view-btns-bg" (click)="exitBuildingModalFullScreen()"
    [ngStyle]="{'display': (isBuildingModalInFullScreenMode && isBuildingModalStreetViewVisible)  ? 'inline' : 'none'}">
    <i class="fas fa-compress fs-icon-pos fs-icon-pos-sv" aria-hidden="true"></i>
  </div>
    <!-- Camera-Icon -->
  <div title="Select map" (click)="onBuildingModalCameraClick()" class="cam-icons-pos"
    [ngStyle]="{'display': (isBuildingModalInFullScreenMode && isBuildingModalStreetViewVisible) ? 'flex' : 'none'}">
    <i class="fa fa-camera icon-pos "></i>
  </div>
  <!-- Map Switch component -->
  <app-map-switch id="map-switch-building" [checkedList]="checkedList" [deckOverlay]="deckOverlay" [map]="map" (onAddMapSwitchBtnControler)="addControllers()" *ngIf="map"></app-map-switch>

   <!-- Capture-and-close-button -->
  <div class="save-and-close-btn save-btn-pos"
    [ngStyle]="{'display': (isBuildingModalInFullScreenMode && isBuildingModalStreetViewVisible) ? 'flex' : 'none'}"
    (click)="saveAndClose()">Confirm & Exit</div>
  </div>

<div id="floorlevelbuilding" class="floorLevel"
  [ngStyle]="{'display': (isBuildingModalInFullScreenMode && !isMasterStrataOrFreeHold())? 'flex' : 'none'}">
  <div class="row no-padding tabs-wrapper" style="background-color: white;">
    <div *ngIf="floorLabels && multiFloors.length > 0" style="display : flex; padding: 0">
      <div *ngFor="let item of multiFloors;let i=index">
        <div class="" *ngIf="item.minFloor && item.maxFloor">
          <div class="tab"
            [ngClass]="{highlight: currentTab(item.BuildingFootPrintID, item.localBuildingFootPrintID)}"
            (click)="selectFloor(item.BuildingFootPrintID, item.localBuildingFootPrintID)" *ngIf="item.minFloor != item.maxFloor">
            {{item.minFloor === 1 ? "G" : item.minFloor - 1}} - {{item.maxFloor ===
            1 ? "G" : item.maxFloor - 1}}</div>
          <div class="tab"
            [ngClass]="{highlight: currentTab(item.BuildingFootPrintID, item.localBuildingFootPrintID)}"
            (click)="selectFloor(item.BuildingFootPrintID, item.localBuildingFootPrintID)" *ngIf="item.minFloor == item.maxFloor">
            {{item.minFloor === 1 ? "G" : item.minFloor - 1}}</div>
        </div>
      </div>
    </div>
    <div class="tab" [ngClass]="{highlight: showAllPolygons}" (click)="showAllFootPrint()">
      Show All
    </div>
  </div>
</div>

<div id="selectedParcelsInfo" class="parcels-wrapper"
  [ngStyle]="{'display': (isBuildingModalInFullScreenMode && fromSelectParcels && selectedParcels.length > 0)? 'flex' : 'none'}">
  <div class="row no-padding parcels-info">
    <div class="parcel-title">Selected Parcels</div>
    <div *ngFor="let parcel of selectedParcels;let i=index" class="list-wrapper">
      <div>Parcel No: {{parcel.Parcel_No}}</div>
      <div>Lot Area: {{parcel.Lot_Area | number: '.2-2'}}</div>
      <hr class="mt-0 hr-tag" *ngIf="selectedParcels.length > 1 && i !== selectedParcels.length - 1">
    </div>
  </div>
</div>

<imperium-modal [(visible)]="showDeletePolygonModal" [title]="'Delete Polygon'" [bodyTemplate]="clearAllPolygon"
  [width]="'medium'">
  <ng-template #clearAllPolygon>
    {{title}}
    <div class="text-right">
      <button class="btn btn-primary start" (click)="deletePolygon()">
        <strong>Continue</strong>
      </button>
      <button class="btn btn-success cancel" (click)="close()">
        <strong>close</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>

 <!-- Modal for unhiding the selected hidden property to map to Master -->
<imperium-modal [(visible)]="isHiddenPropertySelected" [title]="'Hidden Pin'" [bodyTemplate]="hiddenPin"
  [width]="'medium'">
  <ng-template #hiddenPin>
    {{unHidePropertyMessage}}
    <div class="modal-buttons">
      <button class="btn btn-primary start" (click)="unHideProperty()">
        <strong>Yes</strong>
      </button>
      <button class="btn btn-success cancel" (click)="closeUnhideConfirmationPopup()">
        <strong>No</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>

<!-- Modal to show Parcel error -->
<imperium-modal [(visible)]="isPropertyFromDifferentParcel" [title]="'Error'" [bodyTemplate]="parcelError"
  [width]="'medium'">
  <ng-template #parcelError>
    {{propertyFromDifferentParcel}}
    <div class="text-right">
      <button class="btn btn-primary start" (click)="onParcelErrorPopupClose()">
        <strong>Ok</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>

<!-- Confirmation pop up to add buildings to master -->
<imperium-modal [(visible)]="addBuildingsToMaster" [title]="'Create Freehold Child Buildings'"
  [bodyTemplate]="mapBldngsToFreehold" [width]="'medium'">
  <ng-template #mapBldngsToFreehold>
    <div [innerHTML]="addBuildingsToMasterConfirmationMsg"></div>
    <div class="modal-buttons">
      <button class="btn btn-success start" (click)="onAddBuildingsToMaster()">
        <strong>Yes</strong>
      </button>
      <button class="btn btn-primary cancel" (click)="onCancel()">
        <strong>No</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>
<!-- Confirmation pop up to add parcels to property -->
<div *ngIf="showMultiParcelConfirmationModal">
  <imperium-modal [(visible)]="showMultiParcelConfirmationModal" [title]="'Multi Parcel Confirmation'"
    [bodyTemplate]="parcelsTemplate">
    <ng-template #parcelsTemplate>
      <app-multi-parcel-confirmation-modal
        (onSave)="onParcelsSave($event)"
        (onCancel)="onParcelsCancel($event)"
        [parcels]="selectedParcels"
      ></app-multi-parcel-confirmation-modal>
    </ng-template>
  </imperium-modal>
</div>

<div id="parcelsWarningMessage" class="message-wrapper alert-warning custom-absolute-alert"
  [ngStyle]="{'display': (isBuildingModalInFullScreenMode && showSelectedExistingParcelMessage)? 'block' : 'none'}">
  Selected an existing property parcel
</div>
