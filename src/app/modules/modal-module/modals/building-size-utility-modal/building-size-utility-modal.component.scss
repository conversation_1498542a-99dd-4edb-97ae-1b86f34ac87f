.propertymap{
    height: 40vh;
    width: 100%;
    transition: all .4s;    
    position: relative;   
}

.layers-dropdown{
    margin:10px 0px 10px 13px;
  }

.icon-pos {
    font-size: 40px;
    color: var(--primary-blue-color);
    cursor: pointer;
}

.cam-icon{
 margin-top: 10px; 
}

.cam-icons-pos{
    position:absolute;
    top:20px;
    right:70px;
    z-index: 2147483647;
}

.cam-icon-wrapper{
    display: flex;
    justify-content: flex-end;
    position:absolute;
    right:-60px;
    z-index: 20;
}

.canvasContainer {
    height: 27em;
    width: 100%;
    position: absolute; 
    background-color: transparent;
    top:0;
}
.searchBarBox{background: rgba(74, 74, 74, 0.73);
    right: 88px !important;
    top: 55px;
    padding: 5px;
    position: absolute;
    z-index: 2;
    span{margin-right: 15px; float: left;}
    label{float: left;margin: 5px 2px 0 5px;color:#fff; margin-right: 10px;}
    .btn{padding: 5px 0.75rem !important;}
}

.propertymap .map{
    height: 100%;
}
#searchbox {   
    font-size: 12px;    
    width: 250px;
    margin-top: 10px;
}

.layers-dropdown{
    margin-top:10px;
    margin-bottom:10px;
    margin-right:13px;
}

.btn-fs {
    background-color: white;
    padding: 7px;
    margin-right: 13px;
    position: absolute;
    z-index: 9999;
    top: 10px;
}

.fs-icon-pos {
    font-size: 23px;
    color: rgb(34, 34, 34);
    cursor: pointer;
}

.screen-mode-change-icons-pos {
    position: absolute;
    top: 20px;
    right: 13px;
    z-Index: 99;
}

.info-card-pos{
    margin-left:13px;   
}

.floorLevel{
    padding: 7px;
    gap: 5px;
    top:100px !important; 
    left:20px !important;
    right: auto !important;
    justify-content: center;
}
.save-and-close-btn, .clear-shapes-btn, .clear-all-shapes-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    border-radius: 10px;
    height: 40px;
    font-size: 20px !important;
    color: white;
    background-color: var(--primary-blue-color);
    margin-right: 10px;
    cursor: pointer;
    border: 0;
}
.save-and-close-btn {
    width: 165px;
}

.clear-shapes-btn {
    width: 155px !important
}
.clear-shapes-btn:disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: var(--primary-white);
    color: var(--btn-disabled-color);
    border-color: var(--btn-disabled-color);
}

.clear-all-shapes-btn:disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: var(--primary-white);
    color: var(--btn-disabled-color);
    border-color: var(--btn-disabled-color);
}

.clear-all-shapes-btn {
    width: 180px !important
}

.save-btn-pos {
    position: absolute;
    top: 20px;
    right: 110px;
    z-index: 2147483647;
}

.street-view-btns-bg {
    background-color: rgb(34, 34, 34) !important;
}

.fs-icon-pos-sv {
    color: #d3d3d3;
}

.fs-icon-pos-sv :hover {
    color: #fff !important;
}
.highlight {
    // background-color: #1e4b7b !important;
    background-color:var(--primary-blue-color) !important;
    color: white !important;
}
.floor-label-btn {
    color: white;
    background-color: var(--primary-blue-color);
    border: var(--primary-blue-color);
}
.btn-labels {
    display: flex;
    gap: 5px;
}
.tab {
    padding: 5px 15px;
    font-size: 14px;
    border: 1px dotted var(--primary-blue-color);
    cursor: pointer;
}
.tabs-wrapper>* {
    width: auto;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}
.parcels-wrapper {
    left: 30px !important;
}
.parcels-info {
    display: flex;
    background-color: white;
    flex-direction: column;
}
.parcel-title {
    display: flex;
    background-color: blue;
    color: white;
    font-size: 15px;
    padding: 5px;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.list-wrapper {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 5px;
    font-size: 13px;
    div {
        display: flex;
        white-space: wrap;
        width: 150px;
    }
}
.hr-tag {
    margin: 0;
    border-bottom: 1px solid black;
    margin-top: 5px !important;
}
.message-wrapper {
    font-size: 14px;
    margin-top: 100px;
    margin-left: 5%;
}
