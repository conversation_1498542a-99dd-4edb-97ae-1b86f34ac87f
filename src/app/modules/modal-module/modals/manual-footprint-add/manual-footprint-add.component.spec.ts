import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ManualFootprintAddComponent } from './manual-footprint-add.component';

describe('ManualFootprintAddComponent', () => {
  let component: ManualFootprintAddComponent;
  let fixture: ComponentFixture<ManualFootprintAddComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ManualFootprintAddComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManualFootprintAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
