.parkingmap {
  height: 40vh;
  width: 100%;
  transition: all .4s;
  position: relative;
}

.parkingmap .map {
  height: 100%;
}

.searchBarBox {
  background: rgba(74, 74, 74, 0.73);
  right: 88px !important;
  top: 55px;
  padding: 5px;
  position: absolute;
  z-index: 2;

  span {
    margin-right: 15px;
    float: left;
  }

  label {
    float: left;
    margin: 5px 2px 0 5px;
    color: #fff;
    margin-right: 10px;
  }

  .btn {
    padding: 5px 0.75rem !important;
  }
}

.icon {
  height: 25px;
  width: 25px;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
}

.layers-dropdown {
  position: absolute;
  top: 10px;
  right: 10px;
}

.info-card-pos {
  position: absolute;
  top: 60px;
  left: 10px;
}
