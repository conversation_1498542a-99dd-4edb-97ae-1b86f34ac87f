import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AreaMeasurementModalComponent } from './area-measurement-modal.component';

describe('AreaMeasurementModalComponent', () => {
  let component: AreaMeasurementModalComponent;
  let fixture: ComponentFixture<AreaMeasurementModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AreaMeasurementModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AreaMeasurementModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
