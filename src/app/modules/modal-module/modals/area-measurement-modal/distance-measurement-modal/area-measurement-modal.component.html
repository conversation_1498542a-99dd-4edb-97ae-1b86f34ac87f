<div class="row">
  <div class="col-sm-8">
    <div class="parkingmap" [style.display]="'block'"
      [style.height]="'60vh'" [style.width]="''">
      <div id="map-property-space" class="map"></div>
       <!-- To display the deck.gl layers info -->
       <div id="parcelInfoCard" class="info-card-pos"></div>
       <div id="dropdownLayers"  class="layers-dropdown">
         <app-multi-select-dropdown [list]="list" [checkedList]="checkedList"
             (shareCheckedList)="shareCheckedList($event)">
         </app-multi-select-dropdown>
       </div>
    </div>
  </div>
  <div class="col-sm-4 no-padding">
    <div class="form-group row">
      <label class="col-md-9 form-control-label" for="text-input">Area (SqM)
      </label>
      <div class="col-md-9 position-relative">
        <i class="fa fa-close icon" (click)="clearPolygon()"></i>
        <input type="text" disabled="true" class="form-control" [(ngModel)]="area">
      </div>
    </div>
    <input *ngIf="area" type="button" class="btn btn-primary" value="Save" (click)="onSave()" />
  </div>
</div>
