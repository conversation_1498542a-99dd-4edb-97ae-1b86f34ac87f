import { Component, OnInit, Input, OnDestroy, NgZone, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-image-viewer-modal',
    templateUrl: './image-viewer-modal.component.html',
    styleUrls: ['./image-viewer-modal.component.scss']
})
export class ImageViewerComponent implements OnInit {

    images: string[];
    imageIndex: number;

    @Input() imgUrls: string[];
    @Input() imgIndex: number;



    constructor() {

    }

    ngOnInit() {

        this.images = this.imgUrls;
        this.imageIndex = this.imgIndex;
    }

}