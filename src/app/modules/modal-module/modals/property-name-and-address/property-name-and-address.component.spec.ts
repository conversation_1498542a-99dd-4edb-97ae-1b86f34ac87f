import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PropertyNameAndAddressComponent } from './property-name-and-address.component';

describe('PropertyNameAndAddressComponent', () => {
  let component: PropertyNameAndAddressComponent;
  let fixture: ComponentFixture<PropertyNameAndAddressComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PropertyNameAndAddressComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PropertyNameAndAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
