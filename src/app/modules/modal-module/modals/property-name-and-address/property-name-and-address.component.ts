import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { PropertyLocation } from '../../../../models/PropertyLocation';
import { CommonStrings } from '../../../../constants';

@Component({
  selector: 'app-property-name-and-address',
  templateUrl: './property-name-and-address.component.html',
  styleUrls: ['./property-name-and-address.component.css']
})
export class PropertyNameAndAddressComponent implements OnInit {
  @Input() propertyLocation: PropertyLocation = new PropertyLocation();
  @Output() onClosePropertyNameModal = new EventEmitter();
  @Output() onSavePropeertyNameAndAddress = new EventEmitter();

  pNameAndAddressForm: FormGroup;
  streetNumberMin = null;
  addressStreetName = null;
  messages = CommonStrings.Messages;
  hasStreetNumber;
  hasStreetName;

  constructor() { }

  ngOnInit(): void {
    this.createForm();
    this.hasStreetNumber = !!this.propertyLocation.StreetNumberMin;
    this.hasStreetName = !!this.propertyLocation.StreetName;
    this.streetNumberMin = this.propertyLocation.StreetNumberMin;
    this.addressStreetName = this.propertyLocation.AddressStreetName;
  }

  createForm() {
    this.pNameAndAddressForm = new FormGroup({
      'StreetNumberMin': new FormControl('', Validators.required),
      'AddressStreetName': new FormControl('', Validators.required),
    });
  }

  savePropertyNameAndAddress() {
    if (this.pNameAndAddressForm?.valid) {
      this.propertyLocation.AddressStreetName = this.addressStreetName;
      this.propertyLocation.StreetNumberMin = this.streetNumberMin;
      this.onSavePropeertyNameAndAddress.emit();
    }
  }

  onCancel() {
    this.onClosePropertyNameModal.emit();
  }

}
