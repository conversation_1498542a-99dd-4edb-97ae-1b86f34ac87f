<div class="col-md-12">
    <form [formGroup]="pNameAndAddressForm" (ngSubmit)="savePropertyNameAndAddress()">
        <div class="col-md-12 heading">{{messages.FieldsRequired}}</div>
        <div class="form-group row">
            <div class="col-md-12" *ngIf="!hasStreetNumber">
                <div class="form-group row">
                    <label class="col-md-6 form-control-label" for="text-input">Street Number Min</label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" formControlName="StreetNumberMin"
                            [(ngModel)]="streetNumberMin"
                            [ngClass]="{'error-field':(!pNameAndAddressForm.controls['StreetNumberMin'].valid)}">
                        <div class="validation-error" *ngIf="(!pNameAndAddressForm.controls['StreetNumberMin'].valid)">
                            {{messages.StreetNumberMinRequired}}</div>
                    </div>
                </div>

                <div class="form-group row" *ngIf="!hasStreetName">
                    <label class="col-md-6 form-control-label" for="text-input">Street Name</label>
                    <div class="col-md-6">
                        <input type="text" maxlength="100" numericOnly allowDecimal="false" allowNegative="false" class="form-control"
                            formControlName="AddressStreetName" [(ngModel)]="addressStreetName"
                            [ngClass]="{'error-field':(!pNameAndAddressForm.controls['AddressStreetName'].valid)}">
                        <div class="validation-error"
                            *ngIf="(!pNameAndAddressForm.controls['AddressStreetName'].valid)">
                            {{messages.StreetNameRequired}}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="btns-wrapper">
            <button type="submit" class="btn btn-warn" [disabled]="!pNameAndAddressForm.valid">Save</button>
            <button class="btn btn-danger" (click)="onCancel()">Cancel</button>
        </div>
    </form>
</div>
