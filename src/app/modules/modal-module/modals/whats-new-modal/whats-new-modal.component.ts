import { Component, Output, EventEmitter, Input, ElementRef, Renderer2, HostListener } from '@angular/core';

@Component({
  selector: 'app-whats-new-modal',
  templateUrl: './whats-new-modal.component.html',
  styleUrls: ['./whats-new-modal.component.css']
})
export class WhatsNewModalComponent {
  @Output() handleOnClickWhatsNew:EventEmitter<any> = new EventEmitter<any>();
  @Input() updates: any[] = [];
  @Input() version: string = '';
  @Input() releaseNoteLink: string = '';

  currentSlide = 0;
  isFullScreen = false;

  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.initFullscreenListeners();
  }

  private initFullscreenListeners() {
    document.addEventListener('fullscreenchange', () => {
      this.isFullScreen = !!document.fullscreenElement;
    });
  }

  toggleFullScreen(imageWrapper: HTMLElement): void {
    if (!document.fullscreenElement) {
      imageWrapper.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  nextSlide(): void {
    if (this.isFullScreen) {
      document.exitFullscreen();
    }
    if (this.currentSlide < this.updates.length - 1) {
      this.currentSlide++;
    }
  }

  prevSlide(): void {
    if (this.isFullScreen) {
      document.exitFullscreen();
    }
    if (this.currentSlide > 0) {
      this.currentSlide--;
    }
  }

  closeWhatsNew(): void {
    const modalElement = this.el.nativeElement.querySelector('.updates-popup');
    if (modalElement) {
      // Add the `closing` class for animation
      this.renderer.addClass(modalElement, 'closing');
      setTimeout(() => {
        this.handleOnClickWhatsNew.emit();
      }, 200);
    }
  }

  handleNextBtnClick() {
    if (this.currentSlide === this.updates.length - 1) {
      this.closeWhatsNew(); // Close the modal on the last slide
    } else {
      this.nextSlide(); // Proceed to the next slide for other slides
    }
  }
}
