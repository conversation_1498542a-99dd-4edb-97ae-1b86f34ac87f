@keyframes blowUp {
  0% {
    opacity: 0;
    transform: scale(0.5) translate(-50%, -20px); /* Start small and off-position */
  }
  40% {
    opacity: 1;
    transform: scale(1.05) translate(-50%, 0); /* Slight overshoot to create a bounce effect */
  }
  60% {
    transform: scale(0.95) translate(-50%, 0); /* A bit of a squeeze */
  }
  100% {
    transform: scale(1) translate(-50%, 0); /* Final size */
  }
}

@keyframes shrinkDown {
  0% {
    opacity: 1;
    transform: scale(1) translate(-50%, 0);
  }
  40% {
    opacity: 0;
    transform: scale(0.9) translate(-50%, -10px); /* Slight shrink */
  }
  70% {
    opacity: 0;
    transform: scale(0.7) translate(-50%, -15px); /* Further shrink and move */
  }
  100% {
    opacity: 0;
    transform: scale(0.5) translate(-50%, -20px); /* Final state */
  }
}

.updates-popup {
  position: fixed;
  top: 70px; /* Adjust this value to control vertical alignment */
  left: 50%;
  transform: translate(-50%, 0);
  width: 50%;
  height: 70%;
  background: #ffffff; /* White background for popup */
  color: #000000; /* Black text for content */
  border-radius: 5px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  overflow: hidden;
  animation: blowUp 0.8s ease-out; /* Apply blow-up animation */

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px;
    background: var(--primary-brightBlue);
    border-bottom: 1px solid #ddd;

    h2 {
      margin: 0;
      font-size: 1.5rem; /* Larger and more prominent title */
      color: #000000; /* Black title color */
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: #ffffff; /* Black close button */
      cursor: pointer;
      transition: transform 0.3s ease, color 0.3s ease;
    }
  }

  .popup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 120px);
    position: relative;
  }
  
  .carousel {
    flex: 1;
    display: flex;
    transition: transform 0.3s ease-in-out;
    height: 100%;
  }
  
  .update-slide {
    flex: 0 0 100%;
    width: 100%;
    padding: 25px;
    padding-top: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    video {
      width: 100%;
      object-fit: cover;
      height: 300px;
      border-radius: 0%;
      flex-shrink: 0;
    }
  
    h3 {
      margin: 8px 0;
      color: #000000;
      font-size: 1.20rem;
      flex-shrink: 0;
    }
  
    .description-wrapper {
      flex: 1;
      overflow-y: auto;
      padding-right: 5px;
      margin-bottom: 60px;
  
      /* Custom scrollbar styling */
      &::-webkit-scrollbar {
        width: 0.7px; /* Thinner scrollbar */
      }
  
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
  
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 1px;
      }
  
      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
  
      /* Firefox */
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;
    }
  
    p {
      margin: 0;
      color: #333333;
      font-size: 1rem;
    }
  }
 
  .navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px 25px 25px; /* Reduced bottom padding from 50px to 25px */
    position: absolute; /* Added to fix position at bottom */
    bottom: 0; /* Position at bottom */
    left: 0;
    right: 0;
    background: #ffffff; /* Match popup background */
  }
  
  .navigation .release-notes {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
  }
  
  .navigation .release-notes:hover {
    color: #0056b3;
    text-decoration: underline;
  }
  
  .navigation .buttons-group {
    display: flex;
    gap: 15px;
    margin-left: auto;
  }
  
  .navigation button {
    color: #ffffff;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    padding: 10px 30px;
  }
  
  .navigation button:nth-child(1) {
    background: transparent;
    border: 1px solid #007bff;
    color: #007bff;
  }
  
  .navigation button:nth-child(2) {
    background: #007bff;
    border: none;
  }

  .navigation button:nth-child(2):hover{
    background: #0056b3;
  }

  .navigation button:nth-child(1):disabled {
    border-color: #bfbfbf;
    color: #bfbfbf;
    cursor: not-allowed;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    height: 300px;
    background-size: cover;
  }
  
  .image-wrapper-full-screen {
    position: relative;
    width: 100%;
    height: 100vh !important;
    object-fit: contain;
  }
  .expand-icon, .minimize-icon {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 5px;
    border-radius: 3px;
    cursor: pointer;
    z-index: 2;
  }
  
  .expand-icon:hover, .minimize-icon:hover {
    background: rgba(0, 0, 0, 0.7);
  }
}

.heading{
  display:flex;
  font-size: 16px;
  margin-left: 15px;
  color: #ffffff;
}

.wrapper{
  display: flex;
  gap:10px;
}

.updates-popup.closing {
  animation: shrinkDown 0.8s ease-out;
  pointer-events: none; 
}

.updates-wrapper{
  display:flex;
  flex-direction: column;
  padding: 20px;
}

.navigation-button{
  display:flex;
  justify-content: flex-end;
}

.background-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4); /* Adds a dimmed effect */
  backdrop-filter: blur(10px); /* Blurs the background */
  z-index: 999; /* Place it below the popup */
}

@media screen and (max-width: 1536px) and (max-height: 795px) {
  .updates-popup {
    width: 70%;
    height: 80%;
    top: 20px;
  }

  .updates-popup .popup-header h2 {
    font-size: 1rem;
  }

  .updates-popup .update-slide {
    padding: 10px;
  }

  .updates-popup .image-wrapper {
    height: 250px;
  }

  .updates-popup .popup-body {
    height: calc(100% - 100px);
  }

  .updates-popup .navigation {
    padding: 0 15px 15px 15px;
  }
}

@media screen and (max-width: 1080px) and (max-height: 555px) {
  .updates-popup {
    width: 80%;
    height: 85%;
    top: 20px;
  }

  .updates-popup .popup-header h2 {
    font-size: 1rem;
  }

  .updates-popup .update-slide {
    padding: 10px;
  }

  .updates-popup .image-wrapper {
    height: 200px;
  }

  .updates-popup .popup-body {
    height: calc(100% - 100px);
  }

  .updates-popup .navigation {
    padding: 0 15px 15px 15px;
  }
}


