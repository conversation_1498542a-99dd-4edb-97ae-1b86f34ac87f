<div class="col-md-12">
  <form [formGroup]="noteForm" (ngSubmit)="saveNote()">
    <div class="form-group row">
          <div class="col-md-6 mb-2">
            <div class="row">
                <label class="col-md-4 form-control-label">Note Type</label>
                <div class="col-md-8">
                 <!--    <select class="form-control" formControlName="NoteType" [(ngModel)]="selectedNote.NoteTypeID" >
                        <option value="">--Select--</option>
                        <option *ngFor="let notetype of notetypes" value="{{notetype.NoteTypeID}}">{{notetype.NoteTypeName}}</option>
                      </select> -->

                      <ng-select formControlName="NoteType" [items]="notetypes"
                      [virtualScroll]="true"
                      bindLabel="NoteTypeName"
                      bindValue="NoteTypeID"
                       placeholder="--Select--"
                       [(ngModel)]="selectedNote.NoteTypeID"
                       ></ng-select>
                </div>
              </div>
          </div>
          <div class="col-md-6 mb-2">
            <div class="row">
              <label class="col-md-4 form-control-label">Title</label>
              <div class="col-md-8">
                <input type="text" formControlName="Title" class="form-control" [(ngModel)]="selectedNote.NoteTitle"
                 [ngClass]="{'error-field':(!noteForm.controls['Title'].valid)}">
              </div>
            </div>
          </div>
          <div class="col-md-12 mb-5">
            <div class="row">
              <label class="col-md-2 form-control-label">Description</label>
              <div class="col-md-10">                  
                 <textarea rows="7" class="form-control" formControlName="Description" [(ngModel)]="selectedNote.NoteDescription" [ngClass]="{'error-field':(!noteForm.controls['Description'].valid)}"></textarea>
                </div>
            </div>
          </div> 
          <div class="col-md-12">
              <div class="row">
                <div class="col-md-12">
                    <div class="pull-right">
                      <button type="submit" class="btn btn-primary start">Save</button>
                      <button type="button" class="btn btn-success cancel" (click)="closeNote()">Cancel</button>
                    </div>
                  </div>
            </div>
          </div>
    </div>
  </form>
</div>