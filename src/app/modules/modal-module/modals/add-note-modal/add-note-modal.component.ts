import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Notes } from '../../../../models/notes';
import { NotesService } from '../../../../services/notes.service';
import { LoginService } from '../../../../services/login.service';
import { PropertyService } from '../../../../services/api-property.service';
import { mapEditPropertyDTO } from '../../../../DTO/mapEditPropertyDTO';

@Component({
  selector: 'app-add-note-modal',
  templateUrl: './add-note-modal.component.html',
  styleUrls: ['./add-note-modal.component.scss']
})
export class AddNoteModalComponent implements OnInit {

  @Input() selectedNote: Notes;
  @Input() initialDetails: mapEditPropertyDTO;
  @Output() onClose = new EventEmitter();
  noteForm: FormGroup;
  notetypes: any;

  constructor(private _notesService: NotesService
    , private _loginService: LoginService
    , private _propertyService: PropertyService) {

    const response_noteTypes = _propertyService.GetAllNoteTypes();
    response_noteTypes.subscribe(result => {
      if (!result.body.error) {
        this.notetypes = result.body.responseData;
      }
    });
  }

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.noteForm = new FormGroup({
      'NoteType': new FormControl(''),
      'Title': new FormControl(''),
      'Description': new FormControl('')
    });
  }

  saveNote() {
    if(this.noteForm.valid){
      this.selectedNote.LoginEntityID = this._loginService.UserInfo.EntityID;
      const response_notes = this._notesService.SaveNote(this.selectedNote);
      response_notes.subscribe(result => {
        if (!result.body.error) {
          this.onClose.emit();
        }
      });
    }    
  }

  closeNote() {
    this.onClose.emit();
  }

}
