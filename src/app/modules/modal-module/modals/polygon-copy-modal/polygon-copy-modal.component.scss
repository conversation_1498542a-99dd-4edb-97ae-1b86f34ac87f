.floor-data {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
}
.specific-use-wrapper {
  padding-left: 0;
}
.default-property-use {
  display: flex;
  margin-left: 10px;
}
.radio-toolbar:not(:first-child) {
  margin-left: 5px;
}

.radio-toolbar input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}

.radio-toolbar label {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.center-aligned-radio-button {
  padding: 6px !important;
}
.blueCheckedLabel {
  background: var(--primary-blue-color) !important;
  border: 1px solid var(--primary-blue-color) !important;
  color: var(--primary-white) !important;
}
.redCheckedLabel {
  background: var(--red-color) !important;
  border: 1px solid var(--red-color) !important;
  color: var(--primary-white) !important;
}
.orangeCheckedLabel {
  background: var(--primary-orange) !important;
  border: 1px solid var(--primary-orange) !important;
  color: var(--primary-white) !important;
}
.brownCheckedLabel {
  background: var(--brown-color) !important;
  border: 1px solid var(--brown-color) !important;
  color: var(--primary-white) !important;
}
.magentaCheckedLabel {
  background: var(--magenta-color) !important;
  border: 1px solid var(--magenta-color) !important;
  color: var(--primary-white) !important;
}
.greenCheckedLabel {
  background: var(--green-color) !important;
  border: 1px solid var(--green-color) !important;
  color: var(--primary-white) !important;
}
.ashCheckedLabel {
  background: var(--primary-light-ash) !important;
  border: 1px solid var(--primary-light-ash) !important;
  color: var(--primary-white) !important;
}
.type-btn {
  background: var(--primary-white-color);
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  min-width: 37px !important;
  height: 39px;
  font-weight: bold;
  border: 1px solid var(--primary-blue-color);
  color: var(--primary-blue-color);
}
.type-btn.blue {
  border: 1px solid var(--primary-blue-color);
  color: var(--primary-blue-color);
}
.type-btn.red {
  border: 1px solid var(--red-color);
  color: var(--red-color);
}
.type-btn.green {
  border: 1px solid var(--green-color);
  color: var(--green-color);
}
.type-btn.orange {
  border: 1px solid var(--primary-orange);
  color: var(--primary-orange);
}
.type-btn.magenta {
  border: 1px solid var(--magenta-color);
  color: var(--magenta-color);
}
.type-btn.brown {
  border: 1px solid var(--brown-color);
  color: var(--brown-color);
}
.type-btn.ash {
  border: 1px solid var(--primary-light-ash);
  color: var(--primary-light-ash);
}
.btn-primary:disabled {
  background-color: #b0aeae !important;
  border-color: #838383 !important;
  color: #3f3f3f !important;
}
.save-polygon {
  background: #4180c3 !important; 
  border-color: #4180c3 !important;
  color: white !important;
  margin-top: 23px;
  line-height: 32px;
  border-radius: 0.6rem;
}
.description{
  max-width: 250px;
  min-width: 100px;
}
.title {
  color: #4180c3 !important;
}
.default-property-use {
  display: flex;
  margin-left: 10px;
}
.radio-button-disabled {
  background-color: #b0aeae !important;
  border: #838383 !important;
  color: #3f3f3f !important;
}
