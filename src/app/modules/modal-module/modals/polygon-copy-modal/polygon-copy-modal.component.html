<div class="col">
  <div class="col">
    <h4 class="title">Copy From</h4>
    <div class="floor-data">
      <div class="specific-use-wrapper">
        <div id="propertyUse" class="row">
          <div class="no-padding">
            <div class="form-group">
              <label class="form-control-label default-property-use" for="text-input">Property Use
              </label>
              <div class="default-property-use">
                <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                  <input type="radio" id="toCopy_{{i}}_{{1}}" [value]="useType.UseTypeID"
                    [(ngModel)]="originalPolygon.specificUse" [ngModelOptions]="{ standalone : true }" [disabled]="true"
                    *ngIf="useType.UseTypeID != '7'">
                  <label for="toCopy_{{i}}_{{1}}" title="{{useType.UseTypeName}}" *ngIf="useType.UseTypeID != '7'"
                    class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer" [ngClass]="{'blue' : useType.UseTypeID == '5',
                                                'red' : useType.UseTypeID == '3',   
                                                'orange' : useType.UseTypeID == '2',
                                                'brown' : useType.UseTypeID == '4',
                                                'magenta' : useType.UseTypeID == '9',
                                                'green': useType.UseTypeID == '7',
                                                'ash': useType.UseTypeID == '12',
                                              'blueCheckedLabel': (originalPolygon.specificUse == '5' && useType.UseTypeID == '5') ,
                                                'redCheckedLabel': (originalPolygon.specificUse == '3' && useType.UseTypeID == '3'),
                                                'orangeCheckedLabel': (originalPolygon.specificUse == '2' && useType.UseTypeID == '2'),
                                                'brownCheckedLabel': (originalPolygon.specificUse == '4' && useType.UseTypeID == '4'), 
                                                'magentaCheckedLabel': (originalPolygon.specificUse == '9' && useType.UseTypeID == '9'),
                                                'greenCheckedLabel': (originalPolygon.specificUse == '7' && useType.UseTypeID == '7'),
                                                'ashCheckedLabel': (originalPolygon.specificUse == '12' && useType.UseTypeID == '12')
                                              }">{{useType.UseTypeLabel}}</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <label class="form-control-label no-padding" style="width: 115px;">Additional Use</label>
        <ng-select [items]="additionalUseTypes" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="UseTypeLabel" [disabled]="true"
          bindValue="UseTypeID" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="originalPolygon.additionalUse">
        </ng-select>
      </div>
      <div class="d-flex justify-content-between" style="gap: 1rem">
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Min </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px;" bindLabel="key"
            bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [disabled]="true"
            [(ngModel)]="originalPolygon.minFloor">
          </ng-select>

        </div>
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Max </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="key"
            bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [disabled]="true"
            [(ngModel)]="originalPolygon.maxFloor">
          </ng-select>
        </div>
      </div>
      <div>
        <div>
          <label class="form-control-label" for="text-input">Floor Size
          </label>
          <div class="position-relative">
            <input type="number" [(ngModel)]="originalPolygon.floorSize" [ngModelOptions]="{standalone: true}"
              class="form-control maxnumbervalidation floorsizeinput" [disabled]="true">
          </div>
        </div>
      </div>
      <div class=" description form-group">
        <label class="form-control-label no-padding" for="text-input"> Description </label>
        <input type="text" class="form-control description" [(ngModel)]="originalPolygon.description"
          [ngModelOptions]="{ standalone: 'true' }" [disabled]="true">
      </div>
      <div class="floorcount form-group">
        <label class="form-control-label" for="text-input"> Floor Count </label>
        <input type="number" class="form-control floor-count" [(ngModel)]="originalPolygon.floorCount"
          [ngModelOptions]="{standalone: true}" [disabled]="true">
      </div>
    </div>
    <h4 class="title">Copy To</h4>
    <div class="floor-data">
      <div class="specific-use-wrapper">
        <div id="propertyUse" class="row">
          <div class="no-padding">
            <div class="form-group">
              <label class="form-control-label default-property-use" for="text-input">Property Use
              </label>
              <div class="default-property-use">
                <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                  <input type="radio" id="toAdd_{{i}}_{{1}}" [value]="useType.UseTypeID"
                    (change)="onSpecificUseChange(useType.UseTypeID)" [(ngModel)]="polygonCopy.specificUse"
                    [ngModelOptions]="{ standalone : true }" *ngIf="useType.UseTypeID != '7'" [disabled]="useType.UseTypeID == originalPolygon.specificUse">
                  <label for="toAdd_{{i}}_{{1}}" title="{{useType.UseTypeName}}" *ngIf="useType.UseTypeID != '7'"
                    class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer" [ngClass]="{'blue' : (useType.UseTypeID == '5' && useType.UseTypeID != originalPolygon.specificUse),
                                                'red' : (useType.UseTypeID == '3' && useType.UseTypeID != originalPolygon.specificUse),
                                                'orange' : (useType.UseTypeID == '2' && useType.UseTypeID != originalPolygon.specificUse),
                                                'brown' : (useType.UseTypeID == '4' && useType.UseTypeID != originalPolygon.specificUse),
                                                'magenta' : (useType.UseTypeID == '9' && useType.UseTypeID != originalPolygon.specificUse),
                                                'green': (useType.UseTypeID == '7' && useType.UseTypeID != originalPolygon.specificUse),
                                                'ash': (useType.UseTypeID == '12' && useType.UseTypeID != originalPolygon.specificUse),
                                              'blueCheckedLabel': (polygonCopy.specificUse == '5' && useType.UseTypeID == '5') ,
                                                'redCheckedLabel': (polygonCopy.specificUse == '3' && useType.UseTypeID == '3'),
                                                'orangeCheckedLabel': (polygonCopy.specificUse == '2' && useType.UseTypeID == '2'),
                                                'brownCheckedLabel': (polygonCopy.specificUse == '4' && useType.UseTypeID == '4'), 
                                                'magentaCheckedLabel': (polygonCopy.specificUse == '9' && useType.UseTypeID == '9'),
                                                'greenCheckedLabel': (polygonCopy.specificUse == '7' && useType.UseTypeID == '7'),
                                                'ashCheckedLabel': (polygonCopy.specificUse == '12' && useType.UseTypeID == '12'),
                                                'radio-button-disabled': (useType.UseTypeID == originalPolygon.specificUse)
                                              }">{{useType.UseTypeLabel}}</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <label class="form-control-label" style="width: 115px;">Additional Use</label>
        <ng-select [items]="additionalUseTypes" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="UseTypeLabel"
          bindValue="UseTypeID" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="polygonCopy.additionalUse">
        </ng-select>
      </div>
      <div class="d-flex justify-content-between" style="gap: 1rem">
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Min </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px;" bindLabel="key"
            bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }"
            [(ngModel)]="polygonCopy.minFloor" (change)="onMinMaxChange()">
          </ng-select>

        </div>
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Max </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="key"
            bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }"
            [(ngModel)]="polygonCopy.maxFloor" (change)="onMinMaxChange()">
          </ng-select>
        </div>
      </div>
      <div>
        <div>
          <label class="form-control-label" for="text-input">Floor Size
          </label>
          <div class="position-relative">
            <input type="number" [(ngModel)]="polygonCopy.floorSize" [ngModelOptions]="{standalone: true}"
              class="form-control maxnumbervalidation floorsizeinput" [disabled]="true">
          </div>
        </div>
      </div>
      <div class=" description form-group">
        <label class="form-control-label no-padding" for="text-input"> Description </label>
        <input type="text" class="form-control description" [(ngModel)]="polygonCopy.description"
          [ngModelOptions]="{ standalone: 'true' }">
      </div>
      <div class="floorcount form-group">
        <label class="form-control-label" for="text-input"> Floor Count </label>
        <input type="number" class="form-control floor-count" [(ngModel)]="polygonCopy.floorCount"
          [ngModelOptions]="{standalone: true}" [disabled]="true">
      </div>
    </div>
    <div class="d-flex justify-content-end w-100">
      <button (click)="onAddPolygon()" class="btn btn-sm btn-primary save-polygon" [disabled]="!isNewPolygonValid()">
        Copy Polygon
      </button>
    </div>
  </div>
</div>
