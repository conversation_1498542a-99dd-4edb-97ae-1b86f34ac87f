import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PolygonCopyModalComponent } from './polygon-copy-modal.component';

describe('PolygonCopyModalComponent', () => {
  let component: PolygonCopyModalComponent;
  let fixture: ComponentFixture<PolygonCopyModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PolygonCopyModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PolygonCopyModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
