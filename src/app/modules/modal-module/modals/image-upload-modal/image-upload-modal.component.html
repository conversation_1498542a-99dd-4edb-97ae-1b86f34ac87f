<div class="col-md-12 data-wrapper" *ngIf="mediaView === 'singleMedia'">
    <div [ngClass]="{'file-row': true, row: true, odd: oddRow}">
        <!--start-->
        <form [formGroup]="mediaForm" class="form-align">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label>File Name</label>
                                <input type="text" class="form-control" formControlName="MediaName"
                                    [(ngModel)]="media.MediaName"
                                    [ngClass]="{'error-field':(!mediaForm.controls['MediaName'].valid)}">
                            </div>
                            <div class="col-lg-12 mb-3" *ngIf="IsProperty">
                                <label>Media Type</label>
                                <ng-select formControlName="MediaTypeID" [items]="mediaTypes" [virtualScroll]="true"
                                    labelForId="MediaTypeID" bindLabel="MediaTypeName" bindValue="MediaTypeID"
                                    placeholder="--Select--" [(ngModel)]="media.MediaTypeID"
                                    [ngClass]="{'error-field':(!mediaForm.controls['MediaTypeID'].valid)}"
                                    (change)="getSelectedValue('MediaTypeID',$event ?$event.MediaTypeName:'')">
                                </ng-select>
                            </div>
                            <div class="col-lg-12 mb-3" *ngIf="IsProperty">
                                <label>Media SubType</label>
                                <ng-select formControlName="MediaSubTypeID" [items]="mediaSubTypes"
                                    [virtualScroll]="true" labelForId="MediaSubTypeID" bindLabel="MediaSubTypeName"
                                    bindValue="MediaSubTypeID" placeholder="--Select--"
                                    [(ngModel)]="media.MediaSubTypeID"
                                    (change)="getSelectedValue('MediaSubTypeID',$event ?$event.MediaSubTypeName:'')">
                                </ng-select>
                            </div>
                            <div class="col-lg-12 mb-3">
                                <label>Media Description</label>
                                <textarea rows="1" class="form-control" formControlName="Description"
                                    [(ngModel)]="media.Description"></textarea>
                            </div>
                            <div class="col-lg-12 mb-3" *ngIf="IsProperty && !MinifiedMedia && !HideDefault">
                                <label class="set-as"><input type="checkbox" formControlName="IsDefault"
                                        name="media.IsDefault" [(ngModel)]="media.IsDefault"
                                        (change)="getSelectedValue('IsDefault',$event ?$event.target.checked:'')" />
                                    Set as Default <img src="assets/images/favourte-icon.png" width="20"></label>

                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-lg-12 mb-3" style="text-align: center">
                                <img [src]="media.URL" class="img-fluid" style="max-height:250px;">
                            </div>
                            <div class="col-lg-12">
                                <ul class="size-info">
                                    <li>
                                        <i class="fa fa-file"></i>
                                        <h3>{{fileSize}}</h3>
                                        <p>size</p>
                                    </li>
                                    <li>
                                        <i class="fa fa-arrows-v"></i>
                                        <h3>{{media.Height}}</h3>
                                        <p>Height</p>
                                    </li>
                                    <li>
                                        <i class="fa fa-exchange"></i>
                                        <h3>{{media.Width}}</h3>
                                        <p>Width</p>
                                    </li>
                                </ul>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
            <div class="col-md-12" *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                <div class="row">
                    <div class="col-md-6">
                        <label>Did you take this photo ? <span class="mandatory">*</span></label>
                        <ng-select formControlName="IsOwnMedia" [items]="sharedDataService.yesNoList"
                            labelForId="IsOwnMedia" [virtualScroll]="true" bindLabel="Item" bindValue="ID"
                            placeholder="--Select--" [(ngModel)]="media.IsOwnMedia"
                            (change)="mediaSourceChange(media.IsOwnMedia)"
                            [ngClass]="{'error-field':(!mediaForm.controls['IsOwnMedia'].valid && mediaForm.controls['IsOwnMedia'].touched)}"
                            (change)="getSelectedValue('IsOwnMedia',$event ?$event.Item:'')">
                        </ng-select>
                    </div>
                    <div class="col-md-6" *ngIf="media.IsOwnMedia == 0">
                        <label>Media Source <span class="mandatory">*</span></label>
                        <ng-select formControlName="MediaSourceID" [items]="mediaSource" [virtualScroll]="true"
                            labelForId="MediaSourceID" bindLabel="MediaSourceName" bindValue="MediaSourceID"
                            placeholder="--Select--" [(ngModel)]="media.MediaSourceID"
                            [ngClass]="{'error-field':(!mediaForm.controls['MediaSourceID'].valid && mediaForm.controls['MediaSourceID'].touched)}"
                            (change)="getSelectedValue('MediaSourceID',$event ?$event.MediaSourceName:'')">
                        </ng-select>
                    </div>
                </div>
            </div>
            <ng-container *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                <div class="col-md-12 mt-3" *ngIf="media.IsOwnMedia == 0">
                    <div class="row">
                        <div class="col-md-12">
                            <label>Source Comments<span *ngIf="mediaWEBsource" class="mandatory">*</span></label>
                            <textarea rows="3" formControlName="SourceComments" [(ngModel)]="media.SourceComments"
                                class="form-control" maxlength="1000"
                                [ngClass]="{'error-field':(!mediaForm.controls['SourceComments'].valid) && mediaForm.controls['SourceComments'].touched}"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mt-3" *ngIf="media.IsOwnMedia == 0">
                    <div class="row">
                        <div class="col-md-12">
                            <label style="color:#20a8d8;" *ngIf="media.MediaID > 0 && fileArray.length">
                                Source Materials
                            </label>
                            <label for="inputFiles" class="file-input-btn" *ngIf="!media.MediaID">
                                <span style="color:#20a8d8;">
                                    <i class="fa fa-paperclip" aria-hidden="true"></i>
                                    <span>Attach confirmation source materials</span>
                                    <input id="inputFiles" name="files[]" multiple="" type="file" accept="image/*"
                                        (change)="fileUploadEvent($event)">
                                </span>
                            </label>
                        </div>
                        <div class="col-md-6">
                            <div class="row m-0">
                                <ng-container *ngFor="let file of fileArray; let i = index;">
                                    <div class="col-md-12 dottedline pr-0 pl-0">
                                        <span class="labelText" *ngIf="!media.MediaID">{{file.MediaName}}</span>

                                        <span class="deleteRow" *ngIf="!media.MediaID && !file.IsUploaded"
                                            (click)="deleteAddedFiles(i)">
                                            <i class="fa fa-times"></i>
                                        </span>
                                        <span class="statusRow" *ngIf="!media.MediaID && file.IsUploaded">
                                            <i class="fa fa-check-circle"></i>
                                        </span>
                                        <span class="deleteRow mr-2">
                                            {{file.Size}}
                                        </span>
                                        <a class="labelText" *ngIf="media.MediaID > 0" target="_blank" download
                                            href="{{file.OrginalURL}}">
                                            {{file.MediaName}}.{{file.Ext}}
                                        </a>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </form>
        <!--end-->

        <div class="col-md-12">
            <div class="row">
                <div class="col-md-12">
                    <div *ngIf="!isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <button class="btn btn-success start" (click)="checkUpload()" [disabled]="!mediaForm.valid">
                            <i class="fa fa-upload"></i>
                            <strong>Upload</strong>
                        </button>
                        <button class="btn btn-primary cancel" (click)="retake()">
                           <i class="fa fa-repeat" aria-hidden="true"></i>
                            <strong>Retake</strong>
                        </button>
                        <button class="btn btn-primary cancel" (click)="close()">
                            <i class="fa fa-remove"></i>
                            <strong>Cancel</strong>
                        </button>
                    </div>
                    <div *ngIf="isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <button class="btn btn-success start" (click)="save()">
                            <i class="fa fa-upload"></i>
                            <strong>Save</strong>
                        </button>
                        <button class="btn btn-primary cancel" (click)="close()">
                            <i class="fa fa-remove"></i>
                            <strong>Cancel</strong>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<imperium-modal [(visible)]="isNoDefaultImage" [title]="'No Default Image'" [bodyTemplate]="noDefaultImage"
  [width]="'medium'" [closeOnClickOutside]="false">
  <ng-template #noDefaultImage>
    {{title}}
    <div class="text-right">
      <button class="btn btn-primary start" (click)="setNewImageAsDefaultImage()">
        <strong>Yes</strong>
      </button>
      <button class="btn btn-success cancel" (click)="noDefaultImages()">
        <strong>No</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>

<imperium-modal [(visible)]="isReplaceDefaultImage" [title]="'Replace Default Image'" [bodyTemplate]="replaceDefaultImage"
  [width]="'medium'" [closeOnClickOutside]="false">
  <ng-template #replaceDefaultImage>
    {{title}}
    <div class="text-right">
      <button class="btn btn-primary start" (click)="swapDefaultImage()">
        <strong>Yes</strong>
      </button>
      <button class="btn btn-success cancel" (click)="noSwapDefaultImage()">
        <strong>No</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>

<imperium-modal [(visible)]="isDefaultImageConflict" [title]="'Default Image Conflict'" [bodyTemplate]="defaultImageConflict"
  [width]="'medium'" [closeOnClickOutside]="false">
  <ng-template #defaultImageConflict>
    {{title}}
    <div class="text-right">
      <button class="btn btn-success cancel" (click)="imageConflictClose()">
        <strong>Ok</strong>
      </button>
    </div>
  </ng-template>
</imperium-modal>
