.file-row .form-align {
    width: 100%;
}

ul.size-info {
    display: flex;
    padding: 0;
    margin-bottom: 0;
    text-align: center;
}

ul.size-info li {
    flex: 0 0 35%;
    list-style: none;
    position: relative;
    padding-left: 35px;
}

ul.size-info li i {
    position: absolute;
    left: 30px;
    font-size: 20px;
    color: #f18a00;
}

ul.size-info li h3 {
    color: #191d64;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 0;
}

ul.size-info li {
    margin: 0;
    font-size: 12px;
}

.file-row .img-fluid {
    max-width: 100%;
    // height: 100%;
    object-fit: contain;
}

#inputFiles {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    font-size: 23px;
    direction: ltr;
    cursor: pointer;
    left: 0;
    width: 100%;
}

.labelText {
    float: left;
    font-size: 12px;
    color: rgb(150, 150, 150);
    max-width: 98%;
    overflow: hidden;
}

.deleteRow {
    float: right;
    color: rgb(150, 150, 150);
    font-size: 12px;
}

.statusRow {
    float: right;
    color: rgb(29, 172, 0);
    font-size: 12px;
}

.dottedline {
    border-bottom: 1px dotted #ccc;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #f9f9f9;
    opacity: 1;
}

.data-wrapper{
    width: 50rem;
}

.data-wrapper label {
    border: 0px !important;
    width: 100%;
    padding-bottom: 5px !important;
    color: #212529 !important;
    background: none !important;
    padding-left: 0 !important;
    padding-top: 0 !important;
    font-size: 16px;
}

.btn-success i, .btn-primary i{
    color: #fff !important;
}

.dynamicBox{
    max-height: 92%;
    overflow: auto;
}

.deleteFaIco{
    color: red; font-size: 1.5rem;
    position: absolute;
    right: 10px;
    top: 0;
}

.succesUploadIcon{
    position: absolute;
    font-size: 1.2rem;
    top: -15px;
    right: -5px;
    color: rgb(29, 172, 0);
}

.loaderSpin{
    position: absolute;
    left: 45%;
    top: 34%;
    color: #fff;
    font-size: 2rem;
}
.loaderSpin-grid {
    position: absolute;
    left: 18%;
    top: 25%;
    color: #fff;
    font-size: 2rem;
}

.greenIcon{
    color: rgb(29, 172, 0);
}
.pointer{
    cursor: pointer;
}
