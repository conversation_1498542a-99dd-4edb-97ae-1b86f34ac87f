import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FileObject, FileObjectStatus } from '../../../aws/types/types';
import { Subscription } from 'rxjs';
import { MediaService } from '../../../../services/media.service'
import { ContactMedia } from '../../../../models/ContactMedia';
import { CommunicationService } from '../../../../services/communication.service';
import {
  MediaType, MediaSubType, Media, MediaTypeEnum, MediaSource, MediaSubTypeEnum,
  MediaRelationTypeEnum, MediaSourceTypeEnum
} from '../../../../models/MediaType';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';

import { LoginService } from '../../../../services/login.service';
import { FileTypes } from '../../../aws/types/types';
import { environment } from '../../../../../environments/environment';


// declare var config: any;
// const constants = config.prototype.constants();
import { SharedDataService } from '../../../../services/shareddata.service';
import { NotificationService } from '../../../notification/service/notification.service';
import { EnumApplication } from '../../../../enumerations/application';

// Import IndexedDB Class
import { IndexedDBService } from '../../../../services/indexeddb.service'
import { StagingIndexedDBService } from '../../../../services/indexeddb-staging.service';
import { CommonStrings } from '../../../../constants';

@Component({
  selector: 'image-upload-modal',
  templateUrl: './image-upload-modal.component.html',
  styleUrls: ['./image-upload-modal.component.scss']
})
export class ExpressViewImageUploadModalComponent implements OnInit {

  @Input() media: Media;
  @Input() oddRow: boolean;
  @Input() IsAgentAdd = false;
  @Input() IsBranchAdd = false;
  @Input() IsCompanyAdd = false;
  @Input() initialDetails: ContactMedia;
  @Input() IsProperty = false;
  @Input() MinifiedMedia = false;
  @Input() HideDefault = false;
  @Output() onSave = new EventEmitter<string>();
  @Output() onClose = new EventEmitter();
  @Output() onRetake = new EventEmitter();
  @Output() onSetDefault = new EventEmitter<string>();
  @Output() onSourceChange = new EventEmitter<boolean>();
  @Input() IsPropertyBasicInfo = false;
  @Input() uploadFiles = new Array<Media>();
  uploadFilesCopy: Array<Media> = new Array<Media>();
  files: Media[] = [];
  attachMedia: Media[] = [];
  FileObjectStatus = FileObjectStatus;
  FileObjectStatusAttach: FileObjectStatus[] = [];
  progress = 0;
  speed = 0;
  uploadError: string;
  containerEventSubscription: Subscription;
  uploadHandle: any;
  isDefault: any = 0;
  mediaForm: FormGroup;
  fileObject: FileObject;
  fileObjectAttach: FileObject[] = [];
  fileSize: string;
  isEdit = false;
  showLoader = false;
  mediaTypes: Array<MediaType>;
  mediaSource: Array<MediaSource>;
  mediaSubTypes: Array<MediaSubType>;
  URL: any;
  IsAttachment = false;
  fileArray: Array<any> = [];
  initialAttachmentDetails: ContactMedia;
  attachfileSize: string;
  count = 0;
  mediaWEBsource = false;
  public mediaCopy: Media = new Media();
  DataArray: Array<any> = [];
  mediaView = 'singleMedia';
  NonImageFiles = [];
  ImageFiles = [];
  extendHeight = '40vh';
  dataCount: number = null;
  multipleArrayCount = 0;
  IndexedDBService: IndexedDBService;
  StagingIndexedDBService: StagingIndexedDBService
  defaultImage : any;
  galleryImages: any;
  isNoDefaultImage: boolean = false;
  isReplaceDefaultImage: boolean = false;
  isDefaultImageConflict: boolean = false;
  title: string;

  constructor(private mediaService: MediaService,
    private _loginService: LoginService,
    private communicationService: CommunicationService,
    private sharedDataService: SharedDataService,
    private _notificationService: NotificationService, private fb: FormBuilder) {
    this.IndexedDBService = new IndexedDBService();
    this.StagingIndexedDBService = new StagingIndexedDBService();
  }

  ngOnInit() {
    this.mediaView = 'singleMedia'
    this.createForm();

    this.media.ModifiedBy = this._loginService.UserInfo.EntityID;
    this.media.RelationshipTypeID = this.initialDetails.RelationshipTypeID;
    this.media.RelationID = this.initialDetails.RelationID;
    this.media.PropertyID = this.initialDetails.PropertyID;
    if (this.media.MediaID > 0) {
      this.isEdit = true;
      this.fileSize = this.formatBytes(this.media.Size);
      this.getAttachmentMedia();
      this.mediaCopy = new Media();
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    } else {
      this.fileObject = new FileObject(this.media.File);
      this.media.Ext = this.media.Ext || this.media.File.name.split('.').pop();
      this.media.MediaName = this.media.File.name.split('.')[0];
      this.media.Size = this.media.File.size;
      this.fileSize = this.formatBytes(this.fileObject.file.size);
      this.media.MediaTypeID = this.media.MediaTypeID || MediaTypeEnum.AerialImagery;
      this.media.MediaSubTypeID = this.media.MediaSubTypeID || 0;
      this.media.CreatedBy = this._loginService.UserInfo.EntityID;
      this.media.IsDefault = this.getIsDefault(this.media.MediaTypeID, this.media.MediaSubTypeID);
      this.media.MediaID = 0;
      this.media.MediaSourceID = this.media.MediaSourceID;
      this.media.IsOwnMedia = this.media.IsOwnMedia;
      if (this.IsPropertyBasicInfo === true) {
        this.media.MediaTypeID = this.media.MediaTypeID || MediaTypeEnum.BuildingImage;
        this.media.MediaSubTypeID = this.media.MediaSubTypeID || MediaSubTypeEnum.MainPhoto;
        this.media.IsDefault = this.media.IsDefault || 1;
      }
    }
    this.fileExtentions(this.media);

    if (!!this.sharedDataService.mediaTypes && this.sharedDataService.mediaTypes.length > 0) {
      this.mediaTypes = this.sharedDataService.mediaTypes;
      this.media.MediaName = this.getMediaFileNameFromMediaTypeID(this.media.MediaTypeID)
    } else {
      const mediaTypeResponse = this.mediaService.GetMediaTypes();
      mediaTypeResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaTypes = result.body.responseData;
          this.sharedDataService.mediaTypes = this.mediaTypes;
          this.media.MediaName = this.getMediaFileNameFromMediaTypeID(this.media.MediaTypeID)
        }
      });
    }
    if (!!this.sharedDataService.mediaSubTypes && this.sharedDataService.mediaSubTypes.length > 0) {
      this.mediaSubTypes = this.sharedDataService.mediaSubTypes;
    } else {
      const mediaSubTypeResponse = this.mediaService.GetMediaSubTypes();
      mediaSubTypeResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaSubTypes = result.body.responseData;
          this.sharedDataService.mediaSubTypes = this.mediaSubTypes;

        }
      });
    }
    if (!!this.sharedDataService.mediaSource && this.sharedDataService.mediaSource.length > 0) {
      this.mediaSource = this.sharedDataService.mediaSource;
    } else {
      const mediaSourceResponse = this.mediaService.GetMediaSource();
      mediaSourceResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaSource = result.body.responseData;
          this.sharedDataService.mediaSource = this.mediaSource;
        }
      });
    }

  }

  ngAfterViewInit() {
      const medias = this.mediaService.getAllMediaRelation(this.initialDetails);
      medias.subscribe(result => {
        if (!result.body.error) {
          this.galleryImages = result.body.responseData[0];
        }
      });
  }

  fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PNG || media.Ext.toLowerCase() === FileTypes.JPG) {
      media.URL = media.URL || environment.MediaS3Base + environment.MediaS3Path + environment.MediaS3ThumbnailPath +
        '/' + environment.MediaS3ThumbResolution + '/' + media.Path;
    } else {
      this.mediaService.fileExtentions(media);
    }
  }

  checkUpload() {
    if (this.galleryImages && this.galleryImages.length > 0) {
      this.defaultImage = this.galleryImages.find(image => image.IsDefault == 1)
      if (this.media.IsDefault == 0 && this.defaultImage === undefined) {
        this.isNoDefaultImage = true;
        this.title = CommonStrings.DialogConfigurations.Messages.NoDefaultImageMessage;
      } else if (this.media.IsDefault == 1 && this.defaultImage) {
        if (this.defaultImage.MediaSourceID == 1) {
          this.isDefaultImageConflict = true;
          this.title = CommonStrings.DialogConfigurations.Messages.DefaultImageConflictMessage;
        }
        else {
          this.isReplaceDefaultImage = true;
          this.title = CommonStrings.DialogConfigurations.Messages.ReplaceDefaultImageMessage
        }
      } else {
        this.upload();
      }
    } else {
      this.upload();
    }
  }
  

  setNewImageAsDefaultImage() {
    this.media.IsDefault = 1;
    this.isNoDefaultImage = false;
    this.upload();
  }

  swapDefaultImage() {
    this.media.IsDefault = 1;
    this.defaultImage.isDefault = 0;
    this.isReplaceDefaultImage = false;
    this.upload();
  }

  noDefaultImages() {
    this.isNoDefaultImage = false;
    this.upload();
  }

  imageConflictClose() {
    this.media.IsDefault = 0;
    this.isDefaultImageConflict = false;
    this.upload();
  }

  noSwapDefaultImage() {
    this.media.IsDefault = 0;
    this.isReplaceDefaultImage = false;
    this.upload();
  }

  upload() {
    if (this.mediaForm.valid) {
      this.showLoader = true;
      if (environment.EnableBackgroundMediaUpload) {
        this.backgroundSaveMedia();
      } else {
        const uploadHandle = this.mediaService.uploadToS3(this.fileObject.fileName, this.media.UploadPathURL);
        uploadHandle.subscribe(result => {
          if (!result.body.error) {
            this.media.Path = this.fileObject.fileName;
            this.saveMedia();
          }
        });
      }
    }
  }

  save() {
    this.showLoader = true;
    this.saveMedia();
  }

  close() {
    this.onClose.emit();
  }

  retake() {
    this.onRetake.emit();
  }

  createForm() {
    this.mediaForm = new FormGroup({
      'MediaName': new FormControl('', Validators.required),
      'MediaTypeID': new FormControl(''),
      'MediaSubTypeID': new FormControl(''),
      'Description': new FormControl(''),
      'IsDefault': new FormControl(''),
      'IsOwnMedia': new FormControl(''),
      'MediaSourceID': new FormControl(''),
      'SourceComments': new FormControl('')
    });
    if (!this.IsAgentAdd && !this.IsBranchAdd && !this.IsCompanyAdd) {
      if (this.media.MediaID > 0) {
        this.mediaForm.controls['IsOwnMedia'].disable();
        this.mediaForm.controls['MediaSourceID'].disable();
        this.mediaForm.controls['SourceComments'].disable();
        this.mediaForm.controls['IsOwnMedia'].markAsUntouched();
        this.mediaForm.controls['MediaSourceID'].markAsUntouched();
        this.mediaForm.controls['SourceComments'].markAsUntouched();
      } else {
        this.mediaForm.controls['IsOwnMedia'].enable();
        this.mediaForm.controls['MediaSourceID'].enable();
        this.mediaForm.controls['SourceComments'].enable();
        this.mediaForm.controls['IsOwnMedia'].markAsTouched();
        this.mediaForm.controls['MediaSourceID'].markAsTouched();
        this.mediaForm.controls['SourceComments'].markAsTouched();
        this.mediaForm.get('IsOwnMedia').setValidators([Validators.required]);
        this.mediaForm.get('IsOwnMedia').valueChanges.subscribe(
          (IsOwnMedia) => {
            if (IsOwnMedia === 0) {
              this.mediaForm.get('MediaSourceID').setValidators([Validators.required]);
            } else {
              this.mediaForm.get('MediaSourceID').clearValidators();
            }
            this.mediaForm.get('MediaSourceID').updateValueAndValidity();
          });
        this.mediaForm.get('MediaSourceID').valueChanges.subscribe(
          (MediaSourceID) => {
            if (MediaSourceID === MediaSourceTypeEnum.OwnerWebsite || MediaSourceID === MediaSourceTypeEnum.BuildingWebsite ||
              MediaSourceID === MediaSourceTypeEnum.BrokerWebsite || MediaSourceID === MediaSourceTypeEnum.ThridPartyWebsite) {
              this.mediaWEBsource = true;
              this.mediaForm.get('SourceComments').setValidators([Validators.required]);
            } else {
              this.mediaWEBsource = false;
              this.mediaForm.get('SourceComments').clearValidators();
            }
            this.mediaForm.get('SourceComments').updateValueAndValidity();
          });
      }
    }
  }

  backgroundSaveMedia() {
    this.mapChangeLog();
    if (this.media.IsOwnMedia != null) {
      this.media.IsOwnMedia = this.media.IsOwnMedia.toString();
    }
    if (this.IsAgentAdd) {
      this.media.IsDefault = 1;
    }

    this.media.ChangeLogJSON = JSON.stringify(this.DataArray);
    this.media.ApplicationID = EnumApplication.VST;
    this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    const status = this.media.PropertyID ? this.IndexedDBService.getIndexedDBInitializationStatus() : this.StagingIndexedDBService.getIndexedDBInitializationStatus()
    if (status) {
      (async () => {
        const attachments: { fileName: string, media: Media }[] = [];
        // Preparing supporting attachments to be uploaded
        this.files.forEach((x, index) => {
          const attachment = this.prepareAttachmentMediaUpload(index);
          attachments.push(attachment);
        })
        //storing in staging as property ID is not assigned yet.
        if (!this.media.PropertyID) {
          await this.StagingIndexedDBService.saveStagingMedia({
            mediaIdentifier: 'staging-' + this.fileObject.fileName,
            mediaInformation: {
              fileObject: this.fileObject,
              media: this.media,
              fileName: this.fileObject.fileName,
              attachments: attachments
            }
          });
        }
        else{
        // Uploading to IndexedDB
          await this.IndexedDBService.saveMedia({
            mediaIdentifier: this.media.PropertyID + '-' + this.fileObject.fileName,
            mediaInformation: {
              fileObject: this.fileObject,
              media: this.media,
              fileName: this.fileObject.fileName,
              attachments: attachments
            }
          });
        }
        this.showLoader = false;
        this.close();
      })();
    }
  }
  saveMedia() {
    if (this.mediaForm.valid) {
      this.mapChangeLog();
      if (this.media.IsOwnMedia != null) {
        this.media.IsOwnMedia = this.media.IsOwnMedia.toString();
      }
      if (this.IsAgentAdd) {
        this.media.IsDefault = 1;
      }

      this.media.ChangeLogJSON = JSON.stringify(this.DataArray);
      this.media.ApplicationID = EnumApplication.VST;
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
      const medias = this.mediaService.SaveMediaToDatabase(this.media);
      medias.subscribe(result => {
        const dataResult = result.body.responseData[0];
        if (this.media.IsDefault) {
          this.onSetDefault.emit(this.media.Path);
          this.sharedDataService.mediaData.Path = this.media.Path;
          this.sharedDataService.mediaData.PropertyID = this.media.PropertyID;

        }
        if (this.media.IsOwnMedia === '0') {
          if (this.files.length) {
            this.files.forEach((x, index) => {
              this.uploadAttachmentMedia(dataResult[0].MediaID, index);
            })
          }
        }
        if (this.fileObject) {
          this.fileObject.mediaId = 0;
        }
        if (!this.files.length) {
          this.showLoader = false;
          this.onSave.emit(this.media.Path);
        }
      });
    }
  }

  formatBytes(bytes) {
    if (bytes < 1024) {
      return bytes + ' Bytes';
    } else if (bytes < 1048576) {
      return (bytes / 1024).toFixed(3) + ' KB';
    } else if (bytes < 1073741824) {
      return (bytes / 1048576).toFixed(3) + ' MB';
    } else { return (bytes / 1073741824).toFixed(3) + ' GB' };
  };

  // .................... Change Log ........................
  getIsDefault=(mediaTypeID:number, mediaSubTypeID:number): number => Number(mediaTypeID === MediaTypeEnum.BuildingImage && mediaSubTypeID === MediaSubTypeEnum.MainPhoto)

  getFileNameFromMediaTypeName = (mediaTypeName:string): string => mediaTypeName + (mediaTypeName.toLowerCase().endsWith('image') ? '' : ' Image')

  getMediaFileNameFromMediaTypeID = (mediaTypeID: number):string => {
    const mediaTypeName = this.mediaTypes.find((media)=>media.MediaTypeID === mediaTypeID).MediaTypeName
    return this.getFileNameFromMediaTypeName(mediaTypeName)
  }

  getSelectedValue(Type, Value, MultipleMedia = null, index = null) {
    const date = new Date().toISOString();
    let id = null;
    if (!!MultipleMedia) {
      id = this.uploadFilesCopy[index][Type];
    } else {
      id = this.mediaCopy[Type];
    }
    let previousData;
    switch (Type) {
      case 'MediaName':
      case 'MediaTypeID':
        this.mediaTypes.forEach(element => {
          if (element.MediaTypeID === id) {
            previousData = element.MediaTypeName;
          }
        });
        this.media.MediaName= this.getFileNameFromMediaTypeName(Value)
        this.media.IsDefault = this.getIsDefault(this.media.MediaTypeID, this.media.MediaSubTypeID)
        break;
      case 'MediaSubTypeID':
        this.mediaSubTypes.forEach(element => {
          if (element.MediaSubTypeID === id) {
            previousData = element.MediaSubTypeName;
          }
        });
        this.media.IsDefault = this.getIsDefault(this.media.MediaTypeID, this.media.MediaSubTypeID)
        break;
      case 'Description':
      case 'IsDefault':
        Value === true ? previousData = false : previousData = true;
        break;
      case 'IsOwnMedia':
        this.sharedDataService.yesNoList.forEach(element => {
          if (element.ID === id) {
            previousData = element.Item;
          }
        });
        break;
      case 'MediaSourceID':
        this.mediaSource.forEach(element => {
          if (element.MediaSourceID === id) {
            previousData = element.MediaSourceName;
          }
        });
        break;
      case 'SourceComments':
      default:
        break;
    }

    if (!MultipleMedia) {
      const i = this.DataArray.findIndex(x => x.Field === Type);
      if (i !== -1) {
        this.DataArray.splice(i, 1);
      }
      this.DataArray.push({
        'Field': Type, 'CurrentValue': Value, 'PreviousValue': previousData,
        'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
      });
    } else {
      const i = this.uploadFiles[index].DataArray.findIndex(x => x.Field === Type);
      if (i !== -1) {
        this.uploadFiles[index].DataArray.splice(i, 1);
      }
      this.uploadFiles[index].DataArray.push({
        'Field': Type, 'CurrentValue': Value, 'PreviousValue': previousData,
        'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
      });
    }
  }

  mapChangeLog(media = null) {
    const data = this.mediaForm['controls'];
    const date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty === true) {
        const index = this.DataArray.findIndex(x => x.Field === i);
        if (index === -1) {
          this.DataArray.push({
            'Field': i, 'CurrentValue': data[i].value, 'PreviousValue': this.mediaCopy[i],
            'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
          });
        }
      }
    });
  }

  // .............................Attachment Source ...................

  fileUploadEvent(fileInput: any, dynamicMedia = null, index = null) {
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const fileObject = new FileObject(fileInput.target.files[i]);
        const media = new Media();
        media.File = fileInput.target.files[i];
        media.Ext = media.File.name.split('.').pop();
        if (this.mediaService.mediaCheck(media) === false) {
          this._notificationService.ShowErrorMessage('File not Supported');
          return;
        }
        if (fileInput.target.files && fileInput.target.files[i]) {
          const reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            media.UploadPathURL = event.target.result;
            const img = new Image;
            img.onload = function () {
              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
            this.fileExtentions(media)
          }
          reader.readAsDataURL(fileInput.target.files[i]);
        }
        if (!!dynamicMedia) {
          this.ImageFiles[index].uploadingFileArray.push(media);
        } else {
          this.files.push(media);
        }
      }
    }
    fileInput.target.value = null;
    if (!!dynamicMedia) {
      this.ImageFiles[index].fileArray = [];
      this.ImageFiles[index].uploadingFileArray.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.ImageFiles[index].fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    } else {
      this.fileArray = [];
      this.files.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    }
  }

  mediaAttachment(i) {
    this.fileObjectAttach[i] = new FileObject(this.files[i].File);
    this.files[i].Ext = this.media.Ext || this.files[i].File.name.split('.').pop();
    this.files[i].MediaName = this.files[i].File.name.split('.')[0];
    this.files[i].Size = this.files[i].File.size;
    this.attachfileSize = this.formatBytes(this.fileObjectAttach[i].file.size);
    this.files[i].MediaTypeID = MediaTypeEnum.OtherMedia;
    this.files[i].MediaSubTypeID = 0;
    this.files[i].CreatedBy = this._loginService.UserInfo.EntityID;
    this.files[i].IsDefault = 0;
    this.files[i].MediaID = 0;
    this.fileExtentions(this.files[i])
  }

  uploadAttachmentMedia(mediaId: number, i: number) {
    this.showLoader = true;
    this.IsAttachment = true;
    this.files[i].ModifiedBy = this._loginService.UserInfo.EntityID;
    this.files[i].RelationshipTypeID = MediaRelationTypeEnum.Media;
    this.files[i].RelationID = mediaId;
    this.files[i].PropertyID = this.initialDetails.PropertyID;
    this.mediaAttachment(i);
    const uploadHandleAttach = this.mediaService.uploadToS3(this.fileObjectAttach[i].fileName, this.files[i].UploadPathURL);
    uploadHandleAttach.subscribe(result => {
      if (!result.body.error) {
        this.fileArray[i].IsUploaded = true;
        this.count++;
        this.saveAttachmentMedia(i);
      }
    });
  }

  /**
    * Method for Preparing supporting attachment to be uploaded
    * @param { number } index Attachment File Index
  */
  prepareAttachmentMediaUpload = (index: number) => {
    this.files[index].ModifiedBy = this._loginService.UserInfo.EntityID;
    this.files[index].RelationshipTypeID = MediaRelationTypeEnum.Media;
    this.files[index].PropertyID = this.initialDetails.PropertyID;
    this.mediaAttachment(index);
    return { fileName: this.fileObjectAttach[index].fileName, media: this.files[index] };
  }

  saveAttachmentMedia(i: number) {
    this.files[i].Path = this.fileObjectAttach[i].fileName;
    const medias = this.mediaService.SaveMediaToDatabase(this.files[i]);
    medias.subscribe(result => {
      if (this.fileObjectAttach[i]) {
        this.fileObjectAttach[i].mediaId = 0;
      }
      if (this.files.length === this.count) {
        this.showLoader = false;
        this.onSave.emit(this.media.Path);
      }
    });
  }

  getAttachmentMedia() {
    this.fileArray = [];
    this.initialAttachmentDetails = new ContactMedia();
    this.initialAttachmentDetails.RelationID = this.media.MediaID;
    this.initialAttachmentDetails.RelationshipTypeID = MediaRelationTypeEnum.Media;
    this.initialAttachmentDetails.PropertyID = this.media.PropertyID;
    if (this.initialAttachmentDetails) {
      const medias = this.mediaService.getAllMediaRelation(this.initialAttachmentDetails);
      medias.subscribe(result => {
        if (!result.body.error) {
          this.fileArray = result.body.responseData[0];
          this.fileArray.forEach(x => {
            x.Size = this.formatBytes(x.Size);
            x.OrginalURL = environment.MediaS3Base + environment.MediaS3Path + '/' + x.Path;
          });
          this.fileArray = this.fileArray.sort((a, b) => b.CreatedDate.localeCompare(a.CreatedDate));
        }
      });
    }
  }

  mediaSourceChange(event: number) {
    if (event === 1) {
      this.onSourceChange.emit(false);
    } else {
      this.onSourceChange.emit(true);
    }
  }

  deleteAddedFiles(i: number, dynamicMedia = null, dynamicIndex = null) {
    if (!!dynamicMedia) {
      this.ImageFiles[dynamicIndex].fileArray.splice(i, 1);
      this.ImageFiles[dynamicIndex].uploadingFileArray.splice(i, 1);
    } else {
      this.fileArray.splice(i, 1);
      this.files.splice(i, 1);
    }
  }

}
