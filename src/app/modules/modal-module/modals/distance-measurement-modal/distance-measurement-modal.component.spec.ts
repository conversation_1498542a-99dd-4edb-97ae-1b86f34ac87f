import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DistanceMeasurementModalComponent } from './distance-measurement-modal.component';

describe('DistanceMeasurementModalComponent', () => {
  let component: DistanceMeasurementModalComponent;
  let fixture: ComponentFixture<DistanceMeasurementModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ DistanceMeasurementModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DistanceMeasurementModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
