import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

import { MapService } from '../../../map-module/service/map-service.service';
import { MapOptions } from '../../../map-module/models/MapOptions';
import * as MapEnum from '../../../map-module/models/MapEnum';

declare var google: any;

@Component({
  selector: 'app-distance-measurement-modal',
  templateUrl: './distance-measurement-modal.component.html',
  styleUrls: ['./distance-measurement-modal.component.scss']
})
export class DistanceMeasurementModalComponent implements OnInit {

  @Output() onClose = new EventEmitter();
  @Input() latLng: { lat: number, lng: number };
  @Output() onRetailFrontageSave: EventEmitter<any> = new EventEmitter<any>();
  @Input() retailFrontagePolyline: { distance: number, polyline: any };
  public mapOptions: MapOptions;
  public map: any;
  private _mapService: MapService;
  drawingManager: any;
  public distance: number;
  polyline: any;

  constructor(
    mapService: MapService
  ) {
    this._mapService = mapService;
  }

  ngAfterViewInit() {
  }

  ngOnInit() {
    this.initMap();
  }

  DrawShape(shape) {
    let instance = this;
    this.drawingManager = this._mapService.DrawPolygon(this.map, shape);
    this.drawingManager.setOptions({ drawingControl: true });
    if (shape == "polyline") {
      this._mapService.OnMapOverlayComplete(this.drawingManager, MapEnum.DrawMode.Polyline, (event) => {
        this.map.setOptions({ draggable: true });

        const path = event.getPath();
        const threshold = 10;
        const firstPoint = path.getAt(0);
        const lastPoint = path.getAt(path.getLength() - 1);
    
        // Compute the distance between first and last points
        const distance = google.maps.geometry.spherical.computeDistanceBetween(firstPoint, lastPoint);
        if (distance < threshold) {
          // Remove the last point to eliminate the closing segment
          path.removeAt(path.getLength() - 1);
        }
        this.drawingManager.setDrawingMode(null);
        const length: number = google.maps.geometry.spherical.computeLength(path);
        if (this.polyline) {
          this.clearPolygon();
        }
        this.distance = parseFloat(length.toFixed(2));
        this.polyline = event;

        google.maps.event.addListener(path, 'set_at', () => {
          instance.preparedPloygonsData(event);
        });

        // Add event listener for new vertex insertion
        google.maps.event.addListener(path, 'insert_at', () => {
          instance.preparedPloygonsData(event);
        });
      });
    }
  }

  preparedPloygonsData(event) {
    var newDistance = google.maps.geometry.spherical.computeLength(event.getPath());
    newDistance = parseFloat(newDistance.toFixed(2));
    this.distance = newDistance;
  }

  clearPolygon() {
    if (this.polyline) {
      this._mapService.ClearPolygon(this.polyline);
      this.distance = null;
    }
  }

  onSave() {
    this.onRetailFrontageSave.emit({ distance: this.distance, polyline: this.polyline });
    this.onClose.emit(false);
  }

  private initMap() {
    this.mapOptions = new MapOptions('map-property-space');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 20, 7, null, this.latLng.lat, this.latLng.lng);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.FullscreenControl = false;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    this.DrawShape("polyline");
    if (this.retailFrontagePolyline) {
      this.distance = this.retailFrontagePolyline.distance;
      this.polyline = this.retailFrontagePolyline.polyline;
      this.polyline.setMap(this.map);

      google.maps.event.addListener(this.polyline.getPath(), 'set_at', () => {
        this.preparedPloygonsData(event);
      });

      // Add event listener for new vertex insertion
      google.maps.event.addListener(this.polyline.getPath(), 'insert_at', () => {
        this.preparedPloygonsData(event);
      });
    }
  }

  addControllers() {
    this._mapService.AddController(this.map, "clearShapesInBM", MapEnum.GoogleMapControlPosition.Top_Left);
  }
  
  closeParkingSpace() {
    this.onClose.emit();
  }

}
