<div class="row">
  <div class="col-sm-8">
    <div class="parkingmap" [style.display]="'block'"
      [style.height]="'60vh'" [style.width]="''">
      <div id="map-property-space" class="map"></div>
    </div>
  </div>
  <div class="col-sm-4 no-padding">
    <div class="form-group row">
      <label class="col-md-9 form-control-label" for="text-input">Distance (Meters)
      </label>
      <div class="col-md-9 position-relative">
        <i class="fa fa-close icon" (click)="clearPolygon()"></i>
        <input type="text" disabled="true" class="form-control" [(ngModel)]="distance">
      </div>
    </div>
    <input *ngIf="distance" type="button" class="btn btn-primary" value="Save" (click)="onSave()" />
  </div>
  <!-- Map Switch component -->
  <app-map-switch id="map-switch-distance-measurement" [map]="map" (onAddMapSwitchBtnControler)="addControllers()" *ngIf="map"></app-map-switch>
</div>
