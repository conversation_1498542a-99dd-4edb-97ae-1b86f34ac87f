import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AdditionalAddressModalComponent } from './additional-address-modal.component';

describe('AdditionalAddressModalComponent', () => {
  let component: AdditionalAddressModalComponent;
  let fixture: ComponentFixture<AdditionalAddressModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AdditionalAddressModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AdditionalAddressModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
