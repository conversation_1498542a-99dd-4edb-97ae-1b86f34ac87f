import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormControl, FormArray, NgForm, Validators, FormBuilder } from '@angular/forms';
import { PropertyLocation } from '../../../../models/PropertyLocation';
import { PropertyService } from '../../../../services/api-property.service';
import { PropertyAdditionalAddress } from '../../../../models/PropertyAdditionalAddress';
import { LoginService } from '../../../../services/login.service';
import { EnumApplication } from '../../../../enumerations/application';

@Component({
  selector: 'app-additional-address-modal',
  templateUrl: './additional-address-modal.component.html',
  styleUrls: ['./additional-address-modal.component.scss']
})
export class AdditionalAddressModalComponent implements OnInit {


  propertyForm: FormGroup;
  streetPrefixes: any;
  streetSufixes: any;
  quadrants: any;
  states: any;
  CountryId: number;
  counties: any;
  cities: any;
  @Input() editAdditionalAddress: PropertyAdditionalAddress;
  @Input() propertyLocation: PropertyLocation;
  additionalAddress: PropertyAdditionalAddress;
  currentAddressID: number;
  DataArray: Array<any> = [];
  @Output() onSaveAdditionalAddress: EventEmitter<PropertyAdditionalAddress> = new EventEmitter<PropertyAdditionalAddress>();
  @Output() onClose: EventEmitter<PropertyAdditionalAddress> = new EventEmitter<PropertyAdditionalAddress>();
  selectedAdditionalAddressCopy: PropertyAdditionalAddress = new PropertyAdditionalAddress();
  public addressTypeValues: any = { Address: 0, Intersection: 1 };

  constructor(private _propertyService: PropertyService,private _loginService: LoginService) {
  }

  ngOnInit() {
    this.additionalAddress = new PropertyAdditionalAddress();
    this.CountryId = this.propertyLocation.CountryID;
    this.additionalAddress = this.editAdditionalAddress;
    this.DataArray =[];
    this.selectedAdditionalAddressCopy = new PropertyAdditionalAddress();
    this.selectedAdditionalAddressCopy = JSON.parse(JSON.stringify(this.editAdditionalAddress));
    this.createForm();
    this.populateDropDowns(() => {
    });

    this.loadStateList(this.propertyLocation.CountryID, () => {
      this.GetCity();
      this.GetCounty();
    });

    setTimeout(() => {
      this.prePopulateData();
    }, 400);

  }

  createForm() {
    this.propertyForm = new FormGroup({
      'StreetNumberMin': new FormControl('', Validators.required),
      'StreetNumberMax': new FormControl(''),
      'Direction': new FormControl(''),
      'AddressStreetName': new FormControl('', Validators.required),
      'streetSuffix1': new FormControl(''),
      'streetSuffix2': new FormControl(''),
      'zipcode': new FormControl(''),
      'state': new FormControl(''),
      'city': new FormControl(''),
      'county': new FormControl(''),
      'quadrant': new FormControl(''),
      'NorthSouthStreet': new FormControl(''),
      'EastWestStreet': new FormControl(''),
      'Latitude': new FormControl(''),
      'Longitude': new FormControl(''),
      'BuildingNumber': new FormControl(''),
      'AddressType': new FormControl('', Validators.required)
    });

    this.propertyForm?.get('AddressType')?.valueChanges?.subscribe(
      (AddressType) => {
        if (AddressType === this.addressTypeValues.Address) {
          this.propertyForm?.get('quadrant')?.clearValidators();
          this.propertyForm?.get('EastWestStreet')?.clearValidators();
          this.propertyForm?.get('NorthSouthStreet')?.clearValidators();

          this.propertyForm?.get('StreetNumberMin')?.setValidators([Validators.required]);
          this.propertyForm?.get('AddressStreetName')?.setValidators([Validators.required]);

        } else {
          this.propertyForm?.get('StreetNumberMin')?.clearValidators();
          this.propertyForm?.get('AddressStreetName')?.clearValidators();

          this.propertyForm?.get('quadrant')?.setValidators([Validators.required]);
          this.propertyForm?.get('EastWestStreet')?.setValidators([Validators.required]);
          this.propertyForm?.get('NorthSouthStreet')?.setValidators([Validators.required]);
        }
        this.propertyForm?.get('StreetNumberMin')?.updateValueAndValidity();
        this.propertyForm?.get('AddressStreetName')?.updateValueAndValidity();
        this.propertyForm?.get('quadrant')?.updateValueAndValidity();
        this.propertyForm?.get('EastWestStreet')?.updateValueAndValidity();
        this.propertyForm?.get('NorthSouthStreet')?.updateValueAndValidity();
      }
    )
  }

  private populateDropDowns(onDropdownsPopulated: () => void) {
    let apiCallCounter = 0;

    apiCallCounter++;
    const response_StreetPrefixes = this._propertyService.GetAllPrefix();
    response_StreetPrefixes.subscribe(result => {
      if (!result.body.error)
        this.streetPrefixes = result.body.responseData;
      apiCallCounter--;
      if (apiCallCounter == 0) {
        if (onDropdownsPopulated) {
          onDropdownsPopulated();
        }
      }
    });

    apiCallCounter++;
    const response_StreetSufixes = this._propertyService.GetAllSuffix();
    response_StreetSufixes.subscribe(result => {
      if (!result.body.error)
        this.streetSufixes = result.body.responseData;

      apiCallCounter--;
      if (apiCallCounter == 0) {
        if (onDropdownsPopulated) {
          onDropdownsPopulated();
        }
      }
    });

    apiCallCounter++;
    const response_quadrants = this._propertyService.GetAllQuadrants();
    response_quadrants.subscribe(result => {
      if (!result.body.error)
        this.quadrants = result.body.responseData;

      apiCallCounter--;
      if (apiCallCounter == 0) {
        if (onDropdownsPopulated) {
          onDropdownsPopulated();
        }
      }
    });
  }

  private loadStateList(countryId, onLoadingState: () => void) {

    const response_States_By_Country = this._propertyService.GetStateListByCountryId(countryId);
    response_States_By_Country.subscribe(result => {
      if (!result.body.error) {
        this.states = result.body.responseData.StateList || [];
      }
      onLoadingState();
    });

  }

  GetCity = function () {
    const response_cities = this._propertyService.GetCitiesByState(this.propertyLocation.State);
    response_cities.subscribe(result => {
      if (!result.body.error) {
        this.cities = result.body.responseData;
      }

    });
  }

  GetCounty() {
    const response_counties = this._propertyService.GetCountiesByState(this.propertyLocation.State);
    response_counties.subscribe(result => {
      if (!result.body.error) {
        this.counties = result.body.responseData.CountyList;
      }

    });
  }
  prePopulateData() {
    if (this.additionalAddress?.AddressID === 0 || !this.additionalAddress?.AddressID) {
    this.additionalAddress.CountryID = this.propertyLocation.CountryID;
    this.additionalAddress.StateID = this.propertyLocation.State?this.propertyLocation.State:0;
    this.additionalAddress.CountyID = this.propertyLocation.County?this.propertyLocation.County:0;
    this.additionalAddress.CityID = this.propertyLocation.City?this.propertyLocation.City:0;
    this.additionalAddress.ZipCode = this.propertyLocation.Zip;
    this.additionalAddress.Latitude = this.propertyLocation.Latitude;
    this.additionalAddress.Longitude = this.propertyLocation.Longitude;
    this.additionalAddress.PropertyID = this.propertyLocation.PropertyID;
    this.additionalAddress.EntityID = this.propertyLocation.EntityID;
    }
  }


  saveAdditionalAddress() {

    if (this.propertyForm.valid)
    {
      if (this.additionalAddress.PrefixID == 0)
      this.additionalAddress.PrefixID = null;
      if (this.additionalAddress.StateID == 0)
        this.additionalAddress.StateID = null;
      if (this.additionalAddress.CityID == 0)
        this.additionalAddress.CityID = null;
      if (this.additionalAddress.CountyID == 0)
        this.additionalAddress.CountyID = null;
      if (this.additionalAddress.QuadrantID == 0)
        this.additionalAddress.QuadrantID = null;
      if (this.additionalAddress.SuffixID == 0)
        this.additionalAddress.SuffixID = null;
      if (this.additionalAddress.Suffix2ID == 0)
        this.additionalAddress.Suffix2ID = null;

    this.mapChangeLog();     
    this.additionalAddress.IsActive = true;
    this.additionalAddress.ChangeLogJSON = JSON.stringify(this.DataArray);
    this.additionalAddress.ApplicationID = EnumApplication.VST;

    const response_location = this._propertyService.savePropertyAdditionalAddress(this.additionalAddress);
    response_location.subscribe(result => {
   
      if (!result.body.error) {
        const AddressID = result.body.responseData.Property[0][0].AddressID;
        this.additionalAddress.AddressID = AddressID;
        this.onSaveAdditionalAddress.emit(this.additionalAddress);
        this.onClose.emit(this.additionalAddress);
      }

    });

  } 

  }

  mapChangeLog() {
    const data = this.propertyForm['controls'];
    const date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty === true) {
        const index = this.DataArray.findIndex(x => x.Field === i);
        if (index === -1) {
          this.DataArray.push({
            'Field': i, 'CurrentValue': data[i].value, 'PreviousValue': this.selectedAdditionalAddressCopy[i],
            'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
          });
        }
      }
    });
  }
  getSelectedValue(Type, event, ValueName) {
    let Value = null;
    if (!!event) {
      Value = event[ValueName];
    } else {
      Value = null;
    }
    const date = new Date().toISOString();
    const id = this.selectedAdditionalAddressCopy[Type];
    let previousData;
    switch (Type) {
      case 'PrefixID':
        this.streetPrefixes.forEach(element => {
          if (element.PrefixID === id) {
            previousData = element.Prefix;
          }
        });
        break;
    case 'SuffixID':
          this.streetSufixes.forEach(element => {
            if (element.SuffixId === id) {
              previousData = element.Suffix;
            }
          });
          break;
    case 'Suffix2ID':
          this.streetSufixes.forEach(element => {
            if (element.SuffixId === id) {
              previousData = element.Suffix;
            }
          });
          break;
    case 'CityID':
            this.cities.forEach(element => {
              if (element.CityID === id) {
                previousData = element.CityName;
              }
            });
          break;
    case 'CountyID':
          this.counties.forEach(element => {
            if (element.CountyID === id) {
              previousData = element.CountyName;
            }
          });
          break;
    case 'StateID':
          this.states.forEach(element => {
            if (element.StateID === id) {
              previousData = element.StateName;
            }
          });
          break;
    case 'QuadrantID':
          this.quadrants.forEach(element => {
            if (element.QuadrantID === id) {
              previousData = element.QuadrantName;
            }
          });
          break;
      default:
        break;
    }
    const i = this.DataArray.findIndex(x => x.Field === Type);
    if (i !== -1) {
      this.DataArray.splice(i, 1);
    }
      this.DataArray.push({
        'Field': Type, 'CurrentValue': Value, 'PreviousValue': previousData,
         'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
      });
  }
  addressTypeChange(Type, Value) {
    const date = new Date().toISOString();
    const currentValue = Value === 0 ? 'Address' : 'Intersection';
    const previousValue = this.selectedAdditionalAddressCopy.AddressType === 0 ? 'Address' : 'Intersection';
    const i = this.DataArray.findIndex(x => x.Field === Type);
    if (i !== -1) {
      this.DataArray.splice(i, 1);
    }
    if (this.propertyForm['controls'][Type].dirty) {
      this.DataArray.push({
        'Field': Type, 'CurrentValue': currentValue, 'PreviousValue': previousValue,
        'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
      });
    }

  }

}  
