<div class="row">
  <div class="col-md-12">
    <form [formGroup]="additionalUseForm" (ngSubmit)="addAdditionalUse()">
      <div class="row">
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Use Type</div>
            <div class="col-md-7">
              <ng-select formControlName="UseTypeID" [items]="propertyTypes" [virtualScroll]="true"
                bindLabel="UseTypeName" bindValue="UseTypeID" labelForId="UseTypeID" placeholder="--Select--"
                [(ngModel)]="additionalUse.UseTypeID"
                [ngClass]="{'error-field':(!additionalUseForm.controls['UseTypeID'].valid)}"
                (change)="getSelectedValue('UseTypeID',$event,'UseTypeName')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Specific Use</div>
            <div class="col-md-7">
              <ng-select formControlName="SpecificUsesID" [items]="specificUses" [virtualScroll]="true"
                bindLabel="SpecificUsesName" bindValue="SpecificUsesID" labelForId="SpecificUsesID"
                placeholder="--Select--" [(ngModel)]="additionalUse.SpecificUsesID"
                [ngClass]="{'error-field':(!additionalUseForm.controls['SpecificUsesID'].valid)}"
                (change)="getSelectedValue('SpecificUsesID',$event,'SpecificUsesName')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Floors</div>
            <div class="col-md-7">
              <input type="text" integer maxlength="11" class="form-control" formControlName="Floors"
                [(ngModel)]="additionalUse.Floors"
                [ngClass]="{'error-field':(!additionalUseForm.controls['Floors'].valid)}" maxlength="5">
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">
              <span *ngIf="UnitId == metricUnit">Avg Floor Size {{UnitDisplayTextSize}}</span>
              <span *ngIf="UnitId != metricUnit">Avg Floor Size {{UnitDisplayTextSize}}</span>
            </div>
            <div class="col-md-7">
              <input type="text" currencyMask [options]="maskOptions_precision_zero" class="form-control"
                formControlName="AvgFloorSizeSF" [(ngModel)]="additionalUse.AvgFloorSizeSF"
                [ngClass]="{'error-field':(!additionalUseForm.controls['AvgFloorSizeSF'].valid)}">
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Notes</div>
            <div class="col-md-7">
              <textarea rows="4" class="form-control" formControlName="Notes" [(ngModel)]="additionalUse.Notes"
                [ngClass]="{'error-field':(!additionalUseForm.controls['Notes'].valid)}"></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer-bottom col-md-12">
          <button class="pull-right btn btn-warn" type="submit"><i class="fa fa-plus"></i>Add</button>
        </div>
      </div>
    </form>
  </div>

</div>
