import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PropertyAdditionalUseModalComponent } from './property-additional-use-modal.component';

describe('PropertyAdditionalUseModalComponent', () => {
  let component: PropertyAdditionalUseModalComponent;
  let fixture: ComponentFixture<PropertyAdditionalUseModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PropertyAdditionalUseModalComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PropertyAdditionalUseModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
