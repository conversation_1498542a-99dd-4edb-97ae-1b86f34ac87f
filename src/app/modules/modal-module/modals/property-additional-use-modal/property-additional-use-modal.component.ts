import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { PropertyUse } from '../../../../models/PropertyUse';
import { LookupDataService } from '../../../../services/api-lookup-data.service';
import { SharedDataService } from '../../../../services/shareddata.service';
import { LoginService } from '../../../../services/login.service';
import { UnitConversionEnum } from '../../../../enumerations/unitConversion';
@Component({
  selector: 'app-property-additional-use-modal',
  templateUrl: './property-additional-use-modal.component.html',
  styleUrls: ['./property-additional-use-modal.component.scss']
})
export class PropertyAdditionalUseModalComponent implements OnInit {

  @Input() additionalUse: PropertyUse;
  @Output() onClose = new EventEmitter();
  @Output() onSave = new EventEmitter<PropertyUse>();
  additionalUseCopy: PropertyUse;
  additionalUseForm: FormGroup;
  specificUses: any;
  propertyTypes: any;
  maskOptions_precision_zero: any;
  DataArray: any = [];
  UnitId: number;
  metricUnit: number = 1;
  UnitDisplayTextSize: any;
  constructor(private _lookupService: LookupDataService
    , private _sharedDataService: SharedDataService, private _loginService: LoginService) {

    this.maskOptions_precision_zero = { prefix: '', precision: 0 };

    if (!!this._sharedDataService.specificUseList && this._sharedDataService.specificUseList.length > 0) {
      this.specificUses = this._sharedDataService.specificUseList;
    } else {
      const response_specificuses = this._lookupService.GetAllSpecificUse();
      response_specificuses.subscribe(result => {
        this.specificUses = result.body.responseData || [];
        this._sharedDataService.specificUseList = this.specificUses;
      });
    }

    if (!!this._sharedDataService.propertyTypeList && this._sharedDataService.propertyTypeList.length > 0) {
      this.propertyTypes = this._sharedDataService.propertyTypeList;
    } else {
      const response_Propertytype = this._lookupService.GetAllPropertyType();
      response_Propertytype.subscribe(result => {
        this.propertyTypes = result.body.responseData || [];
        this._sharedDataService.propertyTypeList = this.propertyTypes;
      });
    }
    this.UnitId = this._loginService.UserInfo.UnitID;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.metricUnit = UnitConversionEnum.Metric;
  }

  ngOnInit() {
    this.additionalUseCopy = JSON.parse(JSON.stringify(this.additionalUse));
    this.createForm();
  }

  createForm() {
    this.additionalUseForm = new FormGroup({
      'UseTypeID': new FormControl('', Validators.required),
      'SpecificUsesID': new FormControl(''),
      'Floors': new FormControl(''),
      'AvgFloorSizeSF': new FormControl(''),
      'Notes': new FormControl('')
    });
  }

  close() {
    this.onClose.emit();
  }

  addAdditionalUse() {
    if (this.additionalUseForm.valid) {
      this.mapChangeLog();
      let response: any = {};
      if (this.additionalUse.UseTypeID) {
        response.PropertyType = this.propertyTypes.filter(x => { if (x.UseTypeID == this.additionalUse.UseTypeID) { return x; } })[0];
        this.additionalUse.UseTypeName = response.PropertyType.UseTypeName;
      }
      if (this.additionalUse.SpecificUsesID) {
        response.SpecificUse = this.specificUses.filter(x => { if (x.SpecificUsesID == this.additionalUse.SpecificUsesID) { return x; } })[0];
        this.additionalUse.SpecificUsesName = response.SpecificUse.SpecificUsesName;
      }
      this.additionalUse.IsActive = 1;
      this.additionalUse.ChangeLogJSON = JSON.stringify(this.DataArray);
      this.onSave.emit(this.additionalUse);
    }
  }
  getSelectedValue(Type, event, ValueName) {
    let Value = null;
    if (!!event) {
      Value = event[ValueName];
    }
    else {
      Value = null;
    }
    let date = new Date().toISOString();
    var id = this.additionalUseCopy[Type];
    let previousData;
    switch (Type) {
      case 'UseTypeID':
        this.propertyTypes.forEach(element => {
          if (element.UseTypeID == id)
            previousData = element.UseTypeName;
        });
        break;

      case 'SpecificUsesID':
        this.specificUses.forEach(element => {
          if (element.SpecificUsesID == id)
            previousData = element.SpecificUsesName;
        });
        break;
      default:
        break;
    }
    let i = this.DataArray.findIndex(x => x.Field == Type);
    if (i != -1) {
      this.DataArray.splice(i, 1);
    }
    this.DataArray.push({
      "Field": Type, "CurrentValue": Value, "PreviousValue": previousData, "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
    });
  }
  mapChangeLog() {
    let data = this.additionalUseForm["controls"];
    let date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty == true) {
        let index = this.DataArray.findIndex(x => x.Field == i);
        if (index == -1) {
          this.DataArray.push({
            "Field": i, "CurrentValue": data[i].value, "PreviousValue": this.additionalUseCopy[i], "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
          });
        }
      }
    });
  }
}
