import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { LookupDataService } from '../../../../services/api-lookup-data.service';
import { SharedDataService } from '../../../../services/shareddata.service';
import { LoginService } from '../../../../services/login.service';
import { PropertyAllocation } from '../../../../models/propertyAllocation';
import { NotificationService } from '../../../notification/service/notification.service';
import { UnitConversionEnum } from '../../../../enumerations/unitConversion';
import { PropertyTypes } from '../../../../enumerations/enums';
import { PropertyService } from '../../../../services/api-property.service';
import { CommonStrings } from '../../../../constants';
import { AddPropertyAllocationKeys } from '../../../../enumerations/addAllocationModalKeys';

@Component({
  selector: 'app-add-propertyallocation-modal',
  templateUrl: './add-propertyallocation-modal.component.html',
  styleUrls: ['./add-propertyallocation-modal.component.scss']
})
export class AddPropertyAllocationModalComponent implements OnInit {
  @Input() propertyAllocation: PropertyAllocation = new PropertyAllocation;
  @Input() totalSpaceAvailable: number = 0; //space allocated towards default allocation
  @Output() onClose = new EventEmitter();
  @Output() onSave = new EventEmitter<PropertyAllocation>();
  @Input() allocationList: Array<PropertyAllocation> = new Array<PropertyAllocation>();
  @Input() totalFloors: number;
  @Input() floorSize: any;
  @Input() allocatedFloorsWithPercent: any;

  private _propertyService: PropertyService;
  propertyAllocationCopy: PropertyAllocation;
  propertyAllocationForm: FormGroup;
  specificUses: any;
  allSpecificUses: any;
  propertyTypes: any;
  propertyFilterTypes: any = [];
  maskOptions_precision_two: any;
  DataArray: any = [];
  prevFloorSM: number = 0;
  UnitId: number;
  metricUnit: number = 1;
  UnitDisplayTextSize: any;
  floorsList: any;
  previousAllocation = 0;
  constructor(private _lookupService: LookupDataService
    , private _sharedDataService: SharedDataService
    , private _loginService: LoginService,
    propertyService: PropertyService,
    private _notificationService: NotificationService) {

    this.maskOptions_precision_two = { prefix: '' };
    this._propertyService = propertyService;

    if (!!this._sharedDataService.specificUseList && this._sharedDataService.specificUseList.length > 0) {
      this.specificUses = this._sharedDataService.specificUseList;
    } else {
      const response_specificuses = this._lookupService.GetAllSpecificUse();
      response_specificuses.subscribe(result => {
        this.allSpecificUses = result.body.responseData || [];
        this._sharedDataService.specificUseList = this.allSpecificUses;
      });
    }
    this.UnitId = this._loginService.UserInfo.UnitID;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;

    this.metricUnit = UnitConversionEnum.Metric;
    
  }

  ngOnInit() {
    if (this.propertyAllocation.PropertyAllocationID) {
      this.setMinFloorNumber(Number(this.propertyAllocation.MinFloorNumber));
      this.setMaxFloorNumber(Number(this.propertyAllocation.MaxFloorNumber));
      const response_constructtype = this._propertyService.GetSpecificUseByGenUseId(this.propertyAllocation.UseTypeID);
      response_constructtype.subscribe(result => {
        this.specificUses = result.body.responseData || [];
      });
      this.previousAllocation = Number(this.propertyAllocation.Floors) * 100 / Number(this.totalFloors);
      const min = this.propertyAllocation.MinFloorNumber;
      const max = this.propertyAllocation.MaxFloorNumber;
      if (min && !max) {
        const floors = Number(parseFloat(this.propertyAllocation.Floors).toFixed(2));
        if (floors > 0 && floors < 1) {
          this.allocatedFloorsWithPercent[min] = Number(this.allocatedFloorsWithPercent[min]) == 100 ? (1 - Number(this.propertyAllocation.Floors)).toFixed(2) : (this.allocatedFloorsWithPercent[min] - Number(this.propertyAllocation.Floors)).toFixed(2);
        } else if (floors == 1) {
          this.allocatedFloorsWithPercent[min] = 0;
        }
      }
      if (min && max) {
        for (let i = min; i <= max; i++) {
          this.allocatedFloorsWithPercent[i] = 0;
        }
      }
    }
    this.propertyAllocationCopy = JSON.parse(JSON.stringify(this.propertyAllocation));
    this.prevFloorSM = this.propertyAllocation.FloorSizeSM;
    this.createForm();
    if (!!this._sharedDataService.propertyTypeList && this._sharedDataService.propertyTypeList.length > 0) {
      this.propertyTypes = this._sharedDataService.propertyTypeList;
      this.propertyTypes = this.propertyTypes.filter(propertyType => propertyType.IsActive == 1 && propertyType.UseTypeID != PropertyTypes.Land); //exclude Land allocation
    } else {
      const response_Propertytype = this._lookupService.GetAllPropertyType();
      response_Propertytype.subscribe(result => {
        this.propertyTypes = result.body.responseData || [];
        this.propertyTypes = this.propertyTypes.filter(propertyType => propertyType.IsActive == 1 && propertyType.UseTypeID != PropertyTypes.Land); //exclude Land allocation
        this._sharedDataService.propertyTypeList = this.propertyTypes;
      });

    }

    const floorSelectItems = [];
    for (let i = 0; i < this.totalFloors; i++) {
      floorSelectItems.push({ FloorNumberLabel: i == 0 ? 'G' : i, FloorNumber: i + 1 });
    }
    this.floorsList = [...floorSelectItems];
    this.propertyAllocation.FloorSizeSM = this.floorSize;
  }

  createForm() {
    this.propertyAllocationForm = new FormGroup({
      'UseTypeID': new FormControl('', Validators.required),
      'SpecificUsesID': new FormControl(''),
      'Floors': new FormControl(''),
      'FloorSizeSM': new FormControl(''),
      'Notes': new FormControl(''),
      'MinFloorNumber': new FormControl('', Validators.required),
      'MaxFloorNumber': new FormControl(undefined),
    });

    if (this.propertyAllocation.PropertyAllocationID == 0) {
      this.propertyAllocationForm.get('UseTypeID').markAsTouched();
      this.propertyAllocationForm.get('UseTypeID').enable();

    }
    else {
      this.propertyAllocationForm.get('UseTypeID').markAsUntouched();
      this.propertyAllocationForm.get('UseTypeID').disable();
    }
    this.propertyAllocationForm.get('UseTypeID').updateValueAndValidity();
  }

  close() {
    this.onClose.emit();
  }

  addpropertyAllocation() {
    if (this.propertyAllocationForm.valid && this.propertyAllocation.FloorSizeSM > 0) {
      this.mapChangeLog();
      let response: any = {};
      if (this.propertyAllocation.UseTypeID) {
        response.PropertyType = this.propertyTypes.filter(x => { if (x.UseTypeID == this.propertyAllocation.UseTypeID) { return x; } })[0];
        this.propertyAllocation.UseTypeName = response.PropertyType.UseTypeName;
      }
      if (this.propertyAllocation.SpecificUsesID) {
        response.SpecificUse = this.specificUses.filter(x => { if (x.SpecificUsesID == this.propertyAllocation.SpecificUsesID) { return x; } })[0];
        this.propertyAllocation.SpecificUsesName = response.SpecificUse.SpecificUsesName;
      }
      this.propertyAllocation.IsActive = 1;
      this.propertyAllocation.IsDefault = 0;
      this.propertyAllocation.ChangeLogJSON = JSON.stringify(this.DataArray);
      this.onSave.emit(this.propertyAllocation);
    }
  }

  minFloorValidations(event) {
    if(this.propertyAllocation.MaxFloorNumber && this.propertyAllocation.MaxFloorNumber < this.propertyAllocation.MinFloorNumber) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.MinAndMaxFloorCountError);
      this.setMaxFloorNumber(undefined);
    }
    if (this.allocatedFloorsWithPercent[event.FloorNumber] == 100) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FloorAllocatedMessage);
      this.setMinFloorNumber(undefined);
    } else if(this.allocatedFloorsWithPercent[event.FloorNumber] > 0) {
      if(this.propertyAllocation.MaxFloorNumber) {
        this.setMaxFloorNumber(undefined);
      }
      // If part of floor is allocated then autopopulating the remaining floor count
      this.setFloorCount((1 - this.allocatedFloorsWithPercent[event.FloorNumber]).toFixed(2));
    } else {
      if (!this.propertyAllocation.MaxFloorNumber) {
        this.setFloorCount('1');
        const currentAllocation = Number(this.propertyAllocation.Floors) * 100 / Number(this.totalFloors) ;
        const allocationAvailable = this.totalSpaceAvailable + this.previousAllocation;
        if (currentAllocation > allocationAvailable) {
          this.setFloorCount(undefined);
          this.setMaxFloorNumber(undefined);
          this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.AvailableAllocationExceeded);
        }
      }
      if (!event) {
        this.setFloorCount('0');
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.SelectMinFloorNumber);
      }
    }
  }

  setFloorCount(value = undefined) {
    this.propertyAllocationForm && this.propertyAllocationForm.controls[AddPropertyAllocationKeys.Floors].reset();
    this.propertyAllocation.Floors = value;
  }

  setMinFloorNumber(value = undefined) {
    this.propertyAllocationForm && this.propertyAllocationForm.controls[AddPropertyAllocationKeys.MinFloorNumber].reset();
    this.propertyAllocation.MinFloorNumber = value;
  }

  setMaxFloorNumber(value = undefined) {
    this.propertyAllocationForm && this.propertyAllocationForm.controls[AddPropertyAllocationKeys.MaxFloorNumber].reset();
    this.propertyAllocation.MaxFloorNumber = value;
  }

  maxFloorValidations(event) {
    if(!event && this.propertyAllocation.MinFloorNumber && !this.propertyAllocation.MaxFloorNumber) {
      this.setFloorCount('1');
    }
    if ((event && event.FloorNumber) < this.propertyAllocation.MinFloorNumber) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.MinAndMaxFloorCountError);
      this.setMaxFloorNumber(undefined);
    }
    if(this.allocatedFloorsWithPercent[event.FloorNumber] == 100) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FloorAllocatedMessage);
      this.setMaxFloorNumber(undefined);
    } else if(this.propertyAllocation.MinFloorNumber && this.propertyAllocation.MaxFloorNumber) {
      let floors = [];
      const min = this.propertyAllocation.MinFloorNumber;
      const max = this.propertyAllocation.MaxFloorNumber;
      for(let i = min; i <= max; i++ ) {
        floors.push(i);
      }
      const allocatedFloors = Object.keys(this.allocatedFloorsWithPercent).filter(key => this.allocatedFloorsWithPercent[key] > 0);
      if(floors.some(floor => allocatedFloors.includes(floor.toString()))) {
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FloorAllocatedMessage);
        this.setMinFloorNumber(undefined);
        this.setMaxFloorNumber(undefined);
      }
    }
  }

  getSelectedValue(Type, event, ValueName) {
    if(Type == AddPropertyAllocationKeys.UseTypeID) {
      this.specificUses = [];
      if (!!this.allSpecificUses && this.allSpecificUses.length > 0) {
        this.allSpecificUses.forEach(item => {
          if (item.UseTypeID == event.UseTypeID) {
            this.specificUses.push(item);
          }
        });
      } else {
        const response_constructtype = this._propertyService.GetSpecificUseByGenUseId(event.UseTypeID);
        response_constructtype.subscribe(result => {
          this.specificUses = result.body.responseData || [];
        });
      }
    }
    if(Type == AddPropertyAllocationKeys.MinFloorNumber) {
      this.minFloorValidations(event);
    }
    if(Type == AddPropertyAllocationKeys.MaxFloorNumber) {
      this.maxFloorValidations(event);
      
    }
    if((Type == AddPropertyAllocationKeys.MaxFloorNumber || Type == AddPropertyAllocationKeys.MinFloorNumber) && this.propertyAllocation.MaxFloorNumber && this.propertyAllocation.MinFloorNumber) {
      this.setFloorCount((this.propertyAllocation.MaxFloorNumber - this.propertyAllocation.MinFloorNumber + 1).toString());
      const currentAllocation = Number(this.propertyAllocation.Floors) * 100 / Number(this.totalFloors) ;
      const allocationAvailable = this.totalSpaceAvailable + this.previousAllocation;
      if (currentAllocation > allocationAvailable) {
        this.setFloorCount(undefined);
        this.setMaxFloorNumber(undefined);
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.AvailableAllocationExceeded)
      }
    }
    let Value = null;
    if (!!event) {
      Value = event[ValueName];
    }
    else {
      Value = null;
    }
    let date = new Date().toISOString();
    var id = this.propertyAllocationCopy[Type];
    let previousData;
    switch (Type) {
      case AddPropertyAllocationKeys.UseTypeID:
        this.propertyTypes.forEach(element => {
          if (element.UseTypeID == id)
            previousData = element.UseTypeName;
        });
        break;

      case AddPropertyAllocationKeys.SpecificUsesID:
        this.specificUses.forEach(element => {
          if (element.SpecificUsesID == id)
            previousData = element.SpecificUsesName;
        });
        break;
      case AddPropertyAllocationKeys.MaxFloorNumber:
        previousData = this.propertyAllocationCopy.MaxFloorNumber;
        Value = event.FloorNumber;
      case AddPropertyAllocationKeys.MinFloorNumber:
        previousData = this.propertyAllocationCopy.MinFloorNumber;
        Value = event.FloorNumber;
      default:
        break;
    }
    let i = this.DataArray.findIndex(x => x.Field == Type);
    if (i != -1) {
      this.DataArray.splice(i, 1);
    }
    this.DataArray.push({
      "Field": Type, "CurrentValue": Value, "PreviousValue": previousData, "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
    });
    if (Type == AddPropertyAllocationKeys.MaxFloorNumber || Type == AddPropertyAllocationKeys.MinFloorNumber) {
      this.DataArray.push({
        "Field": 'Floors', "CurrentValue": this.propertyAllocation.Floors, "PreviousValue": this.propertyAllocationCopy.Floors, "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
      });
    }
  }
  mapChangeLog() {
    let data = this.propertyAllocationForm["controls"];
    let date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty == true) {
        let index = this.DataArray.findIndex(x => x.Field == i);
        if (index == -1) {
          this.DataArray.push({
            "Field": i, "CurrentValue": data[i].value, "PreviousValue": this.propertyAllocationCopy[i], "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
          });
        }
      }
    });
  }
  getValueChange() {
    if(this.propertyAllocation.MaxFloorNumber && this.propertyAllocation.MinFloorNumber) {
      const floorsCount = ((this.propertyAllocation.MaxFloorNumber - this.propertyAllocation.MinFloorNumber) + 1).toString()
      if(this.propertyAllocation.Floors != floorsCount) {
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.IncorrectFloorCount);
        this.setFloorCount(floorsCount);
      }
    }
    if(this.propertyAllocation.MinFloorNumber && !this.propertyAllocation.MaxFloorNumber) {
      const floors  = Number(this.propertyAllocation.Floors);
      const floorAllocated = this.allocatedFloorsWithPercent[this.propertyAllocation.MinFloorNumber];
      if(floorAllocated && floors > Number((1 - floorAllocated).toFixed(2))) {
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.FloorCountExceedingMessage);
        this.setFloorCount((1 - floorAllocated).toFixed(2));
      }
    }
    const currentAllocation = Number(this.propertyAllocation.Floors) * 100 / Number(this.totalFloors) ;
    const allocationAvailable = this.totalSpaceAvailable + this.previousAllocation;
    if (currentAllocation > allocationAvailable) {
      this.setFloorCount(undefined);
      this.setMaxFloorNumber(undefined);
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.AvailableAllocationExceeded);
    }
  }

}
