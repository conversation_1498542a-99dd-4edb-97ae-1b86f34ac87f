<div class="row">
  <div class="col-md-12">
    <form [formGroup]="propertyAllocationForm" (ngSubmit)="addpropertyAllocation()">
      <div class="row">
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Total Allocation Available</div>
            <div class="col-md-7">
              <label><b>{{totalSpaceAvailable | number: '.2-2'}}%</b></label>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Use Type</div>
            <div class="col-md-7">
              <ng-select formControlName="UseTypeID" [items]="propertyTypes" [virtualScroll]="true"
                bindLabel="UseTypeName" bindValue="UseTypeID" labelForId="UseTypeID" placeholder="--Select--"
                [(ngModel)]="propertyAllocation.UseTypeID"
                [ngClass]="{'error-field':(!propertyAllocationForm.controls['UseTypeID'].valid  && propertyAllocationForm.controls['UseTypeID'].touched)}"
                (change)="getSelectedValue('UseTypeID',$event,'UseTypeName')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Specific Use</div>
            <div class="col-md-7">
              <ng-select formControlName="SpecificUsesID" [items]="specificUses" [virtualScroll]="true"
                bindLabel="SpecificUsesName" bindValue="SpecificUsesID" labelForId="SpecificUsesID"
                placeholder="--Select--" [(ngModel)]="propertyAllocation.SpecificUsesID"
                [ngClass]="{'error-field':(!propertyAllocationForm.controls['SpecificUsesID'].valid)}"
                (change)="getSelectedValue('SpecificUsesID',$event,'SpecificUsesName')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Floor Number Min</div>
            <div class="col-md-7">
              <ng-select formControlName="MinFloorNumber" [items]="floorsList" [virtualScroll]="true"
                bindLabel="FloorNumberLabel" bindValue="FloorNumber" labelForId="MinFloorNumber"
                placeholder="--Select--" [(ngModel)]="propertyAllocation.MinFloorNumber"
                [ngClass]="{'error-field':(!propertyAllocationForm.controls['MinFloorNumber'].valid)}"
                (change)="getSelectedValue('MinFloorNumber',$event,'MinFloorNumber')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Floor Number Max</div>
            <div class="col-md-7">
              <ng-select formControlName="MaxFloorNumber" [items]="floorsList" [virtualScroll]="true"
                bindLabel="FloorNumberLabel" bindValue="FloorNumber" labelForId="MaxFloorNumber"
                placeholder="--Select--" [(ngModel)]="propertyAllocation.MaxFloorNumber"
                (change)="getSelectedValue('MaxFloorNumber',$event,'MaxFloorNumber')"></ng-select>
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Floor Count</div>
            <div class="col-md-7">
              <input type="text" integer class="form-control" formControlName="Floors"
                [(ngModel)]="propertyAllocation.Floors"
                [ngClass]="{'error-field':(!propertyAllocationForm.controls['Floors'].valid)}" maxlength="5" (blur)="getValueChange()">
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">
              <span *ngIf="UnitId == metricUnit">Floor Size {{UnitDisplayTextSize}}</span>
              <span *ngIf="UnitId != metricUnit">Floor Size {{UnitDisplayTextSize}}</span>
            </div>
            <div class="col-md-7">
              <input type="text" class="form-control" [attr.disabled]="true"
                formControlName="FloorSizeSM" [(ngModel)]="propertyAllocation.FloorSizeSM"
                >
            </div>
          </div>
        </div>
        <div class="col-md-12" style="margin-top: 5px;">
          <div class="row">
            <div class="col-md-4">Notes</div>
            <div class="col-md-7">
              <textarea rows="4" class="form-control" formControlName="Notes" [(ngModel)]="propertyAllocation.Notes"
                [ngClass]="{'error-field':(!propertyAllocationForm.controls['Notes'].valid)}"></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer-bottom col-md-12">
          <button class="pull-right btn btn-warn" type="submit" ><i class="fa fa-save"></i>
            Save</button>
        </div>
      </div>
    </form>
  </div>

</div>
