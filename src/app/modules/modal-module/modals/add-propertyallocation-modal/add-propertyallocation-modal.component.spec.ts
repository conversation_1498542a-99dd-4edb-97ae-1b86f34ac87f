import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AddPropertyAllocationModalComponent } from './add-propertyallocation-modal.component';

describe('AddPropertyAllocationModalComponent', () => {
  let component: AddPropertyAllocationModalComponent;
  let fixture: ComponentFixture<AddPropertyAllocationModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AddPropertyAllocationModalComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddPropertyAllocationModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
