import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { LatLng } from '../../../map-module/models/LatLng';
import { MapService } from '../../../../modules/map-module/service/map-service.service';
import { MapOptions } from '../../../../modules/map-module/models/MapOptions';
import * as MapEnum from '../../../../modules/map-module/models/MapEnum';
import { ParkingSpace } from '../../../../models/ParkingSpace';
import { environment } from './../../../../../environments/environment';

declare var google: any;

@Component({
  selector: 'app-property-parking-space-modal',
  templateUrl: './property-parking-space-modal.component.html',
  styleUrls: ['./property-parking-space-modal.component.scss']
})
export class PropertyParkingSpaceModalComponent implements OnInit {

  @Output() onClose = new EventEmitter();
  @Input() latLng: { lat: number, lng: number };
  @Output() onParkingSave: EventEmitter<any> = new EventEmitter<any>();
  @Input() parkingSpacePolygon: { singleSlot: ParkingSpace, parkingAreaPolygon: ParkingSpace[], parkingSpaces: number };
  public mapOptions: MapOptions;
  public map: any;
  private _mapService: MapService;
  drawingManager: any;
  singleSlotPolygon: ParkingSpace = { polygonId: null, area: 0, polygon: null };
  parkingAreaPolygons: ParkingSpace[] = [];
  public totalArea: number = 0;
  public singleSlotArea: number = 0;
  public parkingSpaces: number = 0;

  constructor(
    mapService: MapService
  ) {
    this._mapService = mapService;
  }

  ngAfterViewInit() {
  }

  ngOnInit() {
    this.initMap();
  }

  DrawShape(shape) {
    let instance = this;
    this.drawingManager = this._mapService.DrawPolygon(this.map, shape);
    this.drawingManager.setOptions({ drawingControl: true });
    if (shape == "polygon") {
      this._mapService.OnMapOverlayComplete(this.drawingManager, MapEnum.DrawMode.Polygon, (event) => {
        event.setOptions({ editable: true, draggable: true });
        this.drawingManager.setDrawingMode(null);
        let latlngArray = event.latLngs.getArray()[0].getArray();
        var area = google.maps.geometry.spherical.computeArea(latlngArray);
        area = parseFloat(area.toFixed(2))
        this.map.setOptions({ draggable: true });
        const id = !this.singleSlotPolygon.polygonId ? 'singleParkingPoly' : 'parkingArea-' + this.parkingAreaPolygons.length + 1;
        if (!this.singleSlotPolygon.polygonId) {
          this.singleSlotPolygon = { polygonId: id, polygon: event, area: area }
          this.singleSlotArea = area;
        } else {
          this.parkingAreaPolygons.push({ polygonId: id, polygon: event, area: area });
          this.totalArea += area;
        }
        if (this.singleSlotArea && this.totalArea) {
          this.parkingSpaces = Math.floor(this.totalArea / this.singleSlotArea);
        }

        google.maps.event.addListener(event.getPath(), 'set_at', () => {
          instance.preparedPloygonsData(event, id);
        });

        // Add event listener for new vertex insertion
        google.maps.event.addListener(event.getPath(), 'insert_at', () => {
          instance.preparedPloygonsData(event, id);
        });
      });
    }
  }

  preparedPloygonsData(event, id: string) {
    const newArea = google.maps.geometry.spherical.computeArea(event.getPath());
    this.saveArea(id, newArea);
    if (id.includes('singleParkingPoly')) {
      this.singleSlotPolygon.polygon = event;
    } else {
      this.parkingAreaPolygons.map((element) => {
        if (element.polygonId == id) {
          element.polygon = event;
          return element;
        } else {
          return element;
        }
      })
    }
  }

  saveArea(polygonId: string, area: number) {
    area = parseFloat(area.toFixed(2))
    if (polygonId.includes('singleParkingPoly')) {
      this.singleSlotPolygon.area = area;
      this.singleSlotArea = area;
    } else {
      this.parkingAreaPolygons.map((element) => {
        if (element.polygonId == polygonId) {
          this.totalArea -= element.area;
          this.totalArea += area
          element.area = area;
          return element;
        } else {
          return element;
        }
      })
    }
    if (this.singleSlotArea && this.totalArea) {
      this.parkingSpaces = Math.floor(this.totalArea / this.singleSlotArea);
    }
  }

  clearPolygon(polygon: { polygonId: string, area: number, polygon: any }) {
    if (polygon && polygon.polygonId && polygon.polygonId.includes('singleParkingPoly')) {
      this._mapService.ClearPolygon(this.singleSlotPolygon.polygon);
      this.singleSlotPolygon = { polygonId: null, polygon: null, area: 0 };
      this.singleSlotArea = 0;
    } else {
      this._mapService.ClearPolygon(polygon.polygon);
      this.totalArea -= polygon.area;
      this.parkingAreaPolygons = this.parkingAreaPolygons.filter((element) => element.polygonId != polygon.polygonId);
    }
    if (!this.singleSlotArea && !this.totalArea) {
      this.parkingSpaces = 0;
    } else {
      this.parkingSpaces = Math.floor(this.totalArea / this.singleSlotArea);
    }
  }

  onSave() {
    const parkingSpaces = Math.floor(this.totalArea / this.singleSlotArea);
    const latlng = this.singleSlotPolygon.polygon.getPath().getArray().map(element => ({
      Latitude: element.lat(),
      Longitude: element.lng(),
    }));
    const singlePolygon: ParkingSpace = { polygonId: this.singleSlotPolygon.polygonId, area: this.singleSlotPolygon.area, polygon: latlng }
    const parkingPolygons = this.parkingAreaPolygons.map((element) => {
      const latlng = element.polygon.getPath().getArray().map(element => ({
        Latitude: element.lat(),
        Longitude: element.lng(),
      }));
      return {
        polygonId: element.polygonId,
        area: element.area,
        polygon: latlng,
      };
    })
    this.onParkingSave.emit({ parkingSpaces, singleSlot: singlePolygon, parkingArea: parkingPolygons });
    this.onClose.emit(false);
  }

  private initMap() {
    this.mapOptions = new MapOptions('map-property-space');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 20, 7, null, this.latLng.lat, this.latLng.lng);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.FullscreenControl = false;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    this.DrawShape('polygon');
    if (this.parkingSpacePolygon.singleSlot.polygon) {
      this.singleSlotArea = this.parkingSpacePolygon.singleSlot.area;
      const polygon = this._mapService.DrawShapeOnMap(this.map, this.parkingSpacePolygon.singleSlot.polygon, null, false, 'blue', true, (area) => this.saveArea(this.singleSlotPolygon.polygonId, area), (poly) => { })
      this.singleSlotPolygon = { polygonId: this.parkingSpacePolygon.singleSlot.polygonId,  area: this.parkingSpacePolygon.singleSlot.area, polygon: polygon[0] };
    }
    if (this.parkingSpacePolygon.parkingAreaPolygon) {
      this.parkingAreaPolygons = this.parkingSpacePolygon.parkingAreaPolygon;
      this.parkingAreaPolygons = this.parkingAreaPolygons.map((element, i) => {
        this.totalArea += element.area;
        const polygon = this._mapService.DrawShapeOnMap(this.map, element.polygon, null, false, 'blue', true, (area) => this.saveArea(element.polygonId, area), (poly) => { })
        return {
          polygonId: element.polygonId,
          area: element.area,
          polygon: polygon[0],
        };
      })
    }
    if (this.parkingSpacePolygon.parkingSpaces) {
      this.parkingSpaces = this.parkingSpacePolygon.parkingSpaces;
    }
  }

  addControllers() {
    this._mapService.AddController(this.map, "clearShapesInBM", MapEnum.GoogleMapControlPosition.Top_Left);
  }

  closeParkingSpace() {
    this.onClose.emit();
  }

}
