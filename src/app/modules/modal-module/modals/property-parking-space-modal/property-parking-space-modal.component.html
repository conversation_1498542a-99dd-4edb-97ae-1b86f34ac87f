<div class="row">
  <div class="col-sm-8">
    <div class="parkingmap" [style.display]="'block'"
      [style.height]="'60vh'" [style.width]="''">
      <div class="searchBarBox">
        <label *ngIf="singleSlotPolygon.area === 0">Draw a polygon for a single slot </label>
        <label *ngIf="singleSlotPolygon.area !== 0 && parkingAreaPolygons.length === 0">Draw a polygon for parking area
        </label>
        <!-- <a (click)="clearShapes()"><img src="assets/images/Close.png"></a> -->
      </div>
      <div id="map-property-space" class="map"></div>
    </div>
  </div>
  <div class="col-sm-4 no-padding" [style.height]="'60vh'" [style.overflow]="'scroll'">
    <div class="form-group row">
      <label class="col-md-9 form-control-label" for="text-input">Single Slot Parking Area (SqM)
      </label>
      <div class="col-md-9 position-relative">
        <i class="fa fa-close icon" (click)="clearPolygon(singleSlotPolygon)"></i>
        <input type="text" disabled="true" [(ngModel)]="singleSlotPolygon.area" class="form-control">
      </div>
    </div>
    <div class="form-group row" *ngFor="let parking of parkingAreaPolygons; let i = index">
      <label class="col-md-9 form-control-label" for="text-input">Parking Area {{i + 1}} (SqM)
      </label>
      <div class="col-md-9 position-relative">
        <i class="fa fa-close icon" (click)="clearPolygon(parking)"></i>
        <input type="text" disabled="true" [(ngModel)]="parking.area" class="form-control">
      </div>
    </div>
    <div class="form-group row" *ngIf="parkingSpaces !== 0">
      <label class="col-md-9 form-control-label" for="text-input">Parking Spaces
      </label>
      <div class="col-md-9 position-relative">
        <input type="text" disabled="true" [(ngModel)]="parkingSpaces" class="form-control">
      </div>
    </div>
    <input *ngIf="singleSlotPolygon.area !== 0 && parkingAreaPolygons.length !== 0" type="button"
      class="btn btn-primary" value="Save" (click)="onSave()" />
  </div>
  <!-- Map Switch component -->
  <app-map-switch id="map-switch-measurement" [map]="map" (onAddMapSwitchBtnControler)="addControllers()" *ngIf="map"></app-map-switch>
</div>
