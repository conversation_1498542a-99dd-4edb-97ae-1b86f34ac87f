.file-row .form-align {
    width: 100%;
}

.progress {
    background: none;
    border: 1px #ccc solid;
}

.progress-bar {
    height: inherit;
}

.size{
    margin-left: 10px;
}


.error-field{
    border: 1px solid #ff0066;
}
ul.size-info {
    display: flex;
    padding: 0;
    margin-bottom: 0;
    text-align: center;
}

ul.size-info li {
    flex: 0 0 35%;
    list-style: none;
    position: relative;
    padding-left: 35px;
}

ul.size-info li i {
    position: absolute;
    left: 30px;
    font-size: 20px;
    color: #f18a00;
}

ul.size-info li h3 {
    color: #191d64;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 0;
}

ul.size-info li {
    margin: 0;
    font-size: 12px;
}
.btn-success i, .btn-primary i{
    color: #fff !important;
}
.succesUploadIcon{
    position: absolute;
    font-size: 1.2rem;
    top: -15px;
    right: -5px;
    color: rgb(29, 172, 0);
}
#inputFiles {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    font-size: 23px;
    direction: ltr;
    cursor: pointer;
    left: 0;
    width: 100%;
}
.mandatory {
    color: red;
    font-weight: bold;
}
.labelText {
    float: left;
    font-size: 12px;
    color: rgb(150, 150, 150);
    max-width: 98%;
    overflow: hidden;
}

.deleteRow {
    float: right;
    color: rgb(150, 150, 150);
    font-size: 12px;
}

.statusRow {
    float: right;
    color: rgb(29, 172, 0);
    font-size: 12px;
}

.dottedline {
    border-bottom: 1px dotted #ccc;
}