import { Component, OnInit, Input, OnDestroy, NgZone, Output, EventEmitter } from '@angular/core';
import { FileObject, ContainerEvents, FileObjectStatus, S3Config } from '../../../../modules/aws/types/types';
import { S3 } from 'aws-sdk';
import { MediaService } from '../../../../services/media.service'
import { mapEditPropertyDTO } from '../../../../DTO/mapEditPropertyDTO';
import { CommunicationService, CommunicationModel } from '../../../../services/communication.service';
import { Subject, Subscription } from 'rxjs';
import { MediaType, MediaSubType, Media, MediaTypeEnum, MediaSource, MediaSubTypeEnum,MediaRelationTypeEnum, MediaSourceTypeEnum } from '../../../../models/MediaType';
import { FormGroup, FormControl, FormArray, NgForm, Validators, FormBuilder } from '@angular/forms';
import { DecimalPipe } from '@angular/common';
import { LoginService } from '../../../../services/login.service';
import { SharedDataService } from '../../../../services/shareddata.service';
import { environment } from '../../../../../environments/environment';
import { FileTypes } from '../../../aws/types/types';
import { NotificationService } from '../../../notification/service/notification.service';
import {ContactMedia} from '../../../../models/ContactMedia';
@Component({
  selector: 'app-file-upload-modal',
  templateUrl: './file-upload-modal.component.html',
  styleUrls: ['./file-upload-modal.component.scss']
})
export class FileUploadModalComponent implements OnInit {

  @Input() media: Media;
  @Input() oddRow: boolean;
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() IsAgentAdd = false;
  @Input() IsBranchAdd = false;
  @Input() IsCompanyAdd = false;
  @Output() onUploadEvent = new EventEmitter();
  @Output() onSourceChange = new EventEmitter<boolean>();
  @Output() onSave = new EventEmitter<string>();
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  FileObjectStatus = FileObjectStatus;
  progress = 0;
  speed = 0;
  uploadError: string;
  containerEventSubscription: Subscription;
  uploadHandle: any;
  isDefault: any = 0;
  mediaTypes: Array<MediaType>;
  mediaSubTypes: Array<MediaSubType>;
  mediaForm: FormGroup;
  fileArray: Array<any> = [];
  files: Media[] = [];
  ImageFiles = [];
  mediaSource: Array<MediaSource>;
  fileObject: FileObject;
  fileSize: string;
  isEdit: boolean = false;
  mediaWEBsource = false;
  showLoader: boolean = false;
  DataArray: Array<any> = [];
  public mediaCopy: Media = new Media();

  private updatePropertyFormListner: Subscription;
  IsAttachment = false;
  fileObjectAttach: FileObject[] = [];
  count = 0;
  attachfileSize: string;
  initialAttachmentDetails: ContactMedia;

  constructor(private mediaService: MediaService,
    private _loginService: LoginService,
    private communicationService: CommunicationService,
    private _notificationService: NotificationService,
    private _sharedDataService: SharedDataService ) {   

  }

  ngOnInit() {

    this.createForm();
    this.media.ModifiedBy = this._loginService.UserInfo.EntityID;
    this.media.RelationshipTypeID = 1;
    this.media.RelationID = this.initialDetails.propertyId;
    this.media.PropertyID = this.initialDetails.propertyId;
    if (this.media.MediaID > 0) {
      this.isEdit = true;
      this.getAttachmentMedia();
      //this.media.URL = constants.MediaS3Base + constants.MediaS3Path + "/" + this.media.Path;
      this.fileSize = this.formatBytes(this.media.Size);
      this.mediaCopy = new Media();
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    }
    else {
      this.fileObject = new FileObject(this.media.File);
      this.media.Ext = this.media.File.name.split('.').pop();
      this.media.MediaName = this.media.File.name.split('.')[0];
      this.media.Size = this.media.File.size;
      this.fileSize = this.formatBytes(this.fileObject.file.size);
      this.media.MediaTypeID = MediaTypeEnum.AerialImagery;
      // this.media.MediaSubTypeID = 0;
      this.media.CreatedBy = this._loginService.UserInfo.EntityID;
      this.media.IsDefault = 1;
      this.media.MediaID = 0;
    }

    if (!!this._sharedDataService.mediaTypes && this._sharedDataService.mediaTypes.length > 0) {
      this.mediaTypes = this._sharedDataService.mediaTypes;
    } else {
      const mediaTypeResponse = this.mediaService.GetMediaTypes();
      mediaTypeResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaTypes = result.body.responseData;
          this._sharedDataService.mediaTypes = this.mediaTypes;
        }
      });
    }
    if (!!this._sharedDataService.mediaSubTypes && this._sharedDataService.mediaSubTypes.length > 0) {
      this.mediaSubTypes = this._sharedDataService.mediaSubTypes;
    } else {
      const mediaSubTypeResponse = this.mediaService.GetMediaSubTypes();
      mediaSubTypeResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaSubTypes = result.body.responseData;
          this._sharedDataService.mediaSubTypes = this.mediaSubTypes;

        }
      });
    }
    if (!!this._sharedDataService.mediaSource && this._sharedDataService.mediaSource.length > 0) {
      this.mediaSource = this._sharedDataService.mediaSource;
    } else {
      const mediaSourceResponse = this.mediaService.GetMediaSource();
      mediaSourceResponse.subscribe(result => {
        if (!result.body.error) {
          this.mediaSource = result.body.responseData;
          this._sharedDataService.mediaSource = this.mediaSource;
        }
      });
    }
  }



  private handleContainerEvent(containerEvent: ContainerEvents) {
    if (containerEvent === ContainerEvents.Upload) {
      return this.fileObject.status === FileObjectStatus.NotStarted && this.upload();
    } else if (containerEvent === ContainerEvents.Cancel) {
      return this.fileObject.status === FileObjectStatus.Uploading && this.cancel();
    } else if (containerEvent === ContainerEvents.Delete) {
      return this.clear();
    }
  }

  upload() {   
    this.showLoader = true;
    if (this.mediaForm.valid) {
      if (this.media.IsOwnMedia === 0 && !this.files.length) {
        this.showLoader = false;
        this._notificationService.ShowErrorMessage('Please upload source materials');
        return;
      }
      console.log('uploading', this.fileObject.file.name);
      this.fileObject.status = FileObjectStatus.Uploading;
      this.uploadError = undefined;
      this.progress = 0;
      const uploadHandle = this.mediaService.uploadToS3(this.fileObject.fileName, this.media.URL,false);
      uploadHandle.subscribe(result => {
        if (!result.body.error) {
          this.media.Path = this.fileObject.fileName;
          this.saveMedia();
        } else {
          this.showLoader = false;
        }
      });
      //this.uploadHandle = this.s3Service.upload(this.fileObject, this.handleS3UploadProgress());
    }
    //  this.showLoader=false;
  }

  save() {
    this.showLoader = true;
    this.saveMedia();
  }

  private handleS3UploadProgress() {
     return (error: Error, progress: number, speed: number) => {
      if (error) {
        this.showLoader = false;
        this.progress = 0;
        this.speed = 0;
        this.uploadError = error.message;
        this.fileObject.status = FileObjectStatus.Failed;
      } else {
        this.progress = progress || this.progress;
        this.speed = speed || this.speed;
        if (this.progress === 100) {
          if (this.fileObject.status == FileObjectStatus.Uploaded) {
            this.media.Path = this.fileObject.fileName;
            this.saveMedia();
          }
          this.fileObject.status = FileObjectStatus.Uploaded;
        }
      }
    };
  }

  cancel() {  
    if (this.fileObject && this.fileObject.status === FileObjectStatus.Uploading) {
      console.log('cancelling', this.fileObject.file.name);
      this.fileObject.status = FileObjectStatus.Canceled;
      //this.s3Service.cancel(this.uploadHandle);
    }

  }

  close() {
    this.onClose.emit();
  }

  clear() {
    if (this.fileObject && this.fileObject.status !== FileObjectStatus.Uploading) {
      console.log('clearing', this.fileObject.file.name);
      this.fileObject.status = FileObjectStatus.Deleted;
      //this.s3Service.publishFileUploadEvent(this.fileObject);
    }
  }

  ngOnDestroy() {
    // prevent memory leak when component destroyed
    if(!!this.containerEventSubscription)
      this.containerEventSubscription.unsubscribe();
    this.clear();
  }

  createForm() {
    this.mediaForm = new FormGroup({
      'MediaName': new FormControl('', Validators.required),
      'MediaType': new FormControl(''),
      'MediaSubType': new FormControl(''),
      'Description': new FormControl(''),
      'IsDefault': new FormControl(''),
      'IsOwnMedia': new FormControl(''),
      'MediaSourceID': new FormControl(''),
      'SourceComments': new FormControl('')
    });
    if (!this.IsAgentAdd && !this.IsBranchAdd && !this.IsCompanyAdd) {
      if (this.media.MediaID > 0) {
        this.mediaForm.controls['IsOwnMedia'].disable();
        this.mediaForm.controls['MediaSourceID'].disable();
        this.mediaForm.controls['SourceComments'].disable();
        this.mediaForm.controls['IsOwnMedia'].markAsUntouched();
        this.mediaForm.controls['MediaSourceID'].markAsUntouched();
        this.mediaForm.controls['SourceComments'].markAsUntouched();
      } else {
        this.mediaForm.controls['IsOwnMedia'].enable();
        this.mediaForm.controls['MediaSourceID'].enable();
        this.mediaForm.controls['SourceComments'].enable();
        this.mediaForm.controls['IsOwnMedia'].markAsTouched();
        this.mediaForm.controls['MediaSourceID'].markAsTouched();
        this.mediaForm.controls['SourceComments'].markAsTouched();
        this.mediaForm.get('IsOwnMedia').setValidators([Validators.required]);
        this.mediaForm.get('IsOwnMedia').valueChanges.subscribe(
          (IsOwnMedia) => {
            if (IsOwnMedia === 0) {
              this.mediaForm.get('MediaSourceID').setValidators([Validators.required]);
            } else {
              this.mediaForm.get('MediaSourceID').clearValidators();
            }
            this.mediaForm.get('MediaSourceID').updateValueAndValidity();
          });
        this.mediaForm.get('MediaSourceID').valueChanges.subscribe(
          (MediaSourceID) => {
            if (MediaSourceID === MediaSourceTypeEnum.OwnerWebsite || MediaSourceID === MediaSourceTypeEnum.BuildingWebsite ||
              MediaSourceID === MediaSourceTypeEnum.BrokerWebsite || MediaSourceID === MediaSourceTypeEnum.ThridPartyWebsite) {
              this.mediaWEBsource = true;
              this.mediaForm.get('SourceComments').setValidators([Validators.required]);
            } else {
              this.mediaWEBsource = false;
              this.mediaForm.get('SourceComments').clearValidators();
            }
            this.mediaForm.get('SourceComments').updateValueAndValidity();
          });
      }
    }
  }

  saveMedia() {
    if (this.mediaForm.valid) {
    this.mapChangeLog();
    if (this.media.IsOwnMedia != null) {
      this.media.IsOwnMedia = this.media.IsOwnMedia.toString();
    }
    if (this.IsAgentAdd) {
      this.media.IsDefault = 1;
    }
    this.media.ChangeLogJSON = JSON.stringify(this.DataArray);
    this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    const medias = this.mediaService.SaveMediaToDatabase(this.media);
    medias.subscribe(result => {
      this.clear();
      const dataResult = result.body.responseData[0];
      this.showLoader = false;
      if(this.fileObject){
      this.fileObject.mediaId = 0;
      }
      if (this.media.IsOwnMedia === '0') {
        if (this.files.length) {
          this.files.forEach((x, index) => {
            this.uploadAttachmentMedia(dataResult[0].MediaID, index);
          })
        }
      }
      if (this.fileObject) {
        this.fileObject.mediaId = 0;
      }
      this._notificationService.ShowSuccessMessage('Media saved successfully');

      this.onUploadEvent.emit("true");
      
    });
  } else {
    this.showLoader = false;
  }
 }
 uploadAttachmentMedia(mediaId: number, i: number) {
  this.showLoader = true;
  this.IsAttachment = true;
  this.files[i].ModifiedBy = this._loginService.UserInfo.EntityID;
  this.files[i].RelationshipTypeID = MediaRelationTypeEnum.Media;
  this.files[i].RelationID = mediaId;
  this.files[i].PropertyID = this.initialDetails.propertyId;
  this.mediaAttachment(i);
  const uploadHandleAttach = this.mediaService.uploadToS3(this.fileObjectAttach[i].fileName, this.files[i].UploadPathURL);
  uploadHandleAttach.subscribe(result => {
    if (!result.body.error) {
      this.fileArray[i].IsUploaded = true;
      this.count++;
      this.saveAttachmentMedia(i);
    }
  });
}

mediaAttachment(i) {
  this.fileObjectAttach[i] = new FileObject(this.files[i].File);
  this.files[i].Ext = this.files[i].File.name.split('.').pop();
  this.files[i].MediaName = this.files[i].File.name.split('.')[0];
  this.files[i].Size = this.files[i].File.size;
  this.attachfileSize = this.formatBytes(this.fileObjectAttach[i].file.size);
  this.files[i].MediaTypeID = MediaTypeEnum.OtherMedia;
  this.files[i].MediaSubTypeID = 0;
  this.files[i].CreatedBy = this._loginService.UserInfo.EntityID;
  this.files[i].IsDefault = 0;
  this.files[i].MediaID = 0;
  this.fileExtentions(this.files[i])
}
saveAttachmentMedia(i: number) {
  this.files[i].Path = this.fileObjectAttach[i].fileName;
  const medias = this.mediaService.SaveMediaToDatabase(this.files[i]);
  medias.subscribe(result => {
    if (this.fileObjectAttach[i]) {
      this.fileObjectAttach[i].mediaId = 0;
    }
    if (this.files.length === this.count) {
      this.showLoader = false;
      this.onSave.emit(this.media.Path);
    }
  });
}
getAttachmentMedia() {
  this.fileArray = [];
  this.initialAttachmentDetails = new ContactMedia();
  this.initialAttachmentDetails.RelationID = this.media.MediaID;
  this.initialAttachmentDetails.RelationshipTypeID = MediaRelationTypeEnum.Media;
  this.initialAttachmentDetails.PropertyID = this.media.PropertyID;
  if (this.initialAttachmentDetails) {
    const medias = this.mediaService.getAllMediaRelation(this.initialAttachmentDetails);
    medias.subscribe(result => {
      if (!result.body.error) {
        this.fileArray = result.body.responseData[0];
        this.fileArray.forEach(x => {
          x.Size = this.formatBytes(x.Size);
          x.OrginalURL = environment.MediaS3Base + environment.MediaS3Path + '/' + x.Path;
        });
        this.fileArray = this.fileArray.sort((a, b) => b.CreatedDate.localeCompare(a.CreatedDate));
      }
    });
  }
}

deleteAddedFiles(i: number, dynamicMedia = null, dynamicIndex = null) {
  if (!!dynamicMedia) {
    this.ImageFiles[dynamicIndex].fileArray.splice(i, 1);
    this.ImageFiles[dynamicIndex].uploadingFileArray.splice(i, 1);
  } else {
    this.fileArray.splice(i, 1);
    this.files.splice(i, 1);
  }
}

 mediaSourceChange(event: number) {
  if (event === 1) {
    this.onSourceChange.emit(false);
  } else {
    this.onSourceChange.emit(true);
  }
}


    // .................... Change Log ........................

    getSelectedValue(Type, Value, MultipleMedia = null, index = null) {
      const date = new Date().toISOString();
      let id = null;
  
       id = this.mediaCopy[Type];
      let previousData;
      switch (Type) {
        case 'MediaName':
        case 'MediaTypeID':
          this.mediaTypes.forEach(element => {
            if (element.MediaTypeID === id) {
              previousData = element.MediaTypeName;
            }
          });
          break;
        case 'MediaSubTypeID':
          this.mediaSubTypes.forEach(element => {
            if (element.MediaSubTypeID === id) {
              previousData = element.MediaSubTypeName;
            }
          });
          break;
        case 'Description':
        case 'IsDefault':
          Value === true ? previousData = false : previousData = true;
          break;
        case 'IsOwnMedia':
          this._sharedDataService.yesNoList.forEach(element => {
            if (element.ID === id) {
              previousData = element.Item;
            }
          });
          break;
         case 'MediaSourceID':
          this.mediaSource.forEach(element => {
            if (element.MediaSourceID === id) {
              previousData = element.MediaSourceName;
             }
           });
           break;
        case 'SourceComments':
        default:
          break;
      }
  
      if (!MultipleMedia) {
        const i = this.DataArray.findIndex(x => x.Field === Type);
        if (i !== -1) {
          this.DataArray.splice(i, 1);
        }
        this.DataArray.push({
          'Field': Type, 'CurrentValue': Value, 'PreviousValue': previousData,
          'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
        });
      }
    }
  
  mapChangeLog(media = null) {
    const data = this.mediaForm['controls'];
    const date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty === true) {
        const index = this.DataArray.findIndex(x => x.Field === i);
        if (index === -1) {
          this.DataArray.push({
            'Field': i, 'CurrentValue': data[i].value, 'PreviousValue': this.mediaCopy[i],
            'LoginEntityID': this._loginService.UserInfo.EntityID, 'DateTime': date
          });
        }
      }
    });
  }
  fileUploadEvent(fileInput: any, dynamicMedia = null, index = null) {
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const fileObject = new FileObject(fileInput.target.files[i]);
        const media = new Media();
        media.File = fileInput.target.files[i];
        media.Ext = media.File.name.split('.').pop();
        if (this.mediaService.mediaCheck(media) === false) {
          this._notificationService.ShowErrorMessage('File not Supported');
          return;
        }
        if (fileInput.target.files && fileInput.target.files[i]) {
          const reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            media.UploadPathURL = event.target.result;
            const img = new Image;
            img.onload = function () {
              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
            this.fileExtentions(media)
          }
          reader.readAsDataURL(fileInput.target.files[i]);
        }
        if (!!dynamicMedia) {
          this.ImageFiles[index].uploadingFileArray.push(media);
        } else {
          this.files.push(media);
        }
      }
    }
    fileInput.target.value = null;
    if (!!dynamicMedia) {
      this.ImageFiles[index].fileArray = [];
      this.ImageFiles[index].uploadingFileArray.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.ImageFiles[index].fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    } else {
      this.fileArray = [];
      this.files.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    }
  }
  fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PNG || media.Ext.toLowerCase() === FileTypes.JPG) {
      media.URL = environment.MediaS3Base + environment.MediaS3Path + environment.MediaS3ThumbnailPath +
        '/' + environment.MediaS3ThumbResolution + '/' + media.Path;
    } else {
      this.mediaService.fileExtentions(media);
    }
  }

  // saveMedia2() {
  //   if (this.media.IsOwnMedia != null) {
  //     this.mapChangeLog();
  //     this.media.IsOwnMedia = this.media.IsOwnMedia.toString();
  //   }
  //   if (this.IsAgentAdd) { this.media.IsDefault = true; }
  //   this.media.ChangeLogJSON = JSON.stringify(this.DataArray);
  //   this.media.ApplicationID = EnumApplication.ECRE;
  //   const medias = this._mediaService.SaveMediaToDatabase(this.media);
  //   medias.subscribe(result => {
  //     if (this.media.IsDefault) {
  //       this.SetDefault.emit(this.media.Path);
  //     }
  //     if (this.fileObject) {
  //       this.fileObject.mediaId = 0;
  //     }
  //     this.showLoader = false;
  //     this.Save.emit(this.media.Path);
  //     this.visibleChange.emit(false);
  //   });
  // }



  formatBytes(bytes) {
    if (bytes < 1024) return bytes + " Bytes";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(3) + " KB";
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(3) + " MB";
    else return (bytes / 1073741824).toFixed(3) + " GB";
  };

}
