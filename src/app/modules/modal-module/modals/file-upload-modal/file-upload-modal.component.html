<div class="col-md-12">
    <div [ngClass]="{'file-row': true, row: true, odd: oddRow}">
        <form [formGroup]="mediaForm" class="form-align">
        <!-- <div class="col-md-12">
            <div class="row">
                <h2>Upload Media</h2>
            </div>
        </div> -->

        <!--start-->
        <div class="row">

            <div class="col-md-6">
                <div class="row">
                    
                        <div class="col-md-12 data-wrapper">
                            <label>File Name</label>
                            <!-- <p class="name">{{fileObject.file.name}}</p> -->
                            <input type="text" class="form-control" 
                            formControlName="MediaName" [(ngModel)]="media.MediaName" 
                            [ngClass]="{'error-field':(!mediaForm.controls['MediaName'].valid)}">

                        </div>
                        <!--  <div class="col-lg-12">
                            <p [ngClass]="{'text-primary': !fileObject && fileObject.status === FileObjectStatus.Uploading, 'text-success': !fileObject && fileObject.status === FileObjectStatus.Uploaded, 'text-danger': !fileObject && fileObject.status === FileObjectStatus.Failed}">
                                <strong *ngIf="!fileObject && fileObject.status !== FileObjectStatus.NotStarted" [ngClass]="{'error': !fileObject && fileObject.status === FileObjectStatus.Failed}">
                                    {{FileObjectStatus[fileObject.status]}}
                                </strong>
                            </p>
                            <strong class="error text-danger">{{uploadError}}</strong>
                        </div> -->
                        <div class="col-lg-12 mb-3">
                            <label>Media Type</label>
<!--                             <select class="form-control" formControlName="MediaType" class="form-control" [(ngModel)]="media.MediaTypeID" [ngClass]="{'error-field':(!mediaForm.controls['MediaType'].valid)}">
                                <option value=0>--Select--</option>
                                <option *ngFor="let medias of mediaTypes" value="{{medias.MediaTypeID}}">{{medias.MediaTypeName}}</option>
                            </select> -->
                            <ng-select formControlName="MediaType" [items]="mediaTypes"
                            [virtualScroll]="true"
                            bindLabel="MediaTypeName"
                            bindValue="MediaTypeID"
                             placeholder="--Select--"
                             [(ngModel)]="media.MediaTypeID" 
                             [ngClass]="{'error-field':(!mediaForm.controls['MediaType'].valid)}"
                             (change)="getSelectedValue('MediaTypeID',$event ?$event.MediaTypeName:'')">
                    </ng-select>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label> Media SubType</label>
<!--                             <select class="form-control" formControlName="MediaSubType" [(ngModel)]="media.MediaSubTypeID">
                                <option value=0>--Select--</option>
                                <option *ngFor="let medias of mediaSubTypes" value="{{medias.MediaSubTypeID}}">{{medias.MediaSubTypeName}}</option>
                            </select> -->
                            <ng-select formControlName="MediaSubType" [items]="mediaSubTypes"
                            [virtualScroll]="true"
                            bindLabel="MediaSubTypeName"
                            bindValue="MediaSubTypeID"
                             placeholder="--Select--"
                             [(ngModel)]="media.MediaSubTypeID"
                             (change)="getSelectedValue('MediaSubTypeID',$event ?$event.MediaSubTypeName:'')"
                  ></ng-select>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label>Media Description</label>
                            <textarea rows="1" class="form-control" formControlName="Description" [(ngModel)]="media.Description"></textarea>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label class="set-as"><input type="checkbox" formControlName="IsDefault"
                                    name="media.IsDefault" [(ngModel)]="media.IsDefault"
                                    (change)="getSelectedValue('setAsDefault',$event ?$event.target.checked:'')" />
                                Set as Default <img src="assets/images/favourte-icon.png" width="20"></label>

                        </div>
                    
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-lg-12 mb-3">
                        <img [src]="media.URL" width="360" height="260">
                    </div>
                    
                    <div class="col-lg-12">
                        <ul class="size-info">
                            <li>
                                <i class="fa fa-file"></i>
                                <h3>{{fileSize}}</h3>
                                <p>size</p>
                            </li>
                            <li>
                                <i class="fa fa-arrows-v"></i>
                                <h3>{{media.Height}}</h3>
                                <p>Height</p>
                            </li>
                            <li>
                                <i class="fa fa-exchange"></i>
                                <h3>{{media.Width}}</h3>
                                <p>Width</p>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-12 text-right mb-3">
                        <!-- <div *ngIf="!fileObject" class="progress">
                            <div [ngClass]="{'progress-bar': true, 'progress-bar-striped': true, 'progress-bar-success': !fileObject && fileObject.status !== FileObjectStatus.Failed, 'progress-bar-danger': !fileObject && fileObject.status === FileObjectStatus.Failed }"
                                role="progressbar" [ngStyle]="{'width': progress + '%'}" aria-valuenow="progress" aria-valuemin="0"
                                aria-valuemax="100">
                                <strong *ngIf="progress > 0">{{progress}}%</strong>

                            </div>

                        </div> -->
                        <!-- <span *ngIf="!fileObject && fileObject.status === FileObjectStatus.Uploading" class="speed">{{speed | fileSize}}/s</span> -->

                        <!-- <span class="size">{{fileObject.file.size | fileSize}}</span> -->
                    </div>
                </div>
            </div>
        </div>
        <!--end-->

   
                    
                    <div class="col-md-12 no-padding" *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                        <div class="row">
                            <div class="col-md-6">
                                <label>Did you take this photo ? <span class="mandatory">*</span></label>
                                <ng-select formControlName="IsOwnMedia" [items]="_sharedDataService.yesNoList"
                                    labelForId="IsOwnMedia" [virtualScroll]="true" bindLabel="Item" bindValue="ID"
                                    placeholder="--Select--" [(ngModel)]="media.IsOwnMedia"
                                    (change)="mediaSourceChange(media.IsOwnMedia)"
                                    [ngClass]="{'error-field':(!mediaForm.controls['IsOwnMedia'].valid && mediaForm.controls['IsOwnMedia'].touched)}"
                                    (change)="getSelectedValue('ID',$event ?$event.Item:'')">
                                </ng-select>
                            </div>
                            <div class="col-md-6" *ngIf="media.IsOwnMedia == 0">
                                <label>Media Source <span class="mandatory">*</span></label>
                                <ng-select formControlName="MediaSourceID" [items]="mediaSource" [virtualScroll]="true"
                                    labelForId="MediaSourceID" bindLabel="MediaSourceName" bindValue="MediaSourceID"
                                    placeholder="--Select--" [(ngModel)]="media.MediaSourceID"
                                    [ngClass]="{'error-field':(!mediaForm.controls['MediaSourceID'].valid && mediaForm.controls['MediaSourceID'].touched)}"
                                    (change)="getSelectedValue('MediaSourceID',$event ?$event.MediaSourceName:'')">
                                </ng-select>
                            </div>
                        </div>
                    </div>
                    
                    <ng-container *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                        
                        <div class="col-md-12 mt-3 no-padding" *ngIf="media.IsOwnMedia == 0">
                            <div class="row">
                                <div class="col-md-12">
                                    <label>Source Comments<span *ngIf="mediaWEBsource" class="mandatory">*</span></label>
                                    <textarea rows="3" formControlName="SourceComments" [(ngModel)]="media.SourceComments"
                                        class="form-control" maxlength="1000"
                                        [ngClass]="{'error-field':(!mediaForm.controls['SourceComments'].valid) && mediaForm.controls['SourceComments'].touched}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mt-3 no-padding" *ngIf="media.IsOwnMedia == 0">
                            <div class="row">
                                <div class="col-md-12">
                                    <label style="color:#20a8d8;" *ngIf="media.MediaID > 0 && fileArray.length">
                                        Source Materials
                                    </label>
                                    <label for="inputFiles" class="file-input-btn" *ngIf="!media.MediaID">
                                        <span style="color:#20a8d8;">
                                            <i class="fa fa-paperclip" aria-hidden="true"></i>
                                            <span>Attach confirmation source materials <span class="mandatory">*</span></span>
                                            <input id="inputFiles" name="files[]" multiple="" type="file" accept="image/*"
                                                (change)="fileUploadEvent($event)">
                                        </span>
                                    </label>
                                </div>
                                <div class="col-md-6">
                                    <div class="row m-0">
                                        <ng-container *ngFor="let file of fileArray; let i = index;">
                                            <div class="col-md-12 dottedline pr-0 pl-0">
                                                <span class="labelText" *ngIf="!media.MediaID">{{file.MediaName}}</span>
        
                                                <span class="deleteRow" *ngIf="!media.MediaID && !file.IsUploaded"
                                                    (click)="deleteAddedFiles(i)">
                                                    <i class="fa fa-times"></i>
                                                </span>
                                                <span class="statusRow" *ngIf="!media.MediaID && file.IsUploaded">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                <span class="deleteRow mr-2">
                                                    {{file.Size}}
                                                </span>
                                                <a class="labelText" *ngIf="media.MediaID > 0" target="_blank" download
                                                    href="{{file.OrginalURL}}">
                                                    {{file.MediaName}}.{{file.Ext}}
                                                </a>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                        </div>
                   
                    </ng-container>
                </form>
                    <div class='col-md-12'>
                        <div class='row'>
                            <div class='col-md-12'>
                    <div *ngIf="!isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <!--  [disabled]="!fileObject && fileObject.status === FileObjectStatus.Uploading"  -->
                        <button class="btn btn-primary start" (click)="upload()"[disabled]="!mediaForm.valid">
                            <i class="fa fa-upload"></i>
                            <strong>Upload</strong>
                        </button>
                        <!-- [disabled]="!fileObject && fileObject.status !== FileObjectStatus.Uploading"  -->
                        <button class="btn btn-success cancel" (click)="close()">
                            <i class="fa fa-cancel"></i>
                            <strong>Cancel</strong>
                        </button>
                        <!-- <button [disabled]="fileObject.status === FileObjectStatus.Uploading" class="btn btn-danger delete" (click)="clear()">
                                <i class="glyphicon glyphicon-trash"></i>
                                <strong>Clear</strong>
                        </button> -->
                    </div>
                    <div *ngIf="isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <!--  [disabled]="!fileObject && fileObject.status === FileObjectStatus.Uploading"  -->
                        <button class="btn btn-primary start" (click)="save()">
                             <i class="fa fa-upload"></i>
                             <strong>Save</strong>
                         </button>
                        <button class="btn btn-success cancel" (click)="close()">
                            <i class="fa fa-cancel"></i>
                            <strong>Cancel</strong>
                        </button>
                        <!-- [disabled]="!fileObject && fileObject.status !== FileObjectStatus.Uploading"  -->
                        <!--   <button class="btn btn-success cancel" (click)="cancel()">
                             <i class="fa fa-cancel"></i>
                             <strong>Cancel</strong>
                         </button> -->
                        <!-- <button [disabled]="fileObject.status === FileObjectStatus.Uploading" class="btn btn-danger delete" (click)="clear()">
                                 <i class="glyphicon glyphicon-trash"></i>
                                 <strong>Clear</strong>
                         </button> -->
                    </div>
                    </div>
                </div>
            </div>
                
            
        
    </div>
</div>