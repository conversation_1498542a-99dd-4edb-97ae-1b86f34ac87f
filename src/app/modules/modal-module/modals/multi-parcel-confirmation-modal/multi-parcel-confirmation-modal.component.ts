import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-multi-parcel-confirmation-modal',
  templateUrl: './multi-parcel-confirmation-modal.component.html',
  styleUrls: ['./multi-parcel-confirmation-modal.component.css']
})
export class MultiParcelConfirmationModalComponent implements OnInit {
  @Input() parcels = [];
  @Output() onSave: EventEmitter<any> = new EventEmitter<any>();
  @Output() onCancel: EventEmitter<any> = new EventEmitter<any>();
  parcelNos = '';

  constructor() { }

  ngOnInit() {
    const parcelNos = this.parcels.map(parcel => parcel.Parcel_No ? parcel.Parcel_No : parcel.ParcelNo);
    this.parcelNos = parcelNos.join(',');
  }

  onYes() {
    this.onSave.emit();
  }

  onNo() {
    this.onCancel.emit();
  }

}
