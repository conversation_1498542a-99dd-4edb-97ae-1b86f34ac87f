import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MultiParcelConfirmationModalComponent } from './multi-parcel-confirmation-modal.component';

describe('MultiParcelConfirmationModalComponent', () => {
  let component: MultiParcelConfirmationModalComponent;
  let fixture: ComponentFixture<MultiParcelConfirmationModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ MultiParcelConfirmationModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MultiParcelConfirmationModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
