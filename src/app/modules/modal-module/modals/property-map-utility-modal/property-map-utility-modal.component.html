<div class="propertymap">
  <div id="PropertyUtilityMap" class="map"></div>
  <div id="layersDropdown" class="layers-dropdown" [ngStyle]="{'display': isMapLoaded ? 'block' : 'none'}">
    <div id="layerInfoCard" class="info-card-pos"></div>
    <app-multi-select-dropdown [list]="list" [checkedList]="checkedList"
        (shareCheckedList)="shareCheckedList($event)">
    </app-multi-select-dropdown>
    <!-- Map Switch component -->
    <app-map-switch id="map-switch-advanced-search" [checkedList]="checkedList" [deckOverlay]="deckOverlay" [map]="map" (onAddMapSwitchBtnControler)="addControllers()" *ngIf="map"></app-map-switch>
</div>
</div>
