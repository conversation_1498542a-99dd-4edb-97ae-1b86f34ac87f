import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { PropertyService } from '../../../../services/api-property.service';
import { LoginService } from '../../../../services/login.service';
import { LatLng } from '../../../map-module/models/LatLng';
import { PropertyParcel } from '../../../../models/PropertyParcel';
import { EnumCountry } from '../../../../enumerations/county';
import { EnumApplication } from '../../../../enumerations/application';
import { validateIntegerInput, validatePasteInput } from '../../../../../app/utils';

@Component({
  selector: 'app-property-parcel-modal',
  templateUrl: './property-parcel-modal.component.html',
  styleUrls: ['./property-parcel-modal.component.css']
})
export class PropertyParcelModalComponent implements OnInit {

  pParcelForm: FormGroup;
  @Input() CountryId: number;
  @Input() EntityID: number;
  @Input() propertyParcel: PropertyParcel;
  @Input() fromAddParcel: false;
  @Input() latLng: LatLng
  @Output() onSave: EventEmitter<PropertyParcel> = new EventEmitter<PropertyParcel>();
  @Output() onClose = new EventEmitter();
  DataArray: Array<any> = [];
  propertyParcelCopy: PropertyParcel = new PropertyParcel();
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  showParcelSizeMeasurementTool: boolean  = false;
  parcelSizePolygon: { area: number, polygon: any };

  constructor(private _propertyService: PropertyService, private _loginService: LoginService) { }

  ngOnInit() {
    this.createForm();
    if (this.CountryId == EnumCountry.Australia)
      this.propertyParcel.ParcelSF = this.propertyParcel.ParcelSizeSM;
    else
      this.propertyParcel.ParcelSF = this.propertyParcel.ParcelSize;
      this.propertyParcelCopy = new PropertyParcel();
      this.propertyParcelCopy = JSON.parse(JSON.stringify(this.propertyParcel));
      this.DataArray = [];
  }

  createForm() {
    this.pParcelForm = new FormGroup({
      'Lot': new FormControl(''),
      'Block': new FormControl(''),
      'Subdivision': new FormControl(''),
      'ParcelNo': new FormControl('', Validators.required),
      'ParcelSF': new FormControl('')
    });
  }

  saveParcelDetails() {
    if (this.pParcelForm.valid) {
      if (this.fromAddParcel) {
        this.onSave.emit(this.propertyParcel);
      } else {
        this.mapChangeLog();
        this.propertyParcel.ParcelSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.propertyParcel.ParcelSF);
        this.propertyParcel.EntityID = this.EntityID; 
        this.propertyParcel.ChangeLogJSON = JSON.stringify(this.DataArray);
        this.propertyParcel.ApplicationID = EnumApplication.VST;
        const response_parcel = this._propertyService.PropertyParcelDetailsSave(this.propertyParcel);
        response_parcel.subscribe(result => {
          if (result.status === 201) {
          } else if (result.status === 200) {
            this.onClose.emit();
          }
        });
      }
    }
  }

  DeleteParcelDetails() {
    this.propertyParcel.EntityID = this.EntityID;
    const response_parcel = this._propertyService.PropertyParcelDetailsDelete(this.propertyParcel);
    response_parcel.subscribe(result => {
      if (result.status === 201) {

      } else if (result.status === 200) {
        this.onClose.emit();
      }
    });
  }
  mapChangeLog() {
    let data = this.pParcelForm["controls"];
    let date = new Date().toISOString();
    Object.keys(data).map(i => {
      if (data[i].dirty == true) {
        let index = this.DataArray.findIndex(x => x.Field == i);
        if (index == -1) {
          this.DataArray.push({
            "Field": i, "CurrentValue": data[i].value, "PreviousValue": this.propertyParcelCopy[i], "LoginEntityID": this._loginService.UserInfo.EntityID, "DateTime": date
          });
        }
      }
    });
  }

  onParcelSizeClicked() {
    this.showParcelSizeMeasurementTool = true;
  }

  onParcelSizeSave({ area, polygon }) {
    if (area) {
      this.propertyParcel.ParcelSF = area;
      this.pParcelForm.get('ParcelSF').setValue(area);
      this.parcelSizePolygon = { area, polygon }
    }
    this.showParcelSizeMeasurementTool = false;
  }
}
