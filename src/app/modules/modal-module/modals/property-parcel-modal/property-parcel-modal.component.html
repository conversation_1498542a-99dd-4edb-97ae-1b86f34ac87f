<div class="col-md-12">
  <form [formGroup]="pParcelForm"  (ngSubmit)="saveParcelDetails()">
    <div class="form-group row">
      <div class="col-md-6">
        <div class="form-group row label-value-wrapper">
          <label class="col-md-4 form-control-label" for="text-input">Parcel No</label>
          <div class="col-md-8">
            <input type="text" class="form-control" formControlName="ParcelNo" [(ngModel)]="propertyParcel.ParcelNo" [ngClass]="{'error-field':(!pParcelForm.controls['ParcelNo'].valid)}">
            <span class="error-message" *ngIf="!pParcelForm.controls['ParcelNo'].valid">Parcel number is required</span>
          </div>
        </div>

        <div class="form-group row label-value-wrapper">
          <label class="col-md-4 form-control-label" for="text-input">Parcel Size</label>
          <div class="col-md-8">
            <input type="text" numericOnly allowNegative="false" allowDecimal="true" class="form-control" formControlName="ParcelSF" [(ngModel)]="propertyParcel.ParcelSF" (paste)="validatePasteInput($event, true)"
            (keypress)="validateIntegerInput($event, true)">
            <a href="javascript://" (click)="onParcelSizeClicked()" class="mapBox" [tabindex]="-1"><img src="assets/images/DrawMap.png" [tabindex]="-1"></a>
            <div class="validation-error" *ngIf="(!pParcelForm.controls['ParcelSF'].valid && pParcelForm.controls['ParcelSF'].touched)">Parcel Size Required</div>
          </div>
        </div>

        <div class="form-group row label-value-wrapper">
          <label class="col-md-4 form-control-label" for="text-input">Lot</label>
          <div class="col-md-8">
            <input type="text" maxlength="100" class="form-control" formControlName="Lot" [(ngModel)]="propertyParcel.Lot"
              >
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Lot'].valid && pParcelForm.controls['Lot'].touched)">Lot Required</div>
          </div>
        </div>

        <div class="form-group row label-value-wrapper">
          <label class="col-md-4 form-control-label" for="text-input">Block</label>
          <div class="col-md-8">
            <input type="text" maxlength="100" numericOnly allowNegative="false" allowDecimal="true" class="form-control" formControlName="Block" [(ngModel)]="propertyParcel.Block"
            (paste)="validatePasteInput($event, true)"
            (keypress)="validateIntegerInput($event, true)">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Block'].valid && pParcelForm.controls['Block'].touched)">Block Required</div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group row label-value-wrapper">
          <label class="col-md-4 form-control-label" for="text-input">Subdivision</label>
          <div class="col-md-8">
            <input type="text"  maxlength="100" class="form-control" formControlName="Subdivision" [(ngModel)]="propertyParcel.SubDivision">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Subdivision'].valid && pParcelForm.controls['Subdivision'].touched)">Subdivision Required</div>
          </div>
        </div>
      </div>

    </div>
    <button type="submit" class="btn btn-warn" [disabled]="!pParcelForm.valid">Save Parcel info</button>
    <input *ngIf="(propertyParcel.ParcelID)" class="btn btn-warn" type="button" value="Delete Parcel" (click)="DeleteParcelDetails()" /> 
  </form>
</div>

<!-- Parcel Measurement Tool -->
<div *ngIf="showParcelSizeMeasurementTool" class="col-md-12">
  <imperium-modal [width]="'xl-large'" [height]="'large'" [(visible)]="showParcelSizeMeasurementTool"
    [title]="'Parcel Size Measurement'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-area-measurement-modal [latLng]="{lat: latLng.Latitude, lng: latLng.Longitude}"
        [areaPolygon]="parcelSizePolygon" (onAreaSave)="onParcelSizeSave($event)"></app-area-measurement-modal>
    </ng-template>
  </imperium-modal>
</div>
