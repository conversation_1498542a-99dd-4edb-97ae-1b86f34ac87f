import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { MapOptions } from '../../../../modules/map-module/models/MapOptions';
import { MapService } from '../../../../modules/map-module/service/map-service.service';
import { CommunicationService, CommunicationModel } from '../../../../services/communication.service';
import { Property } from '../../../../models/Property';
import { LatLng } from '../../../../modules/map-module/models/LatLng';
import * as MapEnum from '../../../../modules/map-module/models/MapEnum';
import { PropertyService } from '../../../../services/api-property.service';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
import { confirmConfiguration } from '../../../../modules/notification/models/confirmConfiguration';
import { LoginService } from '../../../../services/login.service';
import { PropertyLocation } from '../../../../models/PropertyLocation';
import { RooftopUtility } from '../../../../models/rooftopUtility';
import { MapBound } from '../../../../modules/map-module/models/mapBound';
import { ParcelService } from '../../../../services/parcel.service';
import { MapHelperService } from '../../../../services/map-helper.service';
import { CommonStrings } from '../../../../constants';

@Component({
  selector: 'app-property-rooftop-utility-modal',
  templateUrl: './property-rooftop-utility-modal.component.html',
  styleUrls: ['./property-rooftop-utility-modal.component.scss']
})
export class PropertyRooftopUtilityModalComponent implements OnInit {
  @Input() rooftopUtility: RooftopUtility;
  @Input() propertyLocation: PropertyLocation;
  @Output() onRooftopChange = new EventEmitter<RooftopUtility>();


  public mapOptions: MapOptions;
  public map: any;
  //private _mapService: MapService = new MapService();
  private propertyDetails: Property;
  private markers: Array<any> = new Array<any>();
  polylines: any;

  public showAddEditPropertyForm: boolean;

  constructor(private _communicationService: CommunicationService,
    private _mapService: MapService,
    private _propertyService: PropertyService,
    private _notificationService: NotificationService,
    private _loginService: LoginService,
    private _parcelService: ParcelService, 
    private _mapHelperService: MapHelperService) {

  }

  ngOnInit() {
    if (!this.rooftopUtility.Latitude) {
      this.rooftopUtility.Latitude = -37.814;
      this.rooftopUtility.Longitude = 144.96332;
    }
    this.initMap();
  }



  private initMap() {

    this.mapOptions = new MapOptions('RoofTopPropertyMap');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 9, 7, null, this.rooftopUtility.Latitude, this.rooftopUtility.Longitude);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.ZoomLevel = 18;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.ArterialRoad, MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.LocalRoad, MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    let marker = this._mapService.PlaceMarker(this.map, this.rooftopUtility.Latitude, this.rooftopUtility.Longitude, true)
    marker.data = this.rooftopUtility;
    this.markers.push(marker);
    this._mapService.OnMarkerDragEnd(marker, (latlng) => {

      let configuration: confirmConfiguration = new confirmConfiguration();
      configuration.Message = CommonStrings.DialogConfigurations.Messages.UpdateMarkerLocationConfirmationMessage;
      configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
      configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
      configuration.OkButton.Callback = () => {
        if (this.rooftopUtility.PropertyID) {
          this.changePropertyDetails(marker, latlng);
        }
        else {
          this.changeCompanyDetails(marker, latlng);
        }
      };
      configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
      configuration.CancelButton.Callback = () => {
        this._mapService.MoveMarker(marker, marker.data.Latitude, marker.data.Longitude);
      }

      this._notificationService.CustomDialog(configuration);

    });

    this.drawParcels();

  }



  changePropertyDetails(marker, latlng) {
    this._propertyService.RelocateProperty(marker.data.PropertyID, latlng.Latitude, latlng.Longitude, this._loginService.UserInfo.EntityID);
    marker.data.Latitude = latlng.Latitude;
    marker.data.Longitude = latlng.Longitude;

    let commModel = new CommunicationModel();
    commModel.Key = 'propertyRelocated';
    commModel.data = marker.data;
    this._communicationService.broadcast(commModel);
  }

  changeCompanyDetails(marker, latlng) {
    this.rooftopUtility.Latitude = latlng.Latitude;
    this.rooftopUtility.Longitude = latlng.Longitude;
    this.onRooftopChange.emit(this.rooftopUtility);
  }

  drawParcels(){
    this._mapService.OnMapViewPortChangedOnce(this.map, (boundProperties: MapBound) => {
      if (this._mapService.GetMapZoomLevel(this.map) >= 18) {
        this._parcelService.getParcelShapeTiles(boundProperties.SouthWest.Latitude, boundProperties.SouthWest.Longitude, boundProperties.NorthEast.Latitude, boundProperties.NorthEast.Longitude).subscribe((result: any) => {
          let response = result.body;
          if (response && response.responseData) {
            response.responseData.forEach(element => {
              let parcelList = this._mapHelperService.GetLatLngListFromPolygon(element.ParcelShape);
              this.polylines = this._mapService.DrawPolygonOnMap(this.map, parcelList, this.polylines, false);
            });
          }
        });
      }
      if (this._mapService.GetMapZoomLevel(this.map) <= 18) {
        this.polylines = this._mapService.ClearPolygons(this.polylines);
      }

      this.drawParcels();

    });
  }

}  
