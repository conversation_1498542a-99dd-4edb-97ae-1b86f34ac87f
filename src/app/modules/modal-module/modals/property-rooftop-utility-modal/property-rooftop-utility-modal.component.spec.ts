import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PropertyParcelModalComponent } from './property-rooftop-utility-modal.component';

describe('PropertyRooftopUtilityModalComponent', () => {
  let component: PropertyRooftopUtilityModalComponent;
  let fixture: ComponentFixture<PropertyRooftopUtilityModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PropertyRooftopUtilityModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PropertyRooftopUtilityModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
