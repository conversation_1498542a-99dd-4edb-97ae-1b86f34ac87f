// th{
//     background: #191d64!important;
//     color: #fff!important;
// }


.showspin {
  font-size: 20px;
}

.max-width-250 {
  max-width: 250px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.max-width-180 {
  max-width: 180px;
}

.input-with-icon {
  position: relative;
}

.input-with-icon:hover i {
  color: #d94624;
}

.input-with-icon i {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}

.pagination>.disabled>a,
.pagination>.disabled>a:focus,
.pagination>.disabled>a:hover,
.pagination>.disabled>span,
.pagination>.disabled>span:focus,
.pagination>.disabled>span:hover {
  color: #0d0e29 !important;
  cursor: not-allowed !important;
  background-color: #f4f5f5 !important;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f4f5f5), to(#dfdddd)) !important;
  background-image: -webkit-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -moz-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -ms-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -o-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: linear-gradient(to bottom, #f4f5f5, #dfdddd) !important;
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#f4f5f5, endColorstr=#dfdddd) !important;
}

.page-item.active .page-link,
.pagination-datatables li.active .page-link,
.pagination li.active .page-link,
.page-item.active .pagination-datatables li a,
.pagination-datatables li .page-item.active a,
.pagination-datatables li.active a,
.page-item.active .pagination li a,
.pagination li .page-item.active a,
.pagination li.active a {
  background-color: #3093c7 !important;
  ;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#3093c7), to(#1c5a85)) !important;
  ;
  background-image: -webkit-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -moz-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -ms-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -o-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: linear-gradient(to bottom, #3093c7, #1c5a85) !important;
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#3093c7, endColorstr=#1c5a85) !important;
}

.page-item.active .page-link,
.pagination-datatables li.active .page-link,
.pagination li.active .page-link,
.page-item.active .pagination-datatables li a,
.pagination-datatables li .page-item.active a,
.pagination-datatables li.active a,
.page-item.active .pagination li a,
.pagination li .page-item.active a,
.pagination li.active a {
  z-index: 2;
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}

.page-link,
.pagination-datatables li a,
.pagination li a {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #20a8d8;
  background-color: #fff;
  border: 1px solid #ddd;
}

table {
  font-size: 14px !important;
}

table tbody tr:nth-child(even) {
  background-color: #f4f4f4;
}

thead th {
  background: #1e4b7b;
  color: #fff;
}

.trimmed-text {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit text to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal; /* Allow text wrapping */
  max-width: 250px; /* Adjust based on your requirements */
}