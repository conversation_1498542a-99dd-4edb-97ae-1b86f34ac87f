import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { PropertyService } from '../../../../services/api-property.service';
import { PagerService } from '../../../../services/pager.service';
import { LoginService } from '../../../../services/login.service';
import { DatePipe } from '@angular/common';
import { Subscription } from 'rxjs';
import { CommunicationService } from '../../../../services/communication.service';
import { LatLng } from '../../../../modules/map-module/models/LatLng';

@Component({
  selector: 'app-changelog-modal',
  templateUrl: './changelog-modal.component.html',
  styleUrls: ['./changelog-modal.component.scss']
})
export class ChangelogModalComponent implements OnInit {

  @Input() parentId: any;
  @Input() changelogType: any;
  @Input() location: LatLng;
  @Output() onClose = new EventEmitter();
  changeLogDetails: Array<any> = new Array<any>();
  changeLogDetailsCopy: Array<any> = new Array<any>();
  pagedchangeLogDetails: Array<any> = new Array<any>();
  IsLoader: boolean = false;
  pager: any = {};
  pageSize: any = 100;
  dateFormat: string;
  filterInput: string = '';
  IsApplication: boolean = true;
  showBuildingFootprintPreviewModal: boolean = false;
  selectedFootprintLog: any;
  isResearchStatusHistory: boolean = false;
  propertySaveOrNewPropertyFetchListener:Subscription;
  researchStatusHistoryListener: Subscription;
  constructor(private _propertyService: PropertyService, private pagerService: PagerService, private _loginService: LoginService, private _datePipe: DatePipe,private communicationService: CommunicationService,
  ) {
    this.dateFormat = this._loginService.UserInfo.DateFormat;
    this.propertySaveOrNewPropertyFetchListener = this.communicationService.subscribe('fetchChangeLog').subscribe(result => {
      this.parentId = result?.data
      this.fetchChangeLogData();
    });
  }

  ngOnDestroy(){
    this.propertySaveOrNewPropertyFetchListener?.unsubscribe();
  }

  ngOnInit() {
  this.fetchChangeLogData();
  }

  fetchChangeLogData() {
    this.IsLoader = true;
    const response_location = this._propertyService.GetChangeLog(this.parentId, this.changelogType, this._loginService.UserInfo.EntityID);
    response_location.subscribe(result => {
      if (result.body && !result.body.error) {
        this.changeLogDetails = result?.body?.responseData[0];
        this.IsLoader = false;
        if (this.changeLogDetails?.length > 0) {
          this.changeLogDetails?.forEach(element => {
            if (element.DataTypeID == 3) {
              try {
                element.OldValue = this._datePipe?.transform(element.OldValue, this.dateFormat, "+0000");
                element.NewValue = this._datePipe?.transform(element.NewValue, this.dateFormat, "+0000");
              } catch (error) {
              }
            }
          });
          this.changeLogDetailsCopy = this.changeLogDetails;
          this.IsLoader = false;
          this.setPage(1);
        }
      }
    });
  }
  cancel() {
    this.onClose.emit();
  }
  setPage(page: number) {
    this.filter(page);
    if (page < 1 || page > this.pager.totalPages || page > this.pager.totalPages) {
      return;
    }
  }
  filter(page: number) {
    if (this.changeLogDetails.length < parseInt(this.pageSize)) {
      this.pager.currentPage = 1;
      page = 1;
    }
    page = page || this.pager.currentPage;
    this.pager = this.pagerService.getPager(this.changeLogDetails.length, page, parseInt(this.pageSize));
    this.pagedchangeLogDetails = this.changeLogDetails.slice(this.pager.startIndex, this.pager.endIndex + 1);
    // window.scrollTo(0,0);
  }
  clearSearchFilter() {
    this.filterInput = '';
    this.clearFun();
  }

  clearFun() {
    this.changeLogDetails = this.changeLogDetailsCopy;
    this.pagedchangeLogDetails = this.changeLogDetailsCopy.slice(0, this.pageSize);
    this.setPage(1);
  }
  filterItem(value) {
    if (value.length >= 3) {
      this.changeLogDetails = Object.assign([], this.changeLogDetailsCopy).filter(item => !!item.DisplayText && item.DisplayText.toLowerCase().indexOf(value.toLowerCase()) > -1);
      this.pagedchangeLogDetails = this.changeLogDetails.slice(0, this.pageSize);
    }
    else {
      this.clearFun();
    }
    this.setPage(1);
  }

  showBuildingFootprintPreview(log) {
    this.showBuildingFootprintPreviewModal = true;
    this.selectedFootprintLog = log;
  }

  onClosePreviewModal(){
    this.showBuildingFootprintPreviewModal = false;
    this.selectedFootprintLog = undefined;
  }

}
