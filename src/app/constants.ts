import { LocationFormControls } from './enumerations/locationFormControls';

export const StringValuedSortParams = ['AddressStreetName', 'PropertyType', 'City', 'SpecificUses', 'ResearchTypeName', 'AuditStatus'];
export const CommonStrings = {
    Messages: {
        FieldsRequired: 'The below detail(s) are required',
        StreetNameRequired: 'Street Name is required',
        StreetNumberMinRequired: 'Street Number Min is required'
    },
    ErrorMessages: {
        MasterStrataBuildingErrorMessage: 'Building area is required',
        FootprintErrorMessage: 'Draw the boundaries to calculate the building footprint area or provide valid building size',
        PropertySaveFail: 'Property Saving failed',
        ResearchStatusErrorMessage: 'Please provide Research status for property',
        NoPropertyChanges: 'No property changes!',
        MinimumCommentsLengthErrorMessage: 'Comments of minimum 25 characters must be filled or multiple polygons required',
        LotSizeSFErrorMessage: 'Provide valid lot size',
        MaxFloorSizeErrorMessage: 'Provide valid max floor size',
        MinFloorSizeErrorMessage: 'Provide valid min floor size',
        MaxFloorErrorValidationMessage: 'Max Floor Size cannot be greater than Building Size and cannot be less than Min Floor Size',
        MinFloorErrorValidationMessage: 'Min Floor Size cannot be greater than Building Size or Max Floor Size',
        BuildingSizeExceedError : 'value must be greater than 85% of the building size',
        SaveAuditStatusFailMessage: 'Failed to update audit status',
        ChildFreeholdCreationFailure: 'Failed to created child freeholds',
        ChildLinkingFailureMsg: 'Failed to link Child pins to Master',
        AtleastOneFootprintIsRequired: 'The building footprint should be captured for at least one floor.',
        GresbScoreErrorMessage: 'Provide valid GRESB Score',
        FillAllMandatoryfields: 'Please fill all mandatory fields and ensure the entered values are valid.',
        PinInsideFootPrint: 'The pin location must be within at least one footprint',
        LookupDataFailureMessage: 'Failed to fetch lookup data',
        GresbScoreMinErrorMessage: 'GRESB score Min cannot be greater than GRESB score Max',
        ClearHeightMinErrorMessage: 'Clear height Min cannot be greater than Clear height Max',
        ClearHeightValueError: 'Value should be between 3 and 999',
        YearBuildError: 'Year Built must be less than renovated year and cannot be greater than current year',
        YearRenovatedError: 'Year Renovated should be greater than Year Built and cannot be greater than current year',
        OfficeFormValidations: 'Please complete all required fields and ensure they are valid in the Office Details section.',
        IndustrialFormValidations: 'Please complete all required fields and ensure they are valid in the Industrial Details section.',
        AdditionalAddressDeleteFailed: 'Additional address delete failed.',
        ConstructionStartDateError: 'Construction start date should not be greater than Est. completion and actual completion dates.',
        FootprintsAreMissing: 'All polygons must have footprints.',
        ContributedGBAErrorMessage: 'Please provide valid Contributed GBA',
        ContributedGBASourceErrorMessage: 'Please provide valid Contributed GBA Source',
        SaleAutoEnrichmentFail: 'Auto enriching Sale from Property failed',
    },
    DialogConfigurations: {
        Messages: {
            PropertyUseMultipleAllocationMessage: 'This property has multiple allocations. Please use ERC application to change property use and edit allocations manually.',
            CancelChangesConfirmationMessage: "Changes made will be lost. Do you wish to continue?",
            DropPinMessage: "Please select/drop a pin on the map to save",
            SelectPropertyUseMessage: "Please select a property use to continue.",
            UpdateMarkerLocationConfirmationMessage: 'The marker location will be updated. Do you want to continue?',
            StrataTypeChangeConfirmationMessage: 'Strata type change will remove the existing relationship with the master property, do you wish to continue?',
            PropertyUseChangeConfirmationMessage: 'Property use change will clear all the footprints. Do you wish to continue?',
            BuildingSizeChangeConfirmationMessage: "Building Size is recalculate for the allocations.",
            DeletePropertyAllocationConfirmationMessage: "The property allocation will be deleted. Do you want to continue?",
            DeleteAdditionalUseConfirmationMessage: "The additional use will be deleted. Do you want to continue?",
            DeleteMediaFileConfirmationMessage: "The media file will be deleted. Do you want to continue?",
            SetAsDefaultImageConfirmationMessage: 'This image will be set as default image of the property. Do you want to continue?',
            DeleteNoteConfirmationMessage: 'The note will be deleted. Do you want to continue?',
            FloorCountChangeConfirmation: 'Floors count is recalculate for the allocations.',
            FloorAllocatedMessage: 'This floor is already allocated',
            SelectMinFloorNumber: 'Please select min floor number',
            AvailableAllocationExceeded: 'Current allocation is more than available allocation',
            IncorrectFloorCount: 'Incorrect floor count and is auto updated based on min and max floor number',
            MinAndMaxFloorCountError: 'Floor Number Max cannot be less than floor number min',
            AllocatedFloorsMessage: 'Floor allocations have been assigned. Kindly revise the allocations and proceed with updating the floors accordingly.',
            FloorCountExceedingMessage: 'Floor count cannot be more than available space',
            StrataMinMaxUnitMessage: 'Please enter valid child strata range',
            StrataMaxUnitMessage: 'Maximum Unit cannot be less or equal to Minimum Unit',
            FreeholdMaxUnitMessage: 'Maximum Unit cannot be less or equal to Minimum Unit',
            FreeholdMinMaxUnitMessage: 'Please enter valid child freehold range',
            FreeholdMinMaxUnitNegativeMessage: 'Units cannot be negative values',
            StrataUnitsExceedingMessage: 'Only 30 units can be created at once',
            FreeholdsUnitsExceedingMessage: 'Only 30 units can be created at once',
            FreeholdMinMaxMessage: 'Both min and max fields are required',
            PropertySaveSuccessfull: 'Property Saved Successfully',
            AerialAndStreetViewImageMissing: 'Both Aerial and Street View screenshots are required',
            AerialViewImageMissing: 'Aerial View screenshot is required',
            SomeFloorsHaveFootprintMessage: "Some of the floors have footprints, please delete the footprints and update the floors",
            DeleteFloorData: 'Deleting this record will result in data loss. Are you sure you want to proceed? ',
            ValidLotSize: 'Please enter valid Lot size',
            ValidParcelNumber: 'Please enter valid Parcel Number',
            ChildPropertyCreationMessage: 'Child freeholds created successfully',
            UnHidePropertyMessage: 'You cannot select a hidden pin to be included as a Child Freehold. Do you wish to continue?',
            PropertyFromDifferentParcel: 'All selected building pins must be inside the selected Parcel',
            PleaseEnableAndSelectParcelLayer: 'Please select a parcel to proceed',
            MasterAndChildParcelMismatch: 'Child freehold is not in selected master freehold, Please select correct parcel',
            AllFloorsDoesntHaveFootprintMessage: 'Some of the floors don\'t have a footprint. Do you wish to continue?',
            RecalculateAllocationOnFloorChange: 'Recalculate allocations percentage based on the updated floor count?',
            RecalculateAllocationOnSizeChange: 'Recalculate allocations percentage based on the updated building size?',
            NoDefaultImageMessage: 'This property does not have an existing Default image. Do you wish to set this image as Default?',
            ReplaceDefaultImageMessage: 'This property has an existing Default image. Do you wish to replace it with the new image set as Default?',
            DefaultImageConflictMessage: 'You cannot replace an Empirical Photography image as Default with a Street view screenshot image',
            DeleteAdditionalAddressConfirmationMessage: "The additional address will be deleted. Do you want to continue?",
            UpdatePropertyResearchStatusConfirmationMessage: 'Are you sure you want to update the property research status',
            CancelUpdatingSoldSQMConfirmationMessage: "By canceling, you will be logging Sales Transactions as having not been updated to new values.",
        },
        Title: {
            EmpiricalCRE: 'Empirical CRE',
            Arealytics: 'Arealytics',
            RecalculateAllocations: 'Recalculate Allocations?',
            FloorwisePolygon: 'Floorwise Polygon?',
            Allocations: 'Allocations',
            AdditionalAddress: 'Additional Address',
            UpdatePropertyStatus: 'Update Property Research Status',
            CancelUpdatingSoldSQM: 'Cancel without Update'
        },
        ButtonLabels: {
            Ok: 'Ok',
            SaveChanges: 'Save Changes',
            DoNotSave: 'Do Not Save',
            Cancel: 'Cancel',
            Yes: 'Yes',
            No: 'No',
            UpdateStatus: 'Update Status'
        },
    },
    SuccessMessages: {
        SaveAuditStatusSuccessMessage: 'Successfully updated audit status',
        PropertyDetailsAndAuditStatusSave: 'Property details and Audit Status is updated successfully.',
        LinkedChildToMasterSuccessfully: 'Linked Child to Master successfully',
        AdditionalAddressDeleteMessage: 'Additional address deleted successfully',
        SaleAutoEnrichmentSuccess: 'Auto-enrichment completed successfully',
    },
}
export const azureMap = {
    MaxZoom: {
      Satellite:19,
      RoadMap:21
    },
  
    TilesName: {
      AzureSatelliteMap: 'Azure Satellite Map',
      AzureHybridMapLayer: 'Azure Hybrid Map Layer',
      AzureRoadMap: 'Azure Road Map',
      AzureTerrainMapLayer: 'Azure Terrain Map Layer'
    },
    
    TilesetIDs: {
      AzureSatelliteMap: 'microsoft.imagery',
      AzureRoadMap: 'microsoft.base.road',
      AzureTerrainMapLayer: 'microsoft.terra.main',
    }
  }

export const LocationValidationsRequiredFields = [LocationFormControls.AddressType, LocationFormControls.PropertyName, LocationFormControls.CountryID, LocationFormControls.State, LocationFormControls.Zip, LocationFormControls.City];
export const LocationFieldChangeLogNames = {
    MarketId:'MarketId',
    SubMarketID:'SubMarketID',
    StreetPrefix1:'StreetPrefix1',
    StreetPrefix2:'StreetPrefix2',
    StreetSuffix1:'StreetSuffix1',
    StreetSuffix2:'StreetSuffix2',
    CityID:'CityID',
    County:'County',
    State:'State',
    CountryID:'CountryID',
    PartOfComplex:'PartOfComplex',
    RooftopSourceID:'RooftopSourceID',
    Quadrant:'Quadrant',
    UseAddressAsPropertyName:'UseAddressAsPropertyName'
}

export const AddressTypeValues = { Address: 0, Intersection: 1 };

export const PartOfComplex = 'Part of Complex';

export const AddressTypeValueNames = {
    Address: 'Address',
    Intersection: 'Intersection'
}

export const ChangeLogTabNames = {
    Location: 'Location',
    PropertyDetails: 'Property Details',
    ResearchStatusHistory: 'Research Status History'
}

export const ChangeLogTypes = {
    Address: 'Address',
    Property: 'Property',
    ResearchStatus: 'ResearchStatus'
}

export const HardStandSizeSourceFields = {
    Name: 'SizeSourceName',
    Id: 'HardstandAreaSourceID'
}

export const DefaultDateFormat = 'dd/MM/yyyy';
export const DefaultUnitDisplayTextSize = 'sqm';

export const MeasurementTypes = {
    CONTRIBUTED: {
      title: 'Contributed GBA',
      class: 'green',
      label: 'C',
      },
    AERIAL: {
      title: 'Aerial Measurement',
      class: 'blue',
      label: 'A',
    },
    AVERAGEESTIMATION: {
      title: 'Averaged Estimate',
      class: 'blue',
      label: 'A',
    },
    ESTIMATED: {
      title: 'Estimated',
      class: 'green',
      label: 'E'
    }
}
