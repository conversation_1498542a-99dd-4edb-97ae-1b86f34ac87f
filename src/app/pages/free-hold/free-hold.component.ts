import {
  Component,
  Input,
  OnInit,
  Output,
  EventEmitter,
  SimpleChanges,
  NgZone,
} from '@angular/core';
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { Property } from '../../models/Property';
import { PropertyService } from '../../services/api-property.service';
import { LoginService } from '../../services/login.service';
import { EnumCondoTypeName } from '../../enumerations/condoType';
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { Subscription } from 'rxjs';
import {
  IMetaData,
  MetaDataIndexedDBService,
} from '../../services/indexed-db-service.service';
import { UnitConversionEnum } from '../../enumerations/unitConversion';
import { MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { getMeasurementTypeClassname, getMeasurementTypeLabel, getMeasurementTypeTitle, getSizeValue } from '../../../app/utils';

@Component({
  selector: 'app-free-hold',
  templateUrl: './free-hold.component.html',
  styleUrls: ['./free-hold.component.css'],
})
export class FreeHoldComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  @Output() showPropertyInfo = new EventEmitter();
  @Input() property: Property;
  @Input() selectedUseTypeID: any;
  @Output() addNewFreeholdUnit: EventEmitter<any> = new EventEmitter<any>();
  updateFormSubscription: Subscription;
  fetchFreeholdListSubscription: Subscription;
  propertyFreeholdList: any[];
  propertiesLoading: boolean = false;
  freeholdTableHeader: any;
  UnitId: number;
  UnitDisplayTextSize: any;
  propertyId: any;
  BuildingSizeHeader = '';
  BuildingSizeValue: any = '';
  LotSizeValue: any = '';
  visitedFreeholdIds: any[];
  visitedPropertyIds: any = [];
  editedPropertyIds: any = [];
  editedFreeholdIds: any = [];
  isNavigationFromSearch: any;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  showAddFreeholdBtn = false;
  constructor(
    private communicationService: CommunicationService,
    private zone: NgZone,
    private _propertyService: PropertyService,
    private _loginService: LoginService
  ) {
    this.UnitId = this._loginService.UserInfo.UnitID;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.updateFormSubscription = this.communicationService
      .subscribe('updatePropertyForm')
      .subscribe((result) => {
        this.zone.run(() => {
          if (this.initialDetails.propertyId != result.data.propertyId) {
            this.propertyId = result.data.propertyId;
            this.initialDetails = result.data;
            sessionStorage.setItem(
              SessionStorageKeys.LastVisitedStrataProperty,
              JSON.stringify(this.property)
            );
            this.getFreeholdPropertyDetails();
          }
        });
      });
    this.fetchFreeholdListSubscription = this.communicationService
    .subscribe('fetchFreeholdList')
    .subscribe((result) => {
      this.zone.run(() => {
        this.getFreeholdPropertyDetails();
      });
    });
  }

  ngOnDestroy() {
    this.updateFormSubscription.unsubscribe();
    this.fetchFreeholdListSubscription.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.property) {
      this.showAddFreeholdBtn = changes.property.currentValue.CondoTypeID === EnumCondoTypeName.Master_Freehold;
    }
  }

  ngOnInit() {
    this.showAddFreeholdBtn = this.property.CondoTypeID === EnumCondoTypeName.Master_Freehold;
    this.editedFreeholdIds =
      JSON.parse(sessionStorage.getItem(SessionStorageKeys.EditedStrataIds)) ||
      [];
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.isNavigationFromSearch = JSON.parse(
      sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch)
    );
    if (this.isNavigationFromSearch) {
      this.getVisitedProperties();
    }
    this.getEditedProperties();
    this.propertyId = this.initialDetails.propertyId;
    if (this.UnitId === UnitConversionEnum.Metric) {
      this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
      this.BuildingSizeValue = 'BuildingSizeSM';
      this.LotSizeValue = 'LotSizeSM';
    } else {
      this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
      this.BuildingSizeValue = 'BuildingSizeSF';
      this.LotSizeValue = 'LotSizeSF';
    }
    this.getTableHeader();
    if (!this.propertyFreeholdList || this.propertyFreeholdList.length <= 0) {
      this.getFreeholdPropertyDetails();
      sessionStorage.setItem(
        SessionStorageKeys.LastVisitedStrataProperty,
        JSON.stringify(this.property)
      );
    }
  }

  async getVisitedProperties() {
    try {
      const searchData: IMetaData | null =
        await this.metaDataIndexedDBService.retriveDataFromMetaData(
          MetaDataCollectionKeys.VisitedPropertyIds
        );
      if (searchData) {
        this.visitedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getEditedProperties() {
    try {
      const searchData: IMetaData | null =
        await this.metaDataIndexedDBService.retriveDataFromMetaData(
          MetaDataCollectionKeys.EditedPropertyIds
        );
      if (searchData) {
        this.editedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  getFreeholdPropertyDetails() {
    const sortedPropertyFreeholdList = [];
    let activeRecord, masterFreeholdRecord;
    this.propertiesLoading = true;
    const response_freehold = this._propertyService.getPropertyFreeholdDetails(
      this.initialDetails.propertyId,
      null,
      null
    );
    response_freehold.subscribe((result) => {
      if (!result.body.error && result.body.responseData[0] && result.body.responseData[0].length > 0) {
        this.propertyFreeholdList = result.body.responseData[0];
        const results = this.propertyFreeholdList || [];
        activeRecord = results.find(
          (row) => row.PropertyID === this.propertyId
        );
        masterFreeholdRecord = results.find((row) => row.IsMaster);
        let freeholdListWithoutActiveAndMaster = results.filter(
          (row) => !(row.PropertyID === this.propertyId) && !row.IsMaster
        );
        freeholdListWithoutActiveAndMaster =
          freeholdListWithoutActiveAndMaster.sort(
            this.dynamicIntegerSort('StrataUnit', 'Ascending')
          );
        sortedPropertyFreeholdList.push(activeRecord);
        sortedPropertyFreeholdList.push(...freeholdListWithoutActiveAndMaster);
        if (activeRecord && masterFreeholdRecord &&  activeRecord.PropertyID != masterFreeholdRecord.PropertyID) {
          sortedPropertyFreeholdList.push(masterFreeholdRecord);
        }
        this.propertyFreeholdList = sortedPropertyFreeholdList;
        this.propertyFreeholdList = this.propertyFreeholdList.map(row => ({
          ...row,
          sizeValue: getSizeValue(this.propertyFreeholdList, row, this.UnitId),
          measurementTypeTitle: getMeasurementTypeTitle(this.propertyFreeholdList, row),
          measurementTypeClass: getMeasurementTypeClassname(this.propertyFreeholdList, row),
          measurementTypeLabel: getMeasurementTypeLabel(this.propertyFreeholdList, row)
        }));
        const childFreeholds = this.propertyFreeholdList.filter(freehold => !freehold?.IsMaster);
        if (childFreeholds.length > 0 && childFreeholds.every((freehold) => !freehold?.IsMaster && freehold?.HasNoBuildingFootprints)) {
          if (this.propertyId === this.propertyFreeholdList.find(freehold => freehold?.IsMaster)?.PropertyID) {
            let commModel = new CommunicationModel();
            commModel.Key = 'showContributedGBAInTopSection';
            commModel.data = true;
            this.communicationService.broadcast(commModel);
          }
        }
        this.visitedFreeholdIds =
          JSON.parse(
            sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)
          ) || [];
        const freeholdPropertyIds = sortedPropertyFreeholdList.map(
          (freehold) => freehold && freehold.PropertyID
        );
        const sessionStrataIds = sessionStorage.getItem(
          SessionStorageKeys.StrataPropertyIds
        );
        if (sessionStrataIds) {
          const sessionStorageStrata = JSON.parse(sessionStrataIds);
          if (
            !this.arraysHaveSameElements(
              sessionStorageStrata,
              freeholdPropertyIds
            )
          ) {
            this.setSessionStrataIds(freeholdPropertyIds);
          }
        } else {
          this.setSessionStrataIds(freeholdPropertyIds);
        }
        this.propertiesLoading = false;
      } else {
        this.propertiesLoading = false;
      }
    }, error => {
      this.propertiesLoading = false;
    });
  }

  setSessionStrataIds(freeholdIds) {
    sessionStorage.setItem(
      SessionStorageKeys.StrataPropertyIds,
      JSON.stringify(freeholdIds)
    );
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
  }

  getTableHeader() {
    this.freeholdTableHeader = [
      { field: 'StrataType', header: 'Type' },
      { field: 'PropertyName', header: 'Property Name' },
      { field: 'Address', header: 'Address' },
      { field: 'StrataUnit', header: 'Child Unit' },
      { field: this.BuildingSizeValue, header: this.BuildingSizeHeader },
      { field: this.LotSizeValue, header: 'Lot Size' },
      { field: 'ParcelNumbers', header: 'Parcel #' },
      { field: 'PropertyID', header: 'PropertyID' },
    ];
  }

  showPropertySummary(data) {
    this.showPropertyInfo.emit(data.PropertyID);
  }
  sortProperty(sortParam, sortOrder) {
    if (sortParam == 'Address' || sortParam == 'ParcelNumbers') {
      this.propertyFreeholdList = this.propertyFreeholdList.sort(
        this.dynamicSort(sortParam, sortOrder)
      );
    } else {
      this.propertyFreeholdList = this.propertyFreeholdList.sort(
        this.dynamicIntegerSort(sortParam, sortOrder)
      );
    }
  }

  dynamicSort(Property: string, sortDirection: string) {
    let sortOrder = 1;
    sortOrder = sortDirection === 'Ascending' ? 1 : -1;
    return function (a, b) {
      const result =
        TryParseInt(a[Property], a[Property]) <
        TryParseInt(b[Property], b[Property])
          ? -1
          : TryParseInt(a[Property], a[Property]) >
            TryParseInt(b[Property], b[Property])
          ? 1
          : 0;
      return result * sortOrder;
    };
  }

  dynamicIntegerSort(Property: string, sortDirection: string) {
    let sortOrder: number;
    sortOrder = sortDirection === 'Ascending' ? 1 : -1;
    return function (a, b) {
      const result =
        getInt(a[Property]) < getInt(b[Property])
          ? -1
          : getInt(a[Property]) > getInt(b[Property])
          ? 1
          : 0;
      return result * sortOrder;
    };
  }

  arraysHaveSameElements(arr1, arr2) {
    return (
      this.allElementsInArray1AreInArray2(arr1, arr2) &&
      this.allElementsInArray1AreInArray2(arr2, arr1)
    );
  }

  allElementsInArray1AreInArray2(arr1, arr2) {
    return arr1.every((element) => arr2.includes(element));
  }

  addFreehold(isMultiStrata = false) {
    let freeholdMin = undefined;
    const findLastValue = (arr: any[]): number => {
      if (!arr || arr.length === 0) {
        return 0;
      }

      let max = Number.MIN_VALUE;
      for (let i = 0; i < arr.length; i++) {
        const freeholdUnit = Number(arr[i].StrataUnit);
        if (!isNaN(freeholdUnit) && freeholdUnit > max) {
          max = freeholdUnit;
        }
      }
      return max;
    };

    const lastMax = findLastValue(this.propertyFreeholdList);
    freeholdMin = `${lastMax + 1}`;
    this.addNewFreeholdUnit.emit({
      strataMin: freeholdMin,
      isMultiStrata,
      strataList: this.propertyFreeholdList,
      isFreehold: true
    });
  }

  isPropertyInList(property, propertyList, freeholdList) {
    if (this.isNavigationFromSearch && propertyList && propertyList.length > 0) {
        return propertyList.includes(property);
    } else if (freeholdList && freeholdList.length > 0) {
        return freeholdList.includes(property);
    }
    return false;
  }

  isPropertyVisited(property) {
      return this.isPropertyInList(property, this.visitedPropertyIds, this.visitedFreeholdIds);
  }

  isPropertyEdited(propertyId) {
      return this.isPropertyInList(propertyId, this.editedPropertyIds, this.editedFreeholdIds);
  }

  getColumnWidth(header: string): string {
    switch (header) {
    case 'Type': return '120px';
    case 'Property Name': return '160px';
    case 'Address': return '160px';
    case 'Strata Unit': return '95px';
    case this.BuildingSizeHeader: return '90px';
    case 'Lot Size': return '90px';
    case 'Parcel #': return '120px';
    case 'PropertyID': return '100px';
    default: return '120px';
}
}
  getScrollHeight() {
    return this.propertyFreeholdList?.length > 6 ? '50vh' : 'auto';
  }
}

function getInt(value) {
  if (value) {
    const match = value.toString().match(/\d+/);
    if (match && match.length > 0 && match[0]) {
      return TryParseInt(match[0], match[0]);
    }
  }
  if (value === 0) {
    return 0;
  } else {
    return -1;
  }
}

function TryParseInt(str: any, defaultValue: any): any {
  let retValue = defaultValue;
  if (str !== null) {
    if (str.length > 0) {
      if (!isNaN(str)) {
        retValue = parseInt(str, 10);
      } else {
        retValue = str.toLowerCase();
      }
    }
  } else {
    retValue = '';
  }
  return retValue;
}
