import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FreeHoldComponent } from './free-hold.component';

describe('FreeHoldComponent', () => {
  let component: FreeHoldComponent;
  let fixture: ComponentFixture<FreeHoldComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ FreeHoldComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FreeHoldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
