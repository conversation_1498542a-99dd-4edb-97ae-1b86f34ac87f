<div id="header" class="header navbar-default">
  <div class="navbar-header">
    <a routerLink="/" class="navbar-brand navbar-brand-log"><img src="../assets/images/primaryLogo.png" class="img-fluid"
        alt="logo" title="Arealytics"></a>
      
    <!-- <button type="button" class="btn btn-primary" (click)="loginToERC()" style=" position:absolute; margin-left: 50%; margin-top:10px;">Property Audit Tool</button> -->
  </div>
  <div class="dropdown mr-5">
    <!-- Popup component for background media upload -->
    <div class="bgupload">
      <backgroung-media-upload-toaster></backgroung-media-upload-toaster>
    </div>
    <div class="updates-wrapper">
  
    
    <button type="button" class="btn btn-primary audit-tool" (click)="loginToERC()" >Property Audit Tool</button>
  

    <!-- Example Bootstrap button with a click event -->
    <a href="javascript:;"  class="navbar-brand navbar-brand-express advance-search" (click)="advanceSearch()" [style.display]="showAdvancedSearchLabel ? 'block' : 'none'">Advanced Search</a>
    <div class="whats-new-button" (click)="openUpdatesPopup()" title="New Updates">
      <i class="fas fa-bell bell"></i>
    </div>
    <a href="javascript:;" class="dropdown-toggle" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true"
      aria-expanded="false">
      <img class="navbar-user mr-2" *ngIf="!!UserPhoto" src="{{UserPhoto}}" alt="" />
      <i class="fa fa-user-circle-o mr-2" style="font-size: 1.8rem;" *ngIf="!UserPhoto" aria-hidden="true"></i>
      <span class="d-none d-md-inline userName">{{UserName}}</span> <b class="caret"></b>
    </a>
    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
      <a href="javascript:;" class="dropdown-item" (click)="logout()"><i class="fas fa-sign-out-alt"></i> Log Out</a>
    </div>
  </div>
  </div>
</div>
<div *ngIf="showWhatsNew">
      <app-whats-new-modal
      [updates]="updates" 
      [version]="version"
      [releaseNoteLink]="releaseNoteLink"
      (handleOnClickWhatsNew)="onCloseWhatsNew()">
    </app-whats-new-modal>
</div>
