div.desc {
  padding: 15px;
  width: 100%;
  background: #fff;
  color: #000;
  width: 100%;
}
.header.navbar-default {
    background: #fff;
    border-bottom: 1px solid var(--primary-blue-color);
}
.header img.navbar-user  {
    width: 30px;
    margin: -5px 10px -5px 0;
    border-radius: 30px;
    height: 30px;
    float: left;
}
.header {
  height: 50px;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-toggle,.dropdown-toggle:hover{
  text-decoration: none;
   color: #585663;
   display: flex;
   align-items: center;
}

.header .userName {
    color: #585663;
}
.header .userName:hover{
  text-decoration: none;
}

.page_sub_head1 {
  font-size: 17px;
  background: var(--primary-white);
  color: var(--primary-blue-color);
  border: 1px solid var(--primary-blue-color);
  padding-top: 5px;
  margin-top: 0 !important;
  width: 100%;
}

.header .navbar-nav>li>.dropdown-menu {
  position: absolute;
}

.header .navbar-brand-log {
  float: left;
  margin-right: 10px;
  padding: 0 20px;
  height: 50px;
  width: 200px;
  font-weight: 100;
  font-size: 18px;
  line-height: 30px;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: left;
}

.formBox {
  background: #f2f3f4;
  width: auto;
}

.header .navbar-form .form-control {
  width: 220px;
  padding: 5px 15px;
  height: 32px;
  background: #f2f3f4;
  border-color: #f2f3f4;
  border-radius: 30px
}

@keyframes expand {
  from {
    width: 220px
  }
  to {
    width: 220px
  }
}

@-webkit-keyframes expand {
  from {
    width: 220px
  }
  to {
    width: 220px
  }
}

.header .navbar-form .form-control:focus {
  width: 220px;
  border-color: #f2f3f4;
  box-shadow: none;
  -webkit-animation: expand .2s;
  animation: expand .2s
}

.header li .navbar-form {
  padding: 0 5px;
  margin: 9px 0;
}

.dropdown-menu-right.dropdown-menu {
  right: 0 !important;
  left: auto !important;
}

.header .navbar-brand-express {
  float: left;
  margin-right: 30px;
  padding: 0 20px;
  width: 220px;
  font-weight: 300;
  font-size: 18px;
  line-height: 30px;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: left;
}

.bgupload {
  position: absolute;
  right: 26rem;
  width: 64%;
}

.advance-search {
  width: 136px !important;
}


.navBar-button {
  float: left !important;
  margin-top: 2px !important;
  margin-right: 10px;
}
a {
  cursor: pointer;
  color: #007bff;
}
a:hover {
  color: #0056b3;
}


.updates-wrapper{
  display: flex;
  flex-direction: row;
  align-items: center;
  height:50px;
  gap:20px;
}

.audit-tool{
  height: 30px;
}

.whats-new-button {
  background: #fff;
  border: 1px solid var(--primary-blue-color);
  border-radius: 50%;
  position: relative;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: transform 0.2s, background-color 0.3s, border-color 0.3s;
}

.whats-new-button .bell {
  color: var(--primary-blue-color);
  padding-left: 3px;
  animation-name: bell-anim, bell-growth;
  animation-duration: 2s, 1s;
  animation-iteration-count: infinite;
}

.whats-new-button:hover {
  transform: scale(1.1);
  background: var(--primary-blue-color);
  border-color: #fff;
}

.whats-new-button:hover .bell {
  color: #fff;
}

@keyframes bell-anim {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
  }
  50% {
    transform: translate(-100%, -50%) rotate(45deg) scale(1.2);
    animation-timing-function: ease-out;
  }
  100% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
    animation-timing-function: ease-in-out;
  }
}

@keyframes bell-growth {
  0% { transform: translate(-10%, -10%) scale(1); }
  50% { transform: translate(-10%, -10%) scale(1.2); }
  100% { transform: translate(-10%, -10%) scale(1); }
}
