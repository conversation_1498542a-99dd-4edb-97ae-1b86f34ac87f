import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { LoginService } from '../../services/login.service';
import { WhatsNewService } from '../../services/whats-new-service';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { MapService } from '../../modules/map-module/service/map-service.service';
import { MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { SSODetails } from '../../models/SSODetails';
import { EnumApplication } from '../../enumerations/application';
declare var google: any;
var CryptoJS = require("crypto-js");
@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit {
  @Input()
  hideModeLabel = false;
  public UserName: string = null;
  UserPhoto: any = null;
  showWhatsNew = false;
  updates: any[] = [];
  version: string = '';
  releaseNoteLink: string = '';
  mediaUrl = `${environment.MediaS3DynamicImageBase}` + `${environment.MediaS3Path}` + `/Thumbnail/30x30/`;
  @Input()
  showAdvancedSearchLabel:boolean;
  private _mapService: MapService;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  constructor(mapService: MapService,private router: Router, private _loginService: LoginService, private whatsNewService: WhatsNewService) {
    this._mapService = mapService;
  }

  
  SSODetails: SSODetails;

  loginToERC() {
      this.SSODetails = new SSODetails();
      this.SSODetails.RequestedUserID = this._loginService.UserInfo.EntityID;
      this.SSODetails.ImpersonateUserID = this._loginService.UserInfo.EntityID;
      this.SSODetails.IsActive = true;
      this.SSODetails.TargetApplicationID = EnumApplication.ERC;
      this.SSODetails.SourceApplicationID = EnumApplication.VST;
      this.SSODetails.LoginID = null;
      const response_save = this._loginService.saveLoginSSODetails(this.SSODetails);
      response_save.subscribe(result => {
        if (!result.body.error) {
          const resultData = result.body.responseData;
          if (!!resultData) {
            window.open(environment.ERCReviewTool + '/#/pages/login/' + resultData.SSODetails.Token, '_blank');
          }
        }
      });
  }

  ngOnInit() {

    var loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
   // var LoggedInUserData = JSON.parse(sessionStorage.getItem('LoggedInUserData'));
    if (loginData != "" && !!loginData) {
      var bytes = CryptoJS.AES.decrypt(loginData.toString(), environment.EncryptionKey);
      var loggedinData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));

      if (loggedinData) {
        this._loginService.UserInfo.IsLoggedin = true;
        this._loginService.UserInfo.CountryId = loggedinData.CountryId;
        this._loginService.UserInfo.EntityID = loggedinData.EntityID;
        this._loginService.UserInfo.PersonName = loggedinData.PersonName;
        this._loginService.UserInfo.MetroCentroidLat = loggedinData.MetroCentroidLat;
        this._loginService.UserInfo.MetroCentroidLong = loggedinData.MetroCentroidLong;

        this._loginService.UserInfo.UnitID = loggedinData.UnitID;
        this._loginService.UserInfo.DateFormat = loggedinData.DateFormat;
        this._loginService.UserInfo.MainPhotoUrl = loggedinData.MainPhotoUrl;  
        this._loginService.UserInfo.UnitDisplayTextSize =loggedinData.UnitDisplayTextSize;
        this._loginService.UserInfo.RoleID = loggedinData.RoleID;
        this._loginService.UserInfo.RoleName = loggedinData.RoleName;
        this.UserName = loggedinData.PersonName;
        this.UserPhoto = loggedinData.MainPhotoUrl ? this.mediaUrl + loggedinData.MainPhotoUrl : null;
      } else {
        this.router.navigate(['/login']);
      }
    } else {
      this.router.navigate(['/login']);
    }
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.fetchWhatsNewUpdates();
  }

  logout() {
    sessionStorage.clear();
    const metaDataKeysToDelete = [
      MetaDataCollectionKeys.PropertyList,
      MetaDataCollectionKeys.PropertyTypeList,
      MetaDataCollectionKeys.ResearchStatusesList,
      MetaDataCollectionKeys.StateList,
      MetaDataCollectionKeys.MultiFloors
    ];
    metaDataKeysToDelete.forEach(key => {
      this.metaDataIndexedDBService.deleteDataFromMetaData(key);
    });
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
    this.router.navigate(['/login']);
  }

  advanceSearch(){
    this.router.navigate(['/search']);
  }

  fetchWhatsNewUpdates(): void {
    this.whatsNewService.getReleaseUpdateNotes()
      .subscribe({
        next: (response: any) => {
          const data = response.body;
          if (data?.updates?.updates?.length) {
            this.updates = data.updates.updates.map((update: any) => ({
              media: {
                image: update.resource && /\.(jpeg|jpg|png|gif|bmp)$/i.test(update.resource) ? update.resource : null,
                video: update.resource && /\.(mp4|webm|ogg|avi|mov)$/i.test(update.resource) ? update.resource : null,
              },
              title: update.title || '',
              description: update.description || '',
              type: update.type || '',
            }));
          } else {
            this.updates = [];
          }
          this.version = data?.version || '';
          this.releaseNoteLink = data?.releaseNoteLink || '';
        },
        error: (error) => {
          console.error('Failed to load updates:', error);
        }
      });
  }

  openUpdatesPopup(){
    this.showWhatsNew = true;
    document.body.style.overflow = 'hidden';
  }
  
  onCloseWhatsNew(){
    this.showWhatsNew = false;
    document.body.style.overflow = 'auto';
  }
  
}
