import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ResearchStatusHistoryComponent } from './research-status-history.component';

describe('ResearchStatusHistoryComponent', () => {
  let component: ResearchStatusHistoryComponent;
  let fixture: ComponentFixture<ResearchStatusHistoryComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ResearchStatusHistoryComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ResearchStatusHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
