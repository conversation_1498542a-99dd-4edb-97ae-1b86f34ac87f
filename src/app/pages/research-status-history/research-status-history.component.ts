import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { Property } from '../../models/Property';
import { PropertyResearchStatus } from '../../models/PropertyResearchStatus';
import { SharedDataService } from '../../services/shareddata.service';
import { PropertyService } from '../../services/api-property.service';
import { Subscription } from 'rxjs';
import { CommunicationService } from '../../services/communication.service';

@Component({
  selector: 'app-research-status-history',
  templateUrl: './research-status-history.component.html',
  styleUrls: ['./research-status-history.component.css']
})
export class ResearchStatusHistoryComponent implements OnChanges {

  selectedOption: any;
  @Input() property: Property;
  @Input() parentId: any;
  @Input() isResearchClicked: boolean = false;
  researchStatusHistoryListener: Subscription;
  isResearchStatusHistory: boolean = false;
  propertyResearchStatus: Array<PropertyResearchStatus>;

  constructor(  private _propertyService: PropertyService, private _sharedDataService: SharedDataService, private communicationService: CommunicationService) {
      this.researchStatusHistoryListener = this.communicationService.subscribe('fetchResearchStatus')?.subscribe(result => {
        this.showPropertyResearchStatus(this.parentId);
      }); 
      this.researchStatusHistoryListener = this.communicationService.subscribe('fetchChangeLog').subscribe(result => {
        this.parentId = result?.data
        this.showPropertyResearchStatus(this.parentId);
      }); 
    }

  ngOnChanges() {
    this.showPropertyResearchStatus(this.property?.PropertyID);
  }

  ngOnDestroy(){
    this.researchStatusHistoryListener.unsubscribe;
  }

  showPropertyResearchStatus(propertyId) {
    if (propertyId > 0) {
      this.propertyResearchStatus = this._sharedDataService.researchStatusList || [];
      const response_researchStatus = this._propertyService.getPropertyResearchStatus(propertyId);
      response_researchStatus.subscribe(result => {
        if (!result.body.error) {
          let propResearchStatus = result && result.body && result.body.responseData && result.body.responseData[0] || [];
          if (this.propertyResearchStatus) {
            this.propertyResearchStatus.forEach(status => {
              status.IsActive = false;
              status.ModifiedDate = "";
              status.ModifiedPersonName = "";
            });
          }
          propResearchStatus.forEach(value => {
            this.propertyResearchStatus.forEach(emp => {
              if (emp.PropertyResearchTypeID == value.PropertyResearchTypeID) {
                emp.IsActive = true;
                emp.ModifiedBy = value.ModifiedBy;
                emp.ModifiedPersonName = value.ModifiedPersonName;
                emp.ModifiedDate = value.ModifiedDate;
                emp.PropertyResearchStatusID = value.PropertyResearchStatusID;
              }
            });
          });
        }
        this._sharedDataService.propertyResearchStatus = this.propertyResearchStatus;
        this._sharedDataService.propertyResearchStatus.forEach(element => {
          element.PropertyID = propertyId;
        });
        const dataArray = this.propertyResearchStatus?.filter(research => research.IsActive === true)
        const sortedArray = dataArray?.sort((a, b) => {
          const dateA = new Date(a.ModifiedDate).getTime();
          const dateB = new Date(b.ModifiedDate).getTime();
          return dateA - dateB;
        });
        if (sortedArray && sortedArray.length > 0 && sortedArray?.slice(-1)[0]) {
          this.selectedOption = sortedArray?.slice(-1)[0];
        }
      })
    }
  }
}
