<div class="review-details-wrapper">
    <div *ngIf="selectedOption">Last Modified 
        By<b>&nbsp;{{selectedOption?.ModifiedPersonName}}&nbsp;</b>on<b>&nbsp;{{selectedOption?.ModifiedDate
            | date:dateFormat}}&nbsp;</b></div>
</div>

<div class="form-group row">
    <div class="col-md-12">
        <div class="row">
            <div *ngFor="let research of propertyResearchStatus" class="col-md-12 repeat-box">
                <div *ngIf="research && research.PropertyResearchTypeID && research.PropertyResearchTypeID!=1"
                    class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-md-1">
                                        <img [src]="research.ResearchStatusPin">
                                    </div>
                                    <div class="col-md-10">
                                        {{research.PropertyResearchTypeName}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <ui-switch [checked]="research.IsActive" [disabled]="true"></ui-switch>
                            </div>
                            <div class="col-md-4">
                                <span class="modified-date" *ngIf="research.ModifiedBy">{{research.ModifiedDate | date:dateFormat :"+0000"}}
                                    &nbsp;
                                    <span class="modified-person-name" *ngIf="research.ModifiedPersonName">({{research.ModifiedPersonName}})</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
