import { Component, Input, OnInit, Output, EventEmitter } from "@angular/core";
import { Property } from "../../models/Property";
import { PropertyService } from "../../services/api-property.service";
import { SaleComp } from "../../models/SaleComp";
import { confirmConfiguration } from "../../modules/notification/models/confirmConfiguration";
import { CommonStrings, MeasurementTypes } from "../../constants";
import { NotificationService } from "../../modules/notification/service/notification.service";
import { ISaleAutoEnrichRequest } from "../../models/saleAutoEnrichRequest";
declare var $: any;

@Component({
  selector: "app-update-sold-sqm",
  templateUrl: "./update-sold-sqm.component.html",
  styleUrls: ["./update-sold-sqm.component.css"],
})
export class UpdateSoldSqmComponent implements OnInit {
  @Input() property: Property;
  @Input() sales: SaleComp[] = [];
  @Input() displayPropertyId: number;
  @Output() close = new EventEmitter<void>();
  @Output() continue = new EventEmitter<any[]>();
  selectedSales: SaleComp[] = [];
  public saleLoading = false;
  public saleTransactions: Array<SaleComp>;
  public totalSaleRecords: number;
  public measurementTypes = MeasurementTypes;

  soldSqm: number;

  constructor(
    private propertyService: PropertyService,
    private _notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    console.log("property", this.property);
    if (this.sales && this.sales.length > 0) {
      this.processSales(this.sales);
      this.saleLoading = false;
      this.checkIfUpdateNeeded();
    } else {
      this.getSaleTransactions();
    }
  }

  private checkIfUpdateNeeded(): void {
    // If no sales need updating, automatically continue without showing the modal
    if (this.selectedSales.length === 0) {
      setTimeout(() => {
        this.continue.emit([]);
        this.close.emit(); // Close the modal since no updates are needed
      }, 100);
    }
  }

  onContinue() {
    this.continue.emit(this.selectedSales);
  }

  onClose() {
    this.close.emit();
  }

  private processSales(sales: SaleComp[]) {
    this.saleTransactions = sales.filter((sale: SaleComp) => sale.IsPortfolioSale === 0);
    this.sales = this.saleTransactions;
    this.totalSaleRecords = this.saleTransactions.length;
    this.selectedSales = this.saleTransactions.filter(sale => {
      const referenceValue = this.property.HasNoBuildingFootprints > 0
        ? this.property.ContributedGBA_SF
        : this.property.BuildingSF;
      return sale.SoldSM !== referenceValue;
    });
  }

  getSaleTransactions() {
    const data = {
      RelationID: this.displayPropertyId,
      RelationType: "Property",
    };
    this.saleLoading = true;
    const response_saleComp = this.propertyService.GetSaleTransactions(data);
    response_saleComp.subscribe({
      next: (result) => {
        this.processSales(result.body.responseData);
        this.saleLoading = false;
        console.log("sales", this.saleTransactions);
        this.checkIfUpdateNeeded();
      },
      error: (err) => {
        this.saleLoading = false;
        // If there's an error fetching sales, continue with save
        this.continue.emit([]);
        this.close.emit();
      },
    });
  }

  cancelConfirmationPopup() {
    const configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message =
      CommonStrings.DialogConfigurations.Messages.CancelUpdatingSoldSQMConfirmationMessage;
    configuration.Title =
      CommonStrings.DialogConfigurations.Title.CancelUpdatingSoldSQM;
    configuration.OkButton.Label =
      CommonStrings.DialogConfigurations.ButtonLabels.Ok;
    configuration.OkButton.Callback = () => {
      this.onClose();
    };
    configuration.CancelButton.Label =
      CommonStrings.DialogConfigurations.ButtonLabels.No;
    configuration.CancelButton.Callback = () => {};

    this._notificationService.CustomDialog(configuration);
  }

  onUpdateEnrichment() {
    // Ensure selectedSales has valid SaleIDs
    const saleIds = this.selectedSales
      .filter(sale => sale.SaleID != null) // Ensure SaleID exists
      .map(sale => sale.SaleID);

    if (saleIds.length === 0) {
      this._notificationService.ShowErrorMessage(
        "No valid sales selected for enrichment."
      );
      return;
    }

    const requestPayLoad: ISaleAutoEnrichRequest = {
      SaleIDs: saleIds.join(","),
      FieldNames: "SoldSF",
      PropertyID: this.displayPropertyId, // Use displayPropertyId from @Input
    };

    const responseAutoEnrich = this.propertyService.saleAutoEnrich(requestPayLoad);
    responseAutoEnrich.subscribe({
      next: (result) => {
        if (!result?.body?.error) {
          const responseData = result?.body?.responseData;
          this._notificationService.ShowSuccessMessage(
            responseData?.[0]?.ResultMessage ||
              CommonStrings.SuccessMessages.SaleAutoEnrichmentSuccess ||
              "Sale enrichment completed successfully."
          );
          this.continue.emit(this.selectedSales); // Use continue instead of afterEnrich
          this.close.emit(); // Use close instead of cancel
        } else {
          const errorMessage =
            result?.body?.message ||
            CommonStrings.ErrorMessages.SaleAutoEnrichmentFail ||
            "Failed to enrich sales.";
          console.error(errorMessage);
          this._notificationService.ShowErrorMessage(errorMessage);
        }
      },
      error: (err) => {
        const errorMessage =
          err?.error?.message ||
          CommonStrings.ErrorMessages.SaleAutoEnrichmentFail ||
          "An error occurred during sale enrichment.";
        console.error(errorMessage);
        this._notificationService.ShowErrorMessage(errorMessage);
      },
    });
  }
}