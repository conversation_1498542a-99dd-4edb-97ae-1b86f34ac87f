<!-- <div class="app flex-row login-bg" style="overflow-y:hidden;">
  <div class="col-md-12 no-padding">
   <form #myForm="ngForm" name="loginForm" (ngSubmit)="login(myForm)">
      <div class="login-log">
          <img src="assets/images/logo1.png">
      </div>

      <div class="col-md-12">
          <div class="row justify-content-center">
              <div class="col-md-4" style="padding-top:50px;">
                  <div class="card-group">
                      <div class="card"> 
                          <div class="card-header text-center logo-color">
                              <h5><strong>Virtual Site Tool - Login</strong></h5>
                          </div>
                           
                          <div class="card-body">
                            <div class="col-md-12 margin-top-20">
                            <div *ngIf="invalidLogin" style="color:red;">{{errorMessage}}</div>
                        </div>
                              <div class="col-md-12 margin-top-20">
                                  <p class="text-muted">Sign In to your account</p>
                                    <div *ngIf="!(myForm.value.username) && validationError" style="color:red">Username is required</div>
                                  <div class="input-group mb-3">
                                      <span class="input-group-addon"><i class="icon-user"></i></span>
                                       <input type="text" class="form-control" ngModel required placeholder="Username" name="username">  
                                     
                                  </div>
                                    <div *ngIf="!(myForm.value.password) && validationError" style="color:red">Password is required</div>
                                  <div class="input-group mb-4">                                    
                                      <span class="input-group-addon"><i class="icon-lock"></i></span>
                                       <input type="Password" class="form-control" ngModel required placeholder="Password" name="password">
                                     
                                  </div>
                                  <div class="row">
                                      <div class="col-6">
                                          <input type="submit" value="Login" class="btn btn-warn px-4" />
                                      </div>
                                      <div class="col-6 text-right">
                                          <button type="button" class="btn btn-link px-0">Forgot password?</button>
                                      </div>
                                      <div class="row">
                                        <div *ngIf="invalidLogin" style="color:red">Invalid Login</div>
                                    </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
   </form>
  </div>
</div> -->

<div class="login login-with-news-feed" *ngIf="showLoginPage">
  <div class="news-feed">
      <div class="news-image" style="background-image: url(assets/images/header-bg.jpg)"></div>
      <div class="news-caption">
          <h4 class="caption-title"><b>Arealytics Virtual Site Tool</b></h4>
          <p>
            Advanced Real Estate Analytics
          </p>
      </div>
  </div>
  <form #myForm="ngForm" name="loginForm" (ngSubmit)="login(myForm)">
    <div class="right-content">
        <!-- <div class="login-header">
            <div class="brand">
                <img src="assets/images/primaryLogo-in.png" class="img-fluid" alt="logo" title="Arealytics">
            </div>
        </div> -->
        <div class="login-header">
          <div class="brand position-relative">
              <div class="position-absolute empiricalMsgWrap">
                  <label class="empiricalText"><b>Empirical CRE</b></label><span> is now</span>
              </div>
              <img src="assets/images/primaryLogo-in.png" class="img-fluid" alt="logo" title="Arealytics">
          </div>
        </div>
        <div class="col-md-12 margin-top-20 login-content">
          <p class="text-muted">Sign In to your account</p>
            <div *ngIf="!(myForm.value.username) && validationError" style="color:red">Username is required</div>
          <div class="input-group mb-3">
              <span class="input-group-addon"><i class="icon-user"></i></span>
              <input type="text" class="form-control" ngModel required placeholder="Username" name="username">  
            
          </div>
            <div *ngIf="!(myForm.value.password) && validationError" style="color:red">Password is required</div>
          <div class="input-group mb-4">                                    
              <span class="input-group-addon"><i class="icon-lock"></i></span>
              <input type="Password" class="form-control" ngModel required placeholder="Password" name="password">
            
          </div>
          <div class="row">
              <div class="col-12">
                  <input type="submit" value="Login" class="btn btn-login px-4 form-control" />
              </div>
              <!-- <div class="col-6 text-right">
                  <button type="button" class="btn btn-link px-0">Forgot password?</button>
              </div> -->
              <div class="row">
                <div *ngIf="invalidLogin" class="pt-1" style="color:red;padding-left: 30px;">Invalid Login</div>
            </div>
          </div>
      </div>
    </div>
  </form>
</div>