import { Component, OnInit } from '@angular/core';
import { login } from '../../models/login';
import { LoginService } from '../../services/login.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { environment } from '../../../environments/environment';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { CommunicationModel, CommunicationService } from '../../services/communication.service';
import { EnumApplication } from '../../enumerations/application';
const CryptoJS = require('crypto-js');

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {


  _router: Router;
  validationError: boolean = false;
  invalidLogin: boolean = false;
  CountryId: number;
  errorMessage:string;
  SSOToken: string = null;
  propertyId: any;
  address: string = null;
  
  public loginDetails: login;
  public showLoginPage = true;
  public invalidMessage = '';
  sourceApplicationId: any;

  constructor(
    private _loginService: LoginService,
    private activatedRouter: ActivatedRoute,
    private _CommService: CommunicationService,
    private router: Router
  ) { }
  
  ngOnInit() { }

  login(loginForm: NgForm) {

    this.invalidLogin = false;
    this.validationError = false;
    if (loginForm.valid) {
      let userdata = {
        username: loginForm.value.username,
        password: loginForm.value.password
      };
      this.processLogin(userdata);
    }
    else {
      this.validationError = true;
    }
  }

  logout() {
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }

  private processLogin(userdata: any) {

    this.invalidLogin = false;
    const response_userLogin = this._loginService.login(userdata);
    response_userLogin.subscribe(result => {
      let data = result.body;

      if(!data.error && data.responseData.Response[0].ResponseCode==1){
        this._loginService.UserInfo.IsLoggedin = true;
        this._loginService.UserInfo.DateFormat = data.responseData.UserInfo[0].DateFormat;
        this._loginService.UserInfo.CountryId = data.responseData.UserInfo[0].CountryID;
        this._loginService.UserInfo.EntityID = data.responseData.UserInfo[0].EntityID;
        this._loginService.UserInfo.PersonName = data.responseData.UserInfo[0].PersonName;
        this._loginService.UserInfo.UnitID = data.responseData.UserInfo[0].UnitId;
        this._loginService.UserInfo.UnitDisplayTextSize = data.responseData.UserInfo[0].UnitDisplayTextSize;        
        this._loginService.UserInfo.MetroCentroidLat = data.responseData.UserInfo[0].MetroCentroidLat || -37.814;
        this._loginService.UserInfo.MetroCentroidLong = data.responseData.UserInfo[0].MetroCentroidLong ||  144.96332;
        this._loginService.UserInfo.MainPhotoUrl = data.responseData.UserInfo[0].MainPhotoUrl;
        this._loginService.UserInfo.RoleID = data.responseData.UserInfo[0].RoleID;
        this._loginService.UserInfo.RoleName = data.responseData.UserInfo[0].RoleName;
        
        sessionStorage.setItem(SessionStorageKeys.AccessToken, data.responseData.Token);

        var CryptoJS = require("crypto-js");
        var encryptedData = CryptoJS.AES.encrypt(JSON.stringify(this._loginService.UserInfo), environment.EncryptionKey);      
        sessionStorage.setItem(SessionStorageKeys.LogInData, encryptedData);
        this.router.navigate(['/search']);
      }
      else {
        this.errorMessage= !!data.responseData ? data.responseData.Response[0].ErrorMessage : !!data ? data : "Invalid Login";
        this.invalidLogin = true;
      }
    });
  }


}
