<div class="page-head">
    <!-- <h4>Notes @ABC Real Estate (Company Level)</h4> -->
    <div class="row" style="margin-right: 0px;">
        <div class="col-md-12">
            <div class="row" style="padding: 5px 15px 5px 15px; background-color: white">
                <div class="col-md-4" *ngFor="let note of notesList" style="padding: 10px;">
                    <div class="notes-cards card">
                        <div class="card-body p-2">
                            <h4>{{note.NoteTitle}} </h4>
                            <p>
                                {{note.NoteDescription}}
                            </p>
                        </div>
                        <div class="card-footer p-2">
                            <div class="row">
                                <div class="col-md-12">
                                    <p><span class="dataKey"> Created on : </span> <span
                                            class="dataLabelValue">{{note.CreatedDate | date: 'dd/MM/yyyy'}} </span></p>
                                    <p><span class="dataKey">Created By : </span> <span
                                            class="dataLabelValue">{{note.CreatedByName}}</span> </p>
                                </div>
                                <div class="icon-wrapper-notes col-md-12">
                                    <div class="pull-right" style="text-align: right">
                                        <i class="fa fa-pencil-square-o cursor-pointer" aria-hidden="true"
                                            (click)="editNote(note)"></i>
                                        <i class="fa fa-trash cursor-pointer" aria-hidden="true" (click)="deleteNote(note)"></i>
                                        <!-- <button class="btn" class="btn-icon-fill">
                                            <i class="fa fa-edit" (click)="editNote(note)"></i>
                                        </button> -->

                                        <!-- <button class="btn" class="btn-icon-fill">
                                            <i class="fa fa-trash" style="padding:2px" (click)="deleteNote(note)"></i>
                                        </button> -->
                                        <!-- <img class="btn" class="btn-icon-fill" src="assets/images/edit_icon.png"
                                            height="30" width="30" (click)="editNote(note)">
                                        <img class="btn" class="btn-icon-fill" src="assets/images/delete_icon.png"
                                            height="30" width="30" (click)="deleteNote(note)"> -->
                                    </div>
                                </div>
                                <!-- <div class="col-md-4">
                              <button class="btn btn-warning save-note" type="button">Save Note</button>
                          </div> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="buttons loader-btn add-note-link">
                        <a (click)="addNote()"><img src="assets/images/add_blue.png" height="50" width="50"> </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--***********************Note-Add-modal ************************* -->