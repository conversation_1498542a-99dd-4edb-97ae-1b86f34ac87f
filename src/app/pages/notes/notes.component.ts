import { ParentTable } from './../../enumerations/enums';
import { Component, OnInit, Input, NgZone } from '@angular/core';
import { Notes } from '../../models/notes';
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { LoginService } from '../../services/login.service';
import { NotesService } from '../../services/notes.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
import { Subscription } from 'rxjs';
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
import { CommonStrings } from '../../constants';

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss']
})
export class NotesComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() selectedNote:Notes;
  note: Notes;
  notesList: Notes[] = [];

  showNoteModal: boolean = false;
  eventSubscription: Subscription;

  constructor(private _notesService: NotesService
    , private _loginService: LoginService
    ,  private communicationService: CommunicationService
    , private notificationService: NotificationService
    , private zone: NgZone) {
      this.eventSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
        this.zone.run(() => {
          this.initialDetails = result.data;
          this.note.ParentID = this.initialDetails.propertyId;
          this.note.ParentTableID  = ParentTable.Property;
          this.getNotes();
        });
      });

      const rest = this.communicationService.subscribe('onNoteClose').subscribe(result => {
        this.getNotes();
      });

     }

  ngOnInit() {
    this.note = new Notes();
  this.note.ParentID = this.initialDetails.propertyId;
  this.note.ParentTableID  = ParentTable.Property;
  this.getNotes();

  }

  getNotes() {
    const response_notes = this._notesService.GetNotes(this.note);
    response_notes.subscribe(result => {
      if (!result.body.error) {
        this.notesList = result.body.responseData;
      }
    });
  }

  addNote() {
    this.selectedNote = new Notes();
    this.selectedNote.ParentID = this.note.ParentID;
    this.selectedNote.ParentTableID = this.note.ParentTableID;
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, selectedNote:  this.selectedNote };
    model.Key = 'showNoteModal';
    this.communicationService.broadcast(model);

  }

  editNote(note) {
    this.selectedNote = note;
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, selectedNote:  this.selectedNote };
    model.Key = 'showNoteModal';
    this.communicationService.broadcast(model);

  }

  deleteNote(note) {


    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteNoteConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
    note.LoginEntityID = this._loginService.UserInfo.EntityID;
      const response_deleteNote = this._notesService.DeleteNote(note);
      response_deleteNote.subscribe(result => {
        if (!result.body.error) {
          this.getNotes();
        }
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {

    }

    this.notificationService.CustomDialog(configuration);
  }

  closeNoteModal() {
    this.getNotes();
  }

}
