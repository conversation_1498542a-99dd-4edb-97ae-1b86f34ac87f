<div class="col-lg-12 no-padding margin-top-20">
  <button (click)="addStrata(true)" class="btn btn-sm btn-primary pull-right add-strata-btn multi-strata-btn" *ngIf="showAddStrataBtn">
    <i class="fa fa-plus"></i> Add Multi Strata
  </button>
  <button (click)="addStrata()" class="btn btn-sm btn-primary pull-right add-strata-btn" *ngIf="showAddStrataBtn">
    <i class="fa fa-plus"></i> Add Strata
  </button>
  <div class="table-responsive mt-2">
    <p-table #strataTable [columns]="strataTableHeader" [value]="propertyStrataList" [scrollable]="true"
      [scrollHeight]="!propertiesLoading ? getScrollHeight() : '25vh'" [loading]="propertiesLoading">
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th *ngFor="let col of columns" [style.max-width]="getColumnWidth(col.header)">
            {{col.header}}
            <div class="arrow-group" *ngIf="col.header != 'Type'">
              <i class="fa fa-sort-up" (click)="sortProperty(col.field,'Ascending')"></i>
              <i class="fa fa-sort-down" (click)="sortProperty(col.field,'Descending')"></i>
          </div>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-index="rowIndex" let-columns="columns">
        <tr [pSelectableRow]="rowData" [ngClass]="{'highlight': rowData && rowData.PropertyID == propertyId}" *ngIf="rowData">
          <td *ngFor="let col of columns" [style.max-width]="getColumnWidth(col.header)">
            <span *ngIf="col.header==='Address'"><a class="prop-name" (click)="showPropertySummary(rowData)">{{rowData[col.field]}}</a></span>
            <span *ngIf="col.header == 'Type'">{{rowData[col.field]}}
              <span *ngIf="rowData.PropertyID == propertyId">(Viewing)</span>
              <span
                *ngIf="rowData.PropertyID != propertyId && isPropertyVisited(rowData.PropertyID) && !isPropertyEdited(rowData.PropertyID)">
                <i class="fas fa-check-circle fa-check-circle-grey" title="Visited"></i></span>
              <span *ngIf="rowData.PropertyID != propertyId && isPropertyEdited(rowData.PropertyID)"> <i class="fas fa-check-circle"
                  title="Edited"></i></span>
              </span>
              <span *ngIf="col.header === BuildingSizeHeader">{{rowData.sizeValue | number: '.0-0'}} 
                <span *ngIf="rowData.sizeValue" [title]="rowData.measurementTypeTitle" class="circleBox" [ngClass]="rowData.measurementTypeClass">{{rowData.measurementTypeLabel}}</span>
              </span>
            <span *ngIf="col.header==='Lot Size'">{{rowData[col.field] | number: '.2-2'}} </span>
            <span *ngIf="col.header==='PropertyID' || col.header==='Property Name'"><a class="prop-name" (click)="showPropertySummary(rowData)">{{rowData[col.field]}}</a></span>
            <span style="word-break: break-word;"
              *ngIf="col.header!='Address' && col.header!='Type' && col.header!= BuildingSizeHeader && col.header !=='Lot Size' && col.header !=='PropertyID' && col.header !=='Property Name'">{{rowData[col.field]}}</span>
          </td>

        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage" let-columns="columns">
        <tr [style.height.px]="propertiesLoading ? 200 : null">
          <td colspan="15" *ngIf="!propertiesLoading">
            No records found
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
