@import url('../../../assets/css/label-styles.css');
.prop-name {
  color: #20a8d8 !important;
  text-decoration: none !important;
  background-color: transparent !important;
  cursor: pointer;
}

.col-lg-12.p-0 button {
  margin-left: 3px;
}

table {
  font-size: 14px !important;
}


tr:nth-child(even) {
  background-color: #f5f5f5;
}

thead th {
  background: #1e4b7b;
  color: #fff;
  position: relative;
}

.tableRow {
  padding: 10px 0px !important;
  font-size: 13px;
}

tr:nth-child(even) {
  background-color: #f5f5f5;
}

.arrow-group {
  display: inline-block;
  position: absolute;
  right: 0;
}

.arrow-group i {
  cursor: pointer;
  height: 10px;
  bottom: 3px;
}

.arrow-group i.fa.fa-sort-up {
  position: relative;
}

.arrow-group i.fa.fa-sort-down {
  position: relative;
  right: 11px;
  top: 5px;
}
.highlight {
  background-color: lightgoldenrodyellow !important;
}
.fa-check-circle {
  color: green;
  margin-left: 4px;;
}

.fa-check-circle-grey {
  color: #40a2e3 !important;
  margin-left: 4px;
}
.add-strata-btn {
  margin-bottom: 10px;
  background: #4180c3 !important; 
  border-color: #4180c3 !important;
  color: white !important;
}
.multi-strata-btn {
  margin-left: 8px;
}
