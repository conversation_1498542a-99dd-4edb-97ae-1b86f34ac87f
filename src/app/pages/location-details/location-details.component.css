@import url(../../../assets/css/common-styles.css);

.map_pin {
    top: 4%;
    position: absolute;
    left: 35%;
    color: #00b7ff;
    cursor: pointer;
}

.map_pin i {
    color: rgb(255, 217, 0);
}

.map_pin{
    top: 4%;
    position: absolute;
    left: 35%;
    color: #00b7ff;
    cursor: pointer;
}
.map_pin i{
    color: rgb(255, 217, 0);
}

.expand-btn {
    margin-bottom: 15px;
}

.expand-btn i.fa-minus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-plus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-minus {
    display: inline-block;
}

.address-type-label {
    display: flex;
    align-items: center;
    gap: 5px
}


.address-type-text {
    margin-bottom: 0 !important;
}

.input-checkbox{
    margin-left:10px;
}

.minMax, .min, .max{
    padding-right: 0 !important;
}

