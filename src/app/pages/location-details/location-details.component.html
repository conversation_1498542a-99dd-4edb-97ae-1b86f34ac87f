<div class="form-group row expandBox">
  <div class="col-md-12 p-0">
    <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" data-target="#location"
      aria-expanded='false'><i class="fa fa-location-arrow"></i>Location Info
    </button>
    <div [formGroup]="locationDetailsForm" id="location" class="collapse mb-2 mt-2 pl-2 pr-2"
      *ngIf="propertyLocation && property">
      <hr class="mt-0">

      <div class="row">
        <div class="col-md-6">
          <div class="row label-value-wrapper">
            <div class="col-md-5 label address-type-text">Address Type
              <span class="mandatory">*</span>
            </div>
            <div class="col-md-7 row">
              <div class="col-md-6">
                <div class="radio-inline address-type-label">
                  <input class="address-type-label-radio" type="radio" [value]="addressTypeValues.Address"
                    formControlName="AddressType" name="AddressType"
                    (change)="addressTypeChange('AddressType',locationDetailsForm?.get('AddressType')?.value);addressAsPropertyName(false);">
                  Address
                </div>
              </div>
              <div class="col-md-6">
                <div class="radio-inline address-type-label">
                  <input class="address-type-label-radio" type="radio" [value]="addressTypeValues.Intersection"
                    formControlName="AddressType" name="AddressType"
                    (change)="addressTypeChange('AddressType',locationDetailsForm?.get('AddressType')?.value);addressAsPropertyName(false);">
                  Intersection
                </div>
              </div>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Street Number
              <span
                *ngIf="(!locationDetailsForm?.controls['StreetNumberMin']?.valid && !locationDetailsForm?.controls['StreetNumberMax']?.valid)"
                style="color:red;">
                * </span>
            </div>
            <div class="col-md-7 row minMax">
              <div class="col-md-6 min">
                <div class="form-control-label label-margin">Min</div>
                <input type="text" pattern="[0-9]+[A-Za-z]*" formControlName="StreetNumberMin" class="form-control"
                  [(ngModel)]="propertyLocation.StreetNumberMin" (change)="addressAsPropertyName(false)"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['StreetNumberMin']?.valid || streetMinMaxError)}"
                  maxLength="6">
              </div>
              <div class="col-md-6 max">
                <div class="form-control-label label-margin">Max</div>
                <input type="text" pattern="[0-9]+[A-Za-z]*" formControlName="StreetNumberMax" class="form-control"
                  [(ngModel)]="propertyLocation.StreetNumberMax" (change)="addressAsPropertyName(false)"
                  [ngClass]="{'error-field':(streetMinMaxError)}" maxLength="6">
              </div>
              <div class="col-md-12 prop-validator error" *ngIf="(streetMinMaxError)">Min street number cannot be
                greater than max street number</div>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label"> Prefix 1/Prefix 2 </div>
            <div class="col-md-7 row minMax">
              <div class="col-md-6 min">
                <span class="help-block">
                  <ng-select formControlName="StreetPrefix1" [items]="streetPrefixes" [virtualScroll]="true"
                    bindLabel="PrefixName" bindValue="PrefixID" placeholder="--Select--"
                    [(ngModel)]="propertyLocation.StreetPrefix1"
                    (change)="addressAsPropertyName(false); onValueChange('StreetPrefix1',$event,'Prefix')">
                  </ng-select>
                </span>
              </div>
              <div class="col-md-6 max">
                <span class="help-block">
                  <ng-select formControlName="StreetPrefix2" [items]="streetPrefixes" [virtualScroll]="true"
                    bindLabel="PrefixName" bindValue="PrefixID" placeholder="--Select--"
                    [(ngModel)]="propertyLocation.StreetPrefix2"
                    (change)="addressAsPropertyName(false); onValueChange('StreetPrefix2',$event,'Prefix')">
                  </ng-select>
                </span>
              </div>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">
              Street Name
              <span *ngIf="!locationDetailsForm?.controls['AddressStreetName']?.valid" style="color:red;"> * </span>
            </div>
            <div class="col-md-7">
              <span class="help-block">
                <input type="text" class="form-control" formControlName="AddressStreetName"
                  (change)="addressAsPropertyName(false)" [(ngModel)]="propertyLocation.AddressStreetName"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['AddressStreetName']?.valid )}">
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label"> Suffix 1/Suffix 2 </div>
            <div class="col-md-7 row minMax">
              <div class="col-md-6 min">
                <span class="help-block">
                  <ng-select formControlName="streetSuffix1" [items]="streetSufixes" [virtualScroll]="true"
                    bindLabel="Suffix" bindValue="StreetSuffix1" placeholder="--Select--"
                    [(ngModel)]="propertyLocation.StreetSuffix1"
                    (change)="addressAsPropertyName(false); onValueChange('StreetSuffix1',$event,'Suffix')">
                  </ng-select>
                </span>
              </div>
              <div class="col-md-6 max">
                <span class="help-block">
                  <ng-select formControlName="streetSuffix2" [items]="streetSufixes" [virtualScroll]="true"
                    bindLabel="Suffix" bindValue="StreetSuffix1" placeholder="--Select--"
                    [(ngModel)]="propertyLocation.StreetSuffix2"
                    (change)="addressAsPropertyName(false);onValueChange('StreetSuffix2',$event,'Suffix')">
                  </ng-select>
                </span>
              </div>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">City
              <span style="color:red;">*</span>
            </div>
            <div class="col-md-7">
              <span class="help-block">
                <ng-select formControlName="City" [items]="cities" [virtualScroll]="true" bindLabel="CityName"
                  bindValue="CityID" placeholder="--Select--" [(ngModel)]="propertyLocation.City"
                  (change)="onValueChange('CityID',$event,'CityName')"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['City']?.valid && !propertyLocation.City)}">
                </ng-select>
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input"> Council </div>
            <div class="col-md-7">
              <span class="help-block">
                <ng-select formControlName="County" [items]="counties" [virtualScroll]="true" bindLabel="CountyName"
                  bindValue="CouncilID" placeholder="--Select--" [(ngModel)]="propertyLocation.County"
                  (change)="onValueChange('County', $event, 'CountyName')"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['County']?.valid && !propertyLocation.County)}">
                </ng-select>
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">State/ZIP
              <span style="color:red;">*</span>
            </div>
            <div class="col-md-7 row minMax">
              <div class="col-md-8 min">
                <span class="help-block">
                  <ng-select formControlName="State" [items]="states" [virtualScroll]="true" bindLabel="StateName"
                    bindValue="StateID" placeholder="--Select--" [(ngModel)]="propertyLocation.State"
                    (change)="onValueChange('State', $event, 'StateName')"
                    [ngClass]="{'error-field':(!locationDetailsForm?.controls['State']?.valid && !propertyLocation.State)}">
                  </ng-select>
                </span>
              </div>
              <div class="col-md-4 max">
                <input type="text" numericOnly allowNegative="false" allowDecimal="true" maxLength="5"
                  formControlName="Zip" class="form-control" [(ngModel)]="propertyLocation.Zip" name="zipcode"
                  id="zipcode" [ngClass]="{'error-field':(!locationDetailsForm?.controls['Zip']?.valid )}">
              </div>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input"> Country </div>
            <div class="col-md-7">
              <span class="help-block">
                <ng-select formControlName="CountryID" [items]="countries" [virtualScroll]="true"
                  bindLabel="CountryName" bindValue="CountryID" placeholder="--Select--"
                  [(ngModel)]="propertyLocation.CountryID" (change)="onValueChange('CountryID', $event, 'CountryName')"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['CountryID']?.valid && !propertyLocation.CountryID)}">
                </ng-select>
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input"> Part of Center/Complex </div>
            <div class="col-md-7">
              <span class="help-block">
                <ng-select formControlName="PartOfComplex" [items]="complexTypes" [virtualScroll]="true"
                  bindLabel="ComplexTypeName" bindValue="ComplexTypeID" placeholder="--Select--"
                  [(ngModel)]="propertyLocation.PartOfComplex"
                  (change)="onValueChange('PartOfComplex', $event, 'ComplexTypeName')"
                  [ngClass]="{'error-field':(!locationDetailsForm?.controls['PartOfComplex']?.valid)}">
                </ng-select>
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <label class="col-md-5 label" for="text-input">Park/Complex
              <span style="color:red;" *ngIf="propertyLocation.PartOfComplex==complexTypeId">*</span>
            </label>
            <div class="col-md-7">
              <input type="text" class="form-control" formControlName="Complex" [(ngModel)]="propertyLocation.Complex"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['Complex']?.valid)}">
            </div>
          </div>

          <div class="row label-value-wrapper">
            <label class="col-md-5 label" for="text-input">Building #
              <span *ngIf="!locationDetailsForm?.controls['BuildingNumber']?.valid" style="color:red;"> * </span>
            </label>
            <div class="col-md-7">
              <input type="text" maxlength="45" class="form-control" formControlName="BuildingNumber"
                [(ngModel)]="propertyLocation.BuildingNumber"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['BuildingNumber']?.valid )}">
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Property Name
              <span style="color:red;">*</span>
            </div>
            <div class="col-md-7">
              <input type="text" class="form-control" formControlName="PropertyName" [(ngModel)]="propertyLocation.PropertyName"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['PropertyName']?.valid)}">
            </div>
          </div>
          
          <div class="row label-value-wrapper">
            <div class="col-md-5 label">Use Address as Property Name</div>
            <div class="col-md-7">
              <input type="checkbox" formControlName="UseAddressAsPropertyName" class="label-margin"
                [(ngModel)]="propertyLocation.UseAddressAsPropertyName"
                (change)="onValueChange('UseAddressAsPropertyName',$event?.target?.checked,'');addressAsPropertyName(true)">
            </div>
          </div>
          
          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Latitude<span style="color:red;">*</span></div>
            <div class="col-md-7">
              <input type="text" readonly formControlName="Latitude" class="form-control" [(ngModel)]="propertyLocation.Latitude"
                value="{{propertyLocation.Latitude}}"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['Latitude']?.valid )}">
            </div>
            <label *ngIf="false" class="map_pin"><span (click)="onClickChangeMapLocation()"><i class="fa fa-map-marker"
                  aria-hidden="true"></i>
                Change Map Pin Location</span></label>
          </div>
          
          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Longitude<span style="color:red;">*</span></div>
            <div class="col-md-7">
              <input type="text" readonly formControlName="Longitude" class="form-control"
                [(ngModel)]="propertyLocation.Longitude" value="{{propertyLocation.Longitude}}"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['Longitude']?.valid )}">
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Rooftop Geo/Source</div>
            <div class="col-md-7">
              <span class="help-block">
                <ng-select formControlName="RooftopSourceID" [items]="roofTopSource" [virtualScroll]="true"
                  bindLabel="RoofTopSource" bindValue="RoofTopSourceID" placeholder="--Select--"
                  [(ngModel)]="propertyLocation.RooftopSourceID"
                  (change)="onValueChange('RooftopSourceID', $event, 'RoofTopSource')">
                </ng-select>
              </span>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Quadrant
              <span *ngIf="!locationDetailsForm?.controls['Quadrant']?.valid" style="color:red;"> * </span>
            </div>
            <div class="col-md-7">
              <ng-select formControlName="Quadrant" [items]="quadrants" [virtualScroll]="true" bindLabel="QuadrantName"
                bindValue="QuadrantID" placeholder="--Select--" [(ngModel)]="propertyLocation.Quadrant"
                (change)="onValueChange('Quadrant',$event,'QuadrantName');addressAsPropertyName(false)"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['Quadrant']?.valid)}"></ng-select>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">East/West St
              <span *ngIf="!locationDetailsForm?.controls['EastWestStreet']?.valid" style="color:red;"> * </span>
            </div>
            <div class="col-md-7">
              <input type="text" class="form-control" formControlName="EastWestStreet" maxLength="100"
                [(ngModel)]="propertyLocation.EastWestStreet" (change)="addressAsPropertyName(false)"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['EastWestStreet']?.valid )}">
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">North/South St
              <span *ngIf="!locationDetailsForm?.controls['NorthSouthStreet']?.valid" style="color:red;"> * </span>
            </div>
            <div class="col-md-7">
              <input type="text" class="form-control" formControlName="NorthSouthStreet" maxLength="100"
                [(ngModel)]="propertyLocation.NorthSouthStreet" (change)="addressAsPropertyName(false)"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['NorthSouthStreet']?.valid )}">
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Market</div>
            <div class="col-md-7">
              <ng-select formControlName="MarketId" [items]="MarketList" [virtualScroll]="true" bindLabel="MarketName"
                bindValue="MarketID" placeholder="--Select--" [(ngModel)]="property.MarketId"
                (change)="changeMarket($event);" labelForId="MarketId">
              </ng-select>
            </div>
          </div>

          <div class="row label-value-wrapper">
            <div class="col-md-5 label" for="text-input">Submarket
              <span
                *ngIf="locationDetailsForm?.controls['SubMarketID'] && !locationDetailsForm?.controls['SubMarketID'].valid"
                style="color:red;"> * </span>
            </div>
            <div class="col-md-7">
              <ng-select formControlName="SubMarketID" [items]="SubmarketList" [virtualScroll]="true"
                bindLabel="SubMarketName" bindValue="SubMarketID" placeholder="--Select--"
                [(ngModel)]="property.SubMarketID"
                [ngClass]="{'error-field':(!locationDetailsForm?.controls['SubMarketID']?.valid)}"
                (change)="onValueChange('SubMarketID',$event,'SubMarketName');" labelForId="SubMarketID">
              </ng-select>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <button data-toggle="collapse" data-target="#demo" class="btn btn-primary expand-btn">
          <i class="fas fa-plus fa-xs"></i>
          <i class="fas fa-minus fa-xs"></i> Intersection Info</button>
      </div>
      <div class="row">
        <div id="demo" class="col-md-12 collapse">
          <app-intersection-info [propertyLocation]="propertyLocation" [locationDetailsForm]="locationDetailsForm"
            [property]="property" (onDateChanged)="onDateChange($event)"></app-intersection-info>
        </div>
      </div>

    </div>
  </div>
</div>

