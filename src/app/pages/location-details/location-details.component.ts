import { Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';

import { SharedDataService } from '../../services/shareddata.service';
import { PropertyService } from '../../services/api-property.service';
import { LoginService } from '../../services/login.service';
import { LookupDataService } from '../../services/api-lookup-data.service';
import { CommunicationService } from '../../services/communication.service';

import { PropertyLocation } from '../../models/PropertyLocation';

import { PropertyFormControls } from '../../enumerations/propertyFormControls';
import { EnumCondoTypeName } from '../../enumerations/condoType';
import { LocationFormControls } from '../../enumerations/locationFormControls';
import { UseTypes } from '../../enumerations/useTypes';

import { AddressTypeValueNames, AddressTypeValues, LocationFieldChangeLogNames, LocationValidationsRequiredFields, PartOfComplex } from '../../constants';

import { getCondoTypeForUnitLabel } from '../../utils';
import { Property } from '../../models/Property';

@Component({
  selector: 'app-location-details',
  templateUrl: './location-details.component.html',
  styleUrls: ['./location-details.component.css']
})
export class LocationDetailsComponent implements OnInit {
  @Input() propertyLocation = new PropertyLocation();
  @Input() locationDetailsForm;
  @Input() streetMinMaxError: boolean = false;
  @Input() streetPrefixes;
  @Input() streetSufixes;
  @Input() cities;
  @Input() states;
  @Input() counties;
  @Input() countries;
  @Input() property = new Property();
  @Input() quadrants;
  @Input() showMasterStrataAlert;
  @Input() propertyLocationCopy;
  @Input() propertyCopy;
  @Input() dataArray;
  @Output() onChangeStrata = new EventEmitter();
  @Output() setAddressAsPropertyName = new EventEmitter<boolean>();
  @Output() changeMapLocation = new EventEmitter();
  @Input() propertyLookups;
  public addressTypeValues = AddressTypeValues;
  generalUseType = UseTypes;
  roofTopSource;
  complexTypes;
  complexTypeId;
  PropertyFormControlsEnum = PropertyFormControls;
  EnumCondoTypeNames = EnumCondoTypeName;
  updateMarketListListener: Subscription;
  newPropertyFetchedListener: Subscription;

  getCondoType = getCondoTypeForUnitLabel;
  MarketList: any;
  SubmarketList: any;

  ngOnDestroy(): void {
    this.updateMarketListListener?.unsubscribe();
    this.newPropertyFetchedListener?.unsubscribe();
  }

  constructor(private sharedDataService: SharedDataService, private propertyService: PropertyService, private loginService: LoginService, private lookupService: LookupDataService, private communicationService: CommunicationService) {
    this.updateMarketListListener = this.communicationService.subscribe('updateMarketList').subscribe(result => {
      const { useTypeID } = result?.data;
        this.onSpecificUseChange(useTypeID);
    });
    this.newPropertyFetchedListener = this.communicationService.subscribe('newPropertyFetched').subscribe(result =>{
      this.fetchInitialMarketList();
      this.initData();
    });
  }

  onSpecificUseChange(useTypeId) {
    this.MarketList = [];
    this.property.MarketId = null;
    this.SubmarketList = [];
    this.property.SubMarketID = null;
    if (!this.property.MetroId) {
      this.getPropertyIntersectMarketSubmarket(this.property);
    } else {
      this.getMarketList(this.property.MetroId, useTypeId);
    }
  }

  fetchInitialMarketList() {
    if (this.property.MetroId) {
      this.getMarketList(this.property.MetroId, this.property.UseTypeID);
    } else {
      this.getPropertyIntersectMarketSubmarket(this.property);
    }
    if (this.property.MarketId) {
      this.getAllSubMarketList(this.property.MarketId);
    }
  }

  getPropertyIntersectMarketSubmarket(value) {
    const response = this.propertyService.getPropertyIntersectMarketSubmarket(
      value.MetroId, value.PropertyID, value.UseTypeID, value.Latitude, value.Longitude);
    response.subscribe(result => {
      if (result.body && !result.body.error) {
        const propMarketData = result.body.responseData[0] || [];
        if (!!propMarketData.MetroID) {
          this.getMarketList(propMarketData.MetroID, value.UseTypeID);
          if (!this.property.MarketId) {
            this.getAllSubMarketList(propMarketData.MarketID);
          }
          this.property.MetroId = propMarketData.MetroID;
        }
      }
    });
  }

  getMarketList(metroID, UseTypeID) {
    const marketsList = this.propertyLookups?.['MarketID'] || [];
    this.MarketList = marketsList.filter(market => market.MetroID === metroID && market.PropertyType === UseTypeID);
    if (!this.property.MarketId) { this.property.MarketId = null; }
  }

  // to get all selected Market submarket List
  getAllSubMarketList(marketID) {
    const submarketsList = this.propertyLookups?.['SubMarketID'] || [];
    setTimeout(() => {
      this.SubmarketList = submarketsList.filter(submarket => submarket.MarketID === marketID);
    }, 0);
  }


  getRoofTopSources() {
    this.roofTopSource = this.propertyLookups?.['RoofTypeID'] || [];
    this.roofTopSource?.forEach(source => {
      source.RoofTopSource = source.RoofTypeName;
      source.RoofTopSourceID = source.RoofTypeID;
    })
  }

  // To get all complex type.
  getComplexTypes() {
    this.complexTypes = this.propertyLookups?.['ComplexTypeID'] || [];
    this.complexTypes?.forEach(complexType => {
      if (complexType?.ComplexTypeName === PartOfComplex) {
        this.complexTypeId = complexType?.ComplexTypeID;
      }
      this.onComplexTypesFetched();
    });
  }

  onComplexTypesFetched() {
    if ((this.complexTypeId) && (this.propertyLocation?.PartOfComplex === this.complexTypeId)) {
      this.locationDetailsForm?.get(LocationFormControls?.Complex)?.clearValidators();
      this.locationDetailsForm?.get(LocationFormControls?.Complex)?.setValidators([Validators.required]);
    }
  }

  ngOnInit(): void {
    this.initData();
  }


  updateValidations(){
    Object?.values(LocationFormControls)?.forEach(controlName => {
      const isValidationRequired = LocationValidationsRequiredFields?.includes(controlName);
    
      // Check if the control exists in the FormGroup
      if (this.locationDetailsForm?.contains(controlName)) {
        const control = this.locationDetailsForm?.get(controlName);
        
        // Update validators for the existing control
        control?.setValidators(isValidationRequired ? Validators.required : null);
        
        // Recalculate the control's validation status
        control?.updateValueAndValidity();
      } 
    });
  }


  initData(){
    this.getComplexTypes();
    this.getRoofTopSources();
    this.updateValidations();
    //set the address type value
    this.locationDetailsForm?.patchValue({
      AddressType: this.propertyLocation?.AddressType
    });
    this.locationDetailsForm?.get(LocationFormControls?.AddressType)?.valueChanges?.subscribe(AddressType => {
      if (AddressType === this.addressTypeValues?.Address) {
        this.clearValidators(['Quadrant', 'EastWestStreet', 'NorthSouthStreet']);
        this.setValidators(['StreetNumberMin', 'AddressStreetName'], [Validators.required]);
      } else {
        this.clearValidators(['StreetNumberMin', 'AddressStreetName']);
        this.setValidators(['Quadrant', 'EastWestStreet', 'NorthSouthStreet'], [Validators.required]);
      }
    });
    
    this.locationDetailsForm?.get(LocationFormControls?.PartOfComplex)?.valueChanges?.subscribe(partOfComplex => {
      const complexControl = this.locationDetailsForm?.get(LocationFormControls?.Complex);
      complexControl?.clearValidators();
      if (this.complexTypeId && partOfComplex === this.complexTypeId) {
        complexControl?.setValidators([Validators.required]);
      }
      complexControl?.updateValueAndValidity();
    });
  }

  setValidators = (controls: string[], validators: any[]) => {
    controls.forEach(ctrl => {
      const control = this.locationDetailsForm?.get(LocationFormControls?.[ctrl]);
      control?.setValidators(validators);
      control?.updateValueAndValidity();
    });
  };
  
  clearValidators = (controls: string[]) => {
    controls.forEach(ctrl => {
      const control = this.locationDetailsForm?.get(LocationFormControls?.[ctrl]);
      control?.clearValidators();
      control?.updateValueAndValidity();
    });
  };
  addressTypeChange(type, value) {
    this.propertyLocation.AddressType = value;
    const date = new Date().toISOString();
    const currentValue = value === 0 ? AddressTypeValueNames?.Address : AddressTypeValueNames?.Intersection;
    const previousValue = this.propertyLocationCopy.AddressType === 0 ? AddressTypeValueNames?.Address : AddressTypeValueNames?.Intersection;
    const i = this.dataArray.findIndex(x => x.Field === type);
    if (i !== -1) {
      this.dataArray.splice(i, 1);
    }
    if (this.locationDetailsForm['controls'][type].dirty) {
      this.dataArray.push({
        'Field': type, 'CurrentValue': currentValue, 'PreviousValue':
          previousValue, 'LoginEntityID': this.loginService.UserInfo.EntityID, 'DateTime': date
      });
    }
  }

  addressAsPropertyName(value: boolean) {
    this.setAddressAsPropertyName?.emit(value);
  }

  onClickChangeMapLocation() {
    this.changeMapLocation?.emit();
  }

  showStrataConfirmation() {
    this.onChangeStrata?.emit();
  }

  onDateChange(event: { type: string, value: any }) {
    this.getSelectedDate(event.type, event.value);
  }

  getSelectedDate(type, Value) {
    this.onChangeSetAsDirty(type);
    const date = new Date().toISOString();
    const i = this.dataArray.findIndex(x => x.Field === type);
    if (i !== -1) {
      this.dataArray.splice(i, 1);
    }
    const PreviousValue = this.propertyLocationCopy[type]?.singleDate?.jsDate;
    const CurrentValue = !!Value.singleDate?.formatted ? !!Value.singleDate?.date ? Value?.singleDate?.date?.year + '-' + Value?.singleDate?.date?.month + '-' + Value?.singleDate?.date?.day : '' : '';
    if (this.locationDetailsForm['controls'][type].dirty) {
      this.dataArray.push({
        'Field': type, 'CurrentValue': CurrentValue, 'PreviousValue':
          PreviousValue, 'LoginEntityID': this.loginService?.UserInfo?.EntityID, 'DateTime': date
      });
    }
  }

  changeMarket(event) {
    if (!!event) {
      const value = event.MarketID;
      this.property.SubMarketID = null;
      this.SubmarketList = [];
      this.getAllSubMarketList(value);
      this.onValueChange(LocationFieldChangeLogNames?.MarketId, event, 'MarketName');
    }
  }

  onValueChange(Type, event, ValueName) {
    const initialValue = event;
    let Value = null;
    if (!!event) {
      Value = event[ValueName];
    } else {
      Value = null;
    }
    const date = new Date().toISOString();
    let id;
    if (Type === LocationFieldChangeLogNames?.MarketId || Type === LocationFieldChangeLogNames?.SubMarketID) {
      id = this.propertyCopy[Type];
    } else {
      id = this.propertyLocationCopy[Type];
    }
    let previousData;

    const lookupMap: { [key: string]: { list: any[], key: string, value: string } } = {
      [LocationFieldChangeLogNames.StreetPrefix1]: { list: this.streetPrefixes, key: 'PrefixID', value: 'Prefix' },
      [LocationFieldChangeLogNames.StreetPrefix2]: { list: this.streetPrefixes, key: 'PrefixID', value: 'Prefix' },
      [LocationFieldChangeLogNames.StreetSuffix1]: { list: this.streetSufixes, key: 'SuffixId', value: 'Suffix' },
      [LocationFieldChangeLogNames.StreetSuffix2]: { list: this.streetSufixes, key: 'SuffixId', value: 'Suffix' },
      [LocationFieldChangeLogNames.CityID]:        { list: this.cities,        key: 'CityID',        value: 'CityName' },
      [LocationFieldChangeLogNames.County]:        { list: this.counties,      key: 'CountyID',      value: 'CountyName' },
      [LocationFieldChangeLogNames.State]:         { list: this.states,        key: 'StateID',       value: 'StateName' },
      [LocationFieldChangeLogNames.CountryID]:     { list: this.countries,     key: 'CountryID',     value: 'CountryName' },
      [LocationFieldChangeLogNames.PartOfComplex]: { list: this.complexTypes,  key: 'ComplexTypeID', value: 'ComplexTypeName' },
      [LocationFieldChangeLogNames.RooftopSourceID]: { list: this.roofTopSource, key: 'RoofTopSourceID', value: 'RoofTopSource' },
      [LocationFieldChangeLogNames.Quadrant]:      { list: this.quadrants,     key: 'QuadrantID',    value: 'QuadrantName' },
      [LocationFieldChangeLogNames.SubMarketID]:   { list: this.SubmarketList, key: 'SubMarketID',   value: 'SubMarketName' },
      [LocationFieldChangeLogNames.MarketId]:      { list: this.MarketList,    key: 'MarketID',      value: 'MarketName' }
    };

    if (Type === LocationFieldChangeLogNames.UseAddressAsPropertyName) {
      previousData = !initialValue;
    } else if (Type === LocationFieldChangeLogNames.MarketId) {
      this.property.SubMarketID = null;
      this.SubmarketList = [];
    }

    const lookup = lookupMap[Type];
    if (lookup) {
      const found = lookup.list.find(item => item[lookup.key] === id);
      if (found) {
        previousData = found[lookup.value];
      }
    }
    const i = this.dataArray.findIndex(x => x.Field === Type);
    if (i !== -1) {
      this.dataArray.splice(i, 1);
    }
    this.dataArray.push({
      'Field': Type,
      'CurrentValue': Value,
      'PreviousValue': previousData,
      'LoginEntityID': this.loginService.UserInfo.EntityID,
      'DateTime': date
    });
  }

  onChangeSetAsDirty(control) {
    const data = this.locationDetailsForm['controls'];
    Object.keys(data).map(i => {
      if (i === control) {
        data[i].markAsDirty();
      }
    });
  }

}
