import { Component, Input, OnInit } from '@angular/core';
import { CommunicationModel, CommunicationService } from '../../services/communication.service';
import { ChangeLogTypes, ChangeLogTabNames } from '../../constants';
import { LatLng } from '../../modules/map-module/models/LatLng';

@Component({
  selector: 'app-change-log',
  templateUrl: './change-log.component.html',
  styleUrls: ['./change-log.component.css']
})
export class ChangeLogComponent implements OnInit {
  @Input() propertyId;
  @Input() location: LatLng;
  selectedTab;
  isLocation: boolean = true;
  isProperty: boolean = false;
  isResearchStatus: boolean = false;
  changelogType = '';


  constructor(private communicationService: CommunicationService) { }

  ngOnInit(): void {
    this.selectedTab = 0;
    this.changelogType = ChangeLogTypes?.Address;
  }

  broadCastFetchChangeLog() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchChangeLog';
    this.communicationService.broadcast(commModel);
  }

  broadcastResearchStatus(){
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchResearchStatus';
    this.communicationService.broadcast(commModel);
  }

  changeTab($event) {
    if (!!$event.tab.textLabel) {
      if ($event.tab.textLabel === ChangeLogTabNames?.Location) {
        this.isLocation = true;
        this.isProperty = false;
        this.isResearchStatus= false;
        this.changelogType = ChangeLogTypes?.Address;
        this.broadCastFetchChangeLog();
      }
      if ($event.tab.textLabel === ChangeLogTabNames?.PropertyDetails) {
        this.isLocation = false;
        this.isProperty = true;
        this.isResearchStatus= false;
        this.changelogType = ChangeLogTypes?.Property;
        this.broadCastFetchChangeLog();
      }
      if ($event.tab.textLabel === ChangeLogTabNames?.ResearchStatusHistory) {
        this.isLocation = false;
        this.isProperty = false;
        this.isResearchStatus= true;
        this.changelogType = ChangeLogTypes?.ResearchStatus;
        setTimeout(()=>{
        this.broadcastResearchStatus();
        },100)
      }
    }
  }
}
