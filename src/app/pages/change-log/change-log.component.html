<div class="form-group row expandBox">
    <div class="col-md-12 p-0">
        <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" data-target="#Changelog"
            aria-expanded='false'><i  class="fa fa-history"></i>Change Log
        </button>
        <div id="Changelog" class="collapse mb-2 mt-2 pl-2 pr-2">
            <hr class="mt-0">
            <mat-tab-group [(selectedIndex)]="selectedTab" (selectedTabChange)="changeTab($event)">
                <mat-tab label="Location">
                    <app-changelog-modal [changelogType]="changelogType" [parentId]="propertyId" *ngIf="isLocation" [location]="location" class="change-log">
                    </app-changelog-modal>
                </mat-tab>
                <mat-tab label="Property Details">
                    <app-changelog-modal [changelogType]="changelogType" [parentId]="propertyId" *ngIf="isProperty" [location]="location" class="change-log">
                    </app-changelog-modal>
                </mat-tab>
                <mat-tab label="Research Status History">       
                    <app-research-status-history [parentId]="propertyId" [hidden]="!isResearchStatus" class="change-log">
                    </app-research-status-history >
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</div>
