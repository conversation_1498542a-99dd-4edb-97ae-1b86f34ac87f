.drop-toggle {
    background: white;
    border: 1px solid #ced4da;
    padding: 5px 10px;
    cursor: pointer;
    width: 140px;
    height: 38px;
    text-align: left;
    color: #495057;
    border-radius: 0.25rem
}

.drop-toggle i {
    padding-top: 3px;
    float: right;
}

.drop-show {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    padding: 0px;
    width: 140px;
    height: fit-content;
    position: absolute;
    z-index: 214748364700;
}

.drop-down-in-edit{
    width:160px !important;
}

.drop-toggle.btn.flat {
    box-shadow: none !important;
}

label {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 0px !important;
}

.checkbox-wrapper {
    display: flex;
    padding: 10px;
    height: 100%;
}

input[type="checkbox"] {
    width: 13px;
    height: 13px;
    accent-color: #28a745;
}

.option {
    background: white;
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 12px;
    color: #495057;
}

.label-text {
    display: flex;
    padding-left: 10px;
}
