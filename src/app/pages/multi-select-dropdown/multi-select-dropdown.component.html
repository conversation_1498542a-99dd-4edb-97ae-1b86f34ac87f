<div class="dropdown">
    <div (mouseleave)="setShowDropdown(false)">
        <button class="drop-toggle btn flat" (click)="setShowDropdown(!showDropdown)" [ngClass]="{'drop-down-in-edit':isEditPropertyOpen }">
            <span>Layers</span>
            <!-- <div class="v-icon"></div> -->
            <i class="fa fa-angle-down" *ngIf="!showDropdown"></i>
            <i class="fa fa-angle-up" *ngIf="showDropdown"></i>
        </button>
        <div class="drop-show" *ngIf="showDropdown"  [ngClass]="{'drop-down-in-edit':isEditPropertyOpen }">
            <div *ngFor="let layer of list" class="option">
                <label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" [(ngModel)]="layer.checked"
                            (change)="getSelectedValue(layer.checked,layer.id)" />
                    </div>
                    <div class="label-text">{{layer.id}}</div>
                </label>
            </div>
        </div>
    </div>
</div>
