<div class ="refresh-button" ><i class="fa fa-refresh refresh-icon" (click)="refreshAllMedia()" ></i></div>
<div *ngIf="galleryImages && galleryImages.length > 0" class="galleryContainer">
  <div *ngFor="let image of galleryImages">
    <div class="gallery">
      <div>

        <img [src]="image.URL" class="img-pointer" (click)="showImage(image)">
      </div>
      <div class="bottom-wrapper">
        <!-- <div class="actions"> -->
        <!-- <input type="button" class="btn btn-info"  value="Edit"  (click)="editMedia(image.MediaID)"/> -->
        <!-- <img src="assets/images/edit_icon.png" height="50" width="50" (click)="editMedia(image.MediaID)"> -->
        <!-- <input type="button" class="btn btn-info"  value="Delete"  (click)="deleteMedia(image.MediaID)"/> -->
        <!-- <img src="assets/images/delete_icon.png" height="50" width="50" (click)="deleteMedia(image.MediaID)"> -->
        <!-- <input type="button" class="btn btn-info"  value="Set As Default"  (click)="setAsDefault(image.MediaID)"/> -->
        <!-- <img *ngIf="image.IsDefault" src="assets/images/star_icon.png" height="50" width="50"
            (click)="setAsDefault(image.MediaID)">
          <img *ngIf="!image.IsDefault" src="assets/images/star_icon_empty.png" height="50" width="50"
            (click)="setAsDefault(image.MediaID)"> -->
        <!-- </div> -->
        <div class="desc">

          <div class="actions">
            <i class="fas fa-pen" aria-hidden="true" (click)="editMedia(image.MediaID)"></i>
            <i class="fa fa-trash" aria-hidden="true" (click)="deleteMedia(image.MediaID)"></i>
            <i class="fa fa-star" style="color: rgb(247, 247, 93);" aria-hidden="true" *ngIf="image.IsDefault" (click)="setAsDefault(image.MediaID)"></i>
            <i class="fas fa-star" aria-hidden="true" *ngIf="!image.IsDefault" (click)="setAsDefault(image.MediaID)"></i>
          </div>

          <span class="nameLabel">Name : <b>{{image.MediaName}}</b></span>
          <span class="nameLabel">Modified Date : <b>{{image.ModifiedDate | date:'MM/dd/yyyy'}}</b></span>
          <span class="nameLabel">Modified By : <b>{{image.ModifiedByName}}</b></span>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="uploads upload-wrapper">
  <div class="main-container">
    <div class="buttons loader-btn">
      <label for="inputFiles" class="btn btn-info file-input-btn">
        <i class="glyphicon glyphicon-plus"></i>
        <img src="assets/images/add_blue.png" height="50" width="50">
        <input id="inputFiles" name="files[]" multiple="" type="file" accept="image/*"
          (change)="fileChangeEvent($event)">
      </label>
    </div>
  </div>

</div>