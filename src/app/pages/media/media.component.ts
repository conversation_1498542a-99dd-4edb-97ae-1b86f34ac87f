import { Component, Input, OnInit, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { ContainerEvents, FileObject, FileObjectStatus } from '../../modules/aws/types/types';
import { MediaService } from '../../services/media.service'
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
import { Subscription } from 'rxjs';
import { Media, MediaRelationTypeEnum } from '../../models/MediaType';
import { FileTypes } from '../../modules/aws/types/types';
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { environment } from '../../../environments/environment';
import { SharedDataService } from '../../services/shareddata.service';
import { ContactMedia } from '../../models/ContactMedia';
import { CommonStrings } from '../../constants';

/**
 * Contrainer for all uploads.
 */

@Component({
  selector: 'app-media',
  templateUrl: './media.component.html',
  styleUrls: ['./media.component.css']
})
export class MediaContainerComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  files: Media[] = [];
  galleryImages: Media[] = [];
  eventSubscription: Subscription;
  fileuploaded: Subscription;
  uploadStarted = false;
  url: any;
  mediaUrl: any;

  constructor(private router: Router,
    private mediaService: MediaService,
    private communicationService: CommunicationService,
    private notificationService: NotificationService,
    private zone: NgZone
    , private _sharedDataService: SharedDataService) {
    this.eventSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
      this.zone.run(() => {
        this.initialDetails = result.data;
        //this.clearAll();
        this.refreshAllMedia();
      });
    });

    this.fileuploaded = this.communicationService.subscribe('onFileUpload').subscribe(result => {
      this.onUploadEvent(result.data);
    });

    this.mediaUrl = `${environment.MediaS3DynamicImageBase}` + `${environment.MediaS3Path}`+'/';
   
  }

  private onUploadEvent($event) {
    this.refreshAllMedia();
  }

  private handleFileUploadEvent(fileObject: FileObject) {
    if (fileObject.status === FileObjectStatus.Deleted) {
      this.files.splice(0, 1);
      // for (let i = 0; i < this.files.length; i++) {
      /* if (this.files[i] === fileObject) {
        this.files.splice(i, 1);
      } */
      //}
    }
  }

  editMedia(mediaId: number) {

    let file = this.galleryImages.find(x => x.MediaID == mediaId);
    let files = new Array<Media>();
    files.push(file);
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, files: files };
    model.Key = 'showFileUploadModal';
    this.communicationService.broadcast(model);
  }

  

  showImage(image) {
    if(image.Ext.toLowerCase() == FileTypes.PNG || image.Ext.toLowerCase() == FileTypes.JPG){
    let imgUrls: string[]=[];
    let imgIndex: number;
    let index = 0;
    this.galleryImages.forEach(img => {
      //let path = constants.MediaS3Base + constants.MediaS3Path + "/" + img.Path
      let path = this.mediaUrl + img.Path;
      imgUrls.push(path)
      if (img.URL == image.URL)
        imgIndex = index;

      index++;
    })
    let model: CommunicationModel = new CommunicationModel();
    model.data = { imageUrls: imgUrls, ImageIndex: imgIndex };
    model.Key = 'imageViewer';
    this.communicationService.broadcast(model);
  }else {
    //let path = constants.MediaS3Base + constants.MediaS3Path + "/" + image.Path
    let path = this.mediaUrl + image.Path;
    window.open(path);
  }
  }
  deleteMedia(mediaId: number) {
    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteMediaFileConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
      let file = this.galleryImages.find(x => x.MediaID == mediaId);
      const medias = this.mediaService.DeletePropertyMedia(file);
      medias.subscribe(result => {
        this.refreshAllMedia();
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {

    }

    this.notificationService.CustomDialog(configuration);

    /*
     this.notificationService.Confirm("The media file will be deleted. Do you want to continue?", (result)=>{
     if(result)
     {
       let file = this.galleryImages.find(x=>x.MediaID==mediaId);
       const medias = this.mediaService.DeletePropertyMedia(file);
       medias.subscribe(result => {
       this.refreshAllMedia();
     });
     }
   })
   */

  }

  setAsDefault(mediaId: number) {

    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.SetAsDefaultImageConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
      let file = this.galleryImages.find(x => x.MediaID == mediaId);
      const medias = this.mediaService.SetDefaultMedia(file);
      medias.subscribe(result => {
        this.refreshAllMedia();
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {

    }

    this.notificationService.CustomDialog(configuration);




  }

  fileChangeEvent(fileInput: any) {
    this.files = new Array<Media>();
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const fileObject = new FileObject(fileInput.target.files[i]);
        const media = new Media();
        media.File = fileInput.target.files[i];
        if (fileInput.target.files && fileInput.target.files[0]) {

          var reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            var img = new Image;
            img.onload = function () {

              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
          }
          reader.readAsDataURL(fileInput.target.files[0]);
        }
        this.files.push(media);
      }
    }
    fileInput.target.value = null;

    let model: CommunicationModel = new CommunicationModel();

    model.data = { initialDetails: this.initialDetails, files: this.files };
    model.Key = 'showFileUploadModal';
    this.communicationService.broadcast(model);
  }

  // uploadAll() {
  //   console.log('uploading all');
  //   this.s3Service.publishUploadContainerEvent(ContainerEvents.Upload);
  // }

  // cancelAll() {
  //   console.log('aborting all');
  //   this.s3Service.publishUploadContainerEvent(ContainerEvents.Cancel);
  // }

  // clearAll() {
  //   console.log('clearing all');
  //   this.s3Service.publishUploadContainerEvent(ContainerEvents.Delete);
  // }

  refreshAllMedia() {
    let initialDetails = new ContactMedia();
    initialDetails.RelationshipTypeID = MediaRelationTypeEnum.Property;
    initialDetails.RelationID = this.initialDetails.propertyId;
    const medias = this.mediaService.getAllMediaRelation(initialDetails);
    medias.subscribe(result => {
      if (!result.body.error) {
        this.galleryImages = result.body.responseData[0];
        this.galleryImages.forEach(x => {
          if(x.Ext.toLowerCase() == FileTypes.PNG || x.Ext.toLowerCase() == FileTypes.JPG){
          x.URL = this.mediaUrl + environment.MediaS3ThumbnailPath + "/" + environment.MediaS3ThumbResolution  +"/"+ x.Path;
        }
        else if(x.Ext.toLowerCase() == FileTypes.PDF) {
          x.URL = 'assets/images/pdf.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.TEXT) {
          x.URL = 'assets/images/text.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.WORD) {
          x.URL = 'assets/images/word.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.EXCEL) {
          x.URL = 'assets/images/excel.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.CSV) {
          x.URL = 'assets/images/csv.png'
        }
      }
      );
         this._sharedDataService.propertyMedia =    this.galleryImages;
         this._sharedDataService.propertyMedia.forEach(element => {
           element.PropertyID = this.initialDetails.propertyId;
         });
      }
    });
  }

  ngOnInit() {
      if(!!this._sharedDataService.propertyMedia && this._sharedDataService.propertyMedia.length > 0 && this._sharedDataService.propertyMedia[0].PropertyID == this.initialDetails.propertyId) {
       this.galleryImages = this._sharedDataService.propertyMedia;
      } else {
        this.refreshAllMedia();
      }   

   
  }

  ngOnDestroy() {
    // prevent memory leak when component destroyed
    this.eventSubscription.unsubscribe();
    this.fileuploaded.unsubscribe();
  }
}
