.uploads {
    margin: 10px 0px;
    background: #fff;
    /* min-height: 500px; */
    width: 100%;
    height: 100%;
}
.refresh-icon{
    margin:5px;
    cursor: pointer;
}
.refresh-button{
    display:flex;
    justify-content: flex-end;
    width:100%;
}
.mat-tab-body-content{
    overflow: hidden !important;
}
.upload-wrapper .buttons label {
    background: none;
    border: 2px dashed #ccc;
    min-width: 300px;
    height: 300px;
    vertical-align: middle;
    float: none;
    display: table-cell;
    margin-top: 20px;
}

.buttons.loader-btn {
    float: left;
    padding: 5px;
    margin-top: 15px;
    margin-left: 5px;
}

.browser-image {
    text-align: left;
}

.browser-image h3 {
    font-size: 18px;
    margin: 10px 0px;
    margin-top: 0;
    color: var(--primary-brightBlue);
}

.uploads label.file-input-btn {
    margin: 0;
}

#inputFiles {
    position: absolute;
    top: -300px;
    right: -300px;
    margin: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    font-size: 23px;
    direction: ltr;
    cursor: pointer;
}

.nameLabel {
    font-size: 0.8rem;
    display: block;
    overflow: hidden;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.valueLabel {}

.links {
    float: right;
}

.buttons {
    float: right;
}

.files {
    clear: both;
}

div.gallery {
    border: 0;
    float: left;
    width: 300px;
    height: 300px;
    position: relative;
    border-radius: 4px;
    -webkit-transition: all .25s ease;
    transition: all .25s ease;
    -webkit-box-shadow: 2px 5px 5px 1px #b3b3b3;
    box-shadow: 2px 5px 5px 1px #b3b3b3;
    background: #fff;
    margin-top: 20px;
    margin-left: 20px;
}

div.gallery:hover {
    /* -webkit-transform: translateY(-4px) scale(1.02);
    -moz-transform: translateY(-4px) scale(1.02);
    -ms-transform: translateY(-4px) scale(1.02);
    -o-transform: translateY(-4px) scale(1.02);
    transform: translateY(-4px) scale(1.02); */
    -webkit-box-shadow: 2px 5px 5px 1px #999898;
    box-shadow: 2px 5px 5px 1px #999898;
    z-index: 999;
}

div.gallery img {
    max-width: 100%;
    height: 300px;
}

div.gallery{
    text-align: center;
}

div.desc {
    /* position: absolute; */
    padding: 5px;
    width: 100%;
    background: rgba(0, 0, 0, 0.747);
    color: #fff;
    width: 100%;
    /* bottom:0; */
    /* padding-top: 30px; */
}

.bottom-wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: left;
}

div.actions {
    margin-top: 0;
    margin-right: 0;
    text-align: right;
    padding: 5px;
    color: #fff;
}

div.actions i{
    margin: 0 0 0 5px;
}

div.actions img {
    /* width: 36px !important;
    height: 36px !important;
    background: #fff;
    border: 1px solid #ccc; */
    padding: 4px;
    margin-right: 5px;
}

.img-pointer {
    cursor: pointer;
}