.pagination>.disabled>a,
.pagination>.disabled>a:focus,
.pagination>.disabled>a:hover,
.pagination>.disabled>span,
.pagination>.disabled>span:focus,
.pagination>.disabled>span:hover {
  color: #fff;
  cursor: not-allowed;
  background-color: #e06a00;
  border-color: none;
}
.card-body {
    font-size: 14px;
}

.form-control {
    margin-bottom: 10px;
}

.form-content {
  margin-left: 30%;
}
.form-heading-label,
.table-content {
    margin-left: 15px;
}

.header {
    border-bottom: 2px solid #4180c3;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page_sub_head1 {
    height: auto !important;
    text-align: left;
    text-indent: 0 !important;
    vertical-align: middle;
    padding-left: 0px;
    padding-top: 5px;
    padding-bottom: 10px;
    text-transform: uppercase;
}

.label-margin {
    margin-top: 9px;
    margin-right: 20px;
}

.lbl-pname {
    margin-bottom: 0;
}

.skip-checkbox {
    margin: 8px 0;
    padding: 0 15px;
    width: auto;
}
.form-control {
  margin-bottom: 10px;
}

.pagination>.disabled>a,
.pagination>.disabled>a:focus,
.pagination>.disabled>a:hover,
.pagination>.disabled>span,
.pagination>.disabled>span:focus,
.pagination>.disabled>span:hover {
  color: #0d0e29 !important;
  cursor: not-allowed !important;
  background-color: #f4f5f5 !important;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f4f5f5), to(#dfdddd)) !important;
  background-image: -webkit-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -moz-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -ms-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: -o-linear-gradient(top, #f4f5f5, #dfdddd) !important;
  background-image: linear-gradient(to bottom, #f4f5f5, #dfdddd) !important;
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#f4f5f5, endColorstr=#dfdddd) !important;
}

.page-item.active .page-link,
.pagination-datatables li.active .page-link,
.pagination li.active .page-link,
.page-item.active .pagination-datatables li a,
.pagination-datatables li .page-item.active a,
.pagination-datatables li.active a,
.page-item.active .pagination li a,
.pagination li .page-item.active a,
.pagination li.active a {
  background-color: #3093c7 !important;
  ;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#3093c7), to(#1c5a85)) !important;
  ;
  background-image: -webkit-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -moz-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -ms-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: -o-linear-gradient(top, #3093c7, #1c5a85) !important;
  ;
  background-image: linear-gradient(to bottom, #3093c7, #1c5a85) !important;
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#3093c7, endColorstr=#1c5a85) !important;
}

.page-item.active .page-link,
.pagination-datatables li.active .page-link,
.pagination li.active .page-link,
.page-item.active .pagination-datatables li a,
.pagination-datatables li .page-item.active a,
.pagination-datatables li.active a,
.page-item.active .pagination li a,
.pagination li .page-item.active a,
.pagination li.active a {
  z-index: 2;
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}

.page-link,
.pagination-datatables li a,
.pagination li a {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #20a8d8;
  background-color: #fff;
  border: 1px solid #ddd;
}

table {
  font-size: 14px !important;
}


tr:nth-child(even) {
  background-color: #f5f5f5;
}

thead th {
  background: #1e4b7b;
  color: #fff;
  position: relative;
}

.tableRow {
  padding: 10px 0px !important;
  font-size: 13px;
}

tr:nth-child(even) {
  background-color: #f5f5f5;
}

.record-number {
  color: #20a8d8 !important;
  text-decoration: none !important;
  background-color: transparent !important;
  cursor: pointer;
}

.arrow-group {
  display: inline-block;
  position: absolute;
  right: 0;
}

.arrow-group i {
  cursor: pointer;
  height: 10px;
  bottom: 3px;
}

.arrow-group i.fa.fa-sort-up {
  position: relative;
  left: 0;
}

.arrow-group i.fa.fa-sort-down {
  position: relative;
  top: 5px;
  right: 9px;
}

.form-group {
  margin-bottom: 10px;
}

.padding-left {
  padding-left: 10px !important;
}

.square-nocolor-legend {
  color: #fff;
  padding: 2px 15px;
  border-radius: 7px;
  font-weight: bold;
  font-size: 10px;
  margin-right: 3px;
  float: right;
  margin-top: 10px;
}

.blueBackground {
  background: rgb(81, 165, 233);
}

.greenBackground {
  background: rgb(71, 207, 89);
}

.yellowBackground {
  background: rgba(240, 219, 35, 0.973);
}

.redBackground {
  background: rgba(240, 76, 35, 0.973);
}

.magentaBackground {
  background: rgba(216, 33, 134, 0.973);
}

.fa-check-circle {
  color: green;
  margin-left: 4px;;
}

.fa-check-circle-grey {
  color: #40a2e3 !important;
  margin-left: 4px;;
}

.select-style {
  margin-bottom: 10px;
}
.action-btns {
  margin-left: 15%;
}
.search-btn {
  background: #4180c3;
  border-color: #4180c3;
  color: white;
}

.table-height{
  min-height:100px;
}

.header-row { 
  display:flex;
  align-items: center;
  justify-content: space-between;
  width:100%;
  }

  .header-row-content {
    white-space: normal;
    word-wrap: break-word;
  }

.navigation-type {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 15px !important;
  display: flex;
  align-items: center;
  margin: 1px 5px 0px 0px;
}

.navigation-type-button {
  flex: 0 0 auto;
  margin-right: 5px;
  font-size: 15px !important;
}

.navigation-preference {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  margin-bottom: 15px;
}

.navigation-prefernce-text {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 17px !important;
  font-weight: bolder;
  gap: 5px;
}

.info-icon {
  margin-top: 2px;
}
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px; /* Adjust height based on your layout */
  text-align: center;
  position: absolute;
  left: 45%;
}
.spinner-container span {
  margin-left: 10px;
}

.input-with-icon {
  position: relative;
}

.input-with-icon input {
  padding-right: 40px;
}
.input-with-icon .fa-calendar-alt {
  font-size: 18px;
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
}
.checkbox-wrapper {
  justify-content: center;
}

.input-with-icon {
  position: relative;
}

.input-with-icon .clear-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  font-size: 1rem;
}

.input-with-icon .clear-icon:hover {
  color: #000;
}

.readonly-input {
  background-color: #fff;
  cursor: pointer;
  color: #000;
}

.readonly-input:focus {
  outline: none;
}

.input-with-icon .clear-icon {
  position: absolute;
  right: 33px;
  top: 52%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #000;
}

.input-with-icon .clear-icon:hover {
  color: #000;
}

.date-error-msg {
  margin-bottom: 10px;
  text-align: center;
}

.reviewDate{
  padding-left: 5px;
}
