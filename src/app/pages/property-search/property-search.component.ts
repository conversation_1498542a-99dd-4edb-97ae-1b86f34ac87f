import { Component, OnInit, HostListener, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Table } from 'primeng/table';
import { LoginService } from '../../services/login.service';
import { SharedDataService } from '../../services/shareddata.service';
import { LookupDataService } from '../../services/api-lookup-data.service';
import { PropertyService } from '../../services/api-property.service';
import { MapService } from '../../modules/map-module/service/map-service.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { IMetaData, MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { PagerService } from '../../services/pager.service';
import { UserService } from '../../services/user.service';

import { PropertySortCriteria } from '../../models/PropertySortCriteria';
import { Property } from '../../models/Property';
import { PropertySearchResult } from '../../models/PropertySearchResult';
import { PropertySearchCriteria } from '../../models/PropertySearchCriteria';

import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { NavigationPreferences } from '../../enumerations/searchGridNavigationTypes';
import { UserRoles } from '../../enumerations/userRoles';
import { EnumCondoTypeName } from '../../enumerations/condoType';
import { environment } from '../../../environments/environment';
import { StringValuedSortParams } from '../../constants';
import { TryParseInt, clearSessionStorage, getInt } from '../../utils';
import { IAngularMyDpOptions } from 'angular-mydatepicker';

const CryptoJS = require('crypto-js');

@Component({
  selector: 'app-property-search',
  templateUrl: './property-search.component.html',
  styleUrls: ['./property-search.component.css']
})
export class PropertySearchComponent implements OnInit {
  @ViewChild('propertyTable') dataTable: Table;
  navigationPreferences = [
    { name: NavigationPreferences.UnVisited },
    { name: NavigationPreferences.UnTouched }
  ];
  reviewedDateError: boolean = false;
  selectedNavigationType: string;
  _lookupService: LookupDataService;
  userService: UserService;
  _propertyService: PropertyService;
  searchCriteria: PropertySearchCriteria = new PropertySearchCriteria();
  sortCriteria: PropertySortCriteria;
  public states: any;
  CountryId: number;
  public cities: any;
  tmpSearchCriteria: PropertySearchCriteria = new PropertySearchCriteria();
  public propertyTypes: any;
  public isPropertyTypeSelected = false;
  searchPropertyFields = true;
  searchPropertyTable = false;
  public propertyList: Array<Property>;
  public propertyListCopy: Array<Property>;
  propertyCount = 0;
  propertypager: any = {};
  public propertyLoading = false;
  IsSearchInProgres = false;
  currentPage = 1;
  public researchTypes: any;
  condoTypes: any;
  totalRecords: number;
  IsSelectAll: boolean = false;
  propertyHeader: any;
  showMapModal: boolean = false;
  private _mapService: MapService;
  visitedPropertyIds: any = [];
  editedPropertyIds: any = [];
  currentPagePropertyList: Property[];
  SearchResultsString: string;
  researchersList: any[] = [];
  auditStatusList: any[] = [];
  metaDataIndexedDBService: MetaDataIndexedDBService;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false,
  };

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (event.keyCode === 13) {
      this.onSearchClick();
    }
  }

  constructor(mapService: MapService,
    private loginService: LoginService,
    private _sharedDataService: SharedDataService,
    lookupService: LookupDataService,
    propertyService: PropertyService,
    private router: Router,
    private pagerService: PagerService,
    private _notificationService: NotificationService,
    _userService: UserService
  ) {
    this._mapService = mapService;
    this.searchCriteria = new PropertySearchCriteria();
    this.sortCriteria = new PropertySortCriteria();
    this._lookupService = lookupService;
    this._propertyService = propertyService;
    this.userService = _userService;
    const loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
    if (loginData !== '' && !!loginData) {
      const bytes = CryptoJS.AES.decrypt(loginData.toString(), environment.EncryptionKey);
      const loggedinData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
      if (loggedinData) {
        this.CountryId = loggedinData.CountryId;
      }
    }
    this.searchCriteria.CountryId = this.CountryId;
  }

  initializeSearchForm() {
    if (sessionStorage.getItem(SessionStorageKeys.SearchCriteria)) {
      this.restoreSearchCriteriaFromCache();
    } else {
      this.searchCriteria = new PropertySearchCriteria();
      this.searchCriteria.ListingType = 'all';
      this.searchCriteria.ExcludeHidden = true;
      this.searchCriteria.OffsetValue = 500;
      this.searchCriteria.StartingIndex = 1;
      this.searchCriteria.FrontEndStartingIndex = 1;
      this.searchCriteria.FrontEndOffsetValue = 150;
      this.currentPage = 1;
    }
  }

  ngOnInit() {
    this.propertyHeader = [
      { field: '', header: 'Number' },
      { field: 'PropertyId', header: 'Property ID' },
      { field: 'ZipCode', header: 'Zip Code' },
      { field: 'StreetNumberMin', header: 'Street Number Min' },
      { field: 'StreetNumberMax', header: 'Street Number Max' },
      { field: 'AddressStreetName', header: 'Street Name' },
      { field: 'City', header: 'City' },
      { field: 'PropertyType', header: 'General Use' },
      { field: 'SpecificUses', header: 'Specific Use' },
      { field: 'ResearchTypeName', header: 'Status' },
      { field: 'CondoTypeID', header: 'Record Type' },
      { field: 'CondoUnit', header: 'Unit' },
      { field: 'IsSkipped', header: 'Skip' },
      { field: 'Comments', header: 'Comments' },
      { field: 'AuditStatus', header: 'Audit Status' }
    ];
    this.selectedNavigationType = JSON.parse(sessionStorage.getItem(SessionStorageKeys.NavigationPreference)) || NavigationPreferences.UnVisited;
    const today = new Date();
    const nextDay = new Date(today);
    nextDay.setDate(today.getDate() + 1);
    this.myDpOptions.dateFormat = this.loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.disableSince = { year: nextDay?.getFullYear(), month: nextDay?.getMonth() + 1, day: nextDay?.getDate() };
  }

  ngAfterViewInit() {
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.getVisitedProperties();
    this.getEditedProperties();
    this.getStateListFromMetaData();
    this.getPropertyTypesFromMetaData();
    this.getResearchersFromMetaData();
    this.getAuditStatusesFromMetaData();
    this.getResearchStatusesFromMetaData();
    const auditStatusUpdated = sessionStorage.getItem(SessionStorageKeys.FetchProperties);
    if (auditStatusUpdated == 'true') {
      const SearchCriteriaFromSs = sessionStorage.getItem(SessionStorageKeys.SearchCriteria);
      this.searchCriteria = JSON.parse(SearchCriteriaFromSs);
      if (this.searchCriteria) {
        this.getResultFromDB();
      }
      sessionStorage.removeItem(SessionStorageKeys.FetchProperties)
    } else {
      this.getPropertyListFromMetaData();
    }
  }


  async getResearchStatusesFromMetaData() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.ResearchStatusesList);
      if (searchData && searchData.value && searchData.value.length > 0) {
        this.researchTypes = searchData.value;
      } else {
        this.getResearchStatuses();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getPropertyTypesFromMetaData() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyTypeList);
      if (searchData && searchData.value && searchData.value.length > 0) {
        this.propertyTypes = searchData.value;
      } else {
        this.getPropertyType();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  getResearchersFromMetaData = async() => {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.ResearchersList);
      if (searchData && searchData.value && searchData.value.length > 0) {
        this.researchersList = searchData.value;
      } else {
        this.getResearchers();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  getAuditStatusesFromMetaData = async () => {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.AuditStatusList);
      if (searchData && searchData.value && searchData.value.length > 0) {
        this.auditStatusList = searchData.value;
      } else {
        this.getAuditStatuses();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getStateListFromMetaData() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.StateList);
      if (searchData) {
        const stateListFromMetadata = searchData.value;
        if (stateListFromMetadata && !!stateListFromMetadata.find(x => x.countryId === this.CountryId) && stateListFromMetadata.find(x => x.countryId === this.CountryId).stateList) {
          this.states = stateListFromMetadata.find(x => x.countryId === this.CountryId).stateList;
        } else {
          this.getStateList();
        }
      } else {
        this.getStateList();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getVisitedProperties() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.VisitedPropertyIds);
      if (searchData) {
        this.visitedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getEditedProperties() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.EditedPropertyIds);
      if (searchData) {
        this.editedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getPropertyListFromMetaData() {
    try {
      this.searchPropertyFields = false;
      this.searchPropertyTable = true;
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyList);
      this.SearchResultsString = sessionStorage.getItem(SessionStorageKeys.SearchResults);
      const SearchCriteriaFromSs = sessionStorage.getItem(SessionStorageKeys.SearchCriteria);
      this.searchCriteria = JSON.parse(SearchCriteriaFromSs);
      if (searchData && SearchCriteriaFromSs && this.SearchResultsString) {
        this.propertyList = searchData.value.propertyList;
        this.propertyListCopy = searchData.value.propertyListCopy;
        const SearchResultsFromSs = JSON.parse(this.SearchResultsString);
        this.currentPagePropertyList = SearchResultsFromSs.CurrentPagePropertyList;
        this.propertyCount = SearchResultsFromSs.PropertyCount;
        this.propertypager = SearchResultsFromSs.Pager;
        this.currentPage = SearchResultsFromSs.Pager.currentPage;
        if(this.searchCriteria.State){
          this.GetCity(Number(this.searchCriteria.State),false);
        }
        const lastVistedPropertyId = this.visitedPropertyIds[this.visitedPropertyIds.length - 1];
        const rowIndex = this.currentPagePropertyList.findIndex(property => property.PropertyId === lastVistedPropertyId);
        this.scrollToRow(rowIndex);
      } else {
        this.initializeSearchForm();
        this.searchPropertyFields = true;
        this.searchPropertyTable = false;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  isPropertyVisited(propertyID) {
    if (this.visitedPropertyIds && this.visitedPropertyIds.length > 0) {
      return this.visitedPropertyIds.includes(propertyID);
    }
  }

  isPropertyEdited(propertyID) {
    if (this.editedPropertyIds && this.editedPropertyIds.length > 0) {
      return this.editedPropertyIds.includes(propertyID);
    }
  }

  clearState() {
    this.searchCriteria.CityId = null;
    this.cities = [];
  }

  getCondoType(CondoTypeID) {
    switch (CondoTypeID) {
      case 1:
        return 'Not Strata';
      case 2:
        return 'Strata';
      case 3:
        return 'Master Strata';
      case 4:
        return 'Master Freehold';
      case 5:
        return 'Child Freehold'
    }
  }

  onPropertyClick(clickedProperty) {
    const activeGridRowNumber = this.propertyList.findIndex(property => property.PropertyId === clickedProperty.PropertyId);
    const selectedPropertyPos = { Latitude: clickedProperty.Latitude, Longitude: clickedProperty.Longitude };
    const isNavigationFromSearch = true;
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(activeGridRowNumber));
    sessionStorage.setItem(SessionStorageKeys.IsNavigationFromSearch, JSON.stringify(isNavigationFromSearch));
    sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(clickedProperty.PropertyId));
    sessionStorage.setItem(SessionStorageKeys.SelectedPropertyPos, JSON.stringify(selectedPropertyPos));
    sessionStorage.setItem(SessionStorageKeys.SearchResultsPageDetails, JSON.stringify({ CurrentPage: this.currentPage, PageSize: this.searchCriteria.FrontEndOffsetValue }));
    this.visitedPropertyIds.push(clickedProperty.PropertyId);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
    this.router.navigate(['/expressmapsearch']);
  }

  GetCity(selectedState,isSelectedStateChanged:boolean = true) {
    if (isSelectedStateChanged) {
      this.cities = [];
      this.searchCriteria.CityId = null;
    }
    const response_cities = this._lookupService.GetCitiesByState(selectedState);
    response_cities.subscribe(result => {
      if (!result.body.error) {
        this.cities = result.body.responseData;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.CityList, value: this.cities });
      }
    });
  }

  restoreSearchCriteriaFromCache() {
    this.searchCriteria = JSON.parse(sessionStorage.getItem(SessionStorageKeys.SearchCriteria));
    this.searchCriteria.ListingType = 'all';
    this.searchCriteria.FrontEndStartingIndex = 1;
    this.searchCriteria.FrontEndOffsetValue = 150;
    this.searchCriteria.OffsetValue = 500;
    this.searchCriteria.StartingIndex = 1;
    this.currentPage = 1;
    if (this.searchCriteria.State) {
      this.searchCriteria.State = Number(this.searchCriteria.State);
      this.GetCity(Number(this.searchCriteria.State), false);
    }
    if (this.searchCriteria.CityId) {
      this.searchCriteria.CityId = this.stringToArray(this.searchCriteria.CityId.toString());
    }
    if (this.propertyTypes && this.searchCriteria.PropertyType) {
      this.searchCriteria.PropertyType.split(',').map(Number).forEach(type => {
        this.propertyTypes.find(x => x.UseTypeID === type).IsSelected = true;
      });
      this.showPropTypeCheckButton(0);
    }
  }

  showPropTypeCheckButton(propertyTypeId) {
    let selectCount = 0;
    this.propertyTypes.forEach(type => {
      if (type.IsSelected) {
        selectCount++;
      }
    });
    if (selectCount > 0) {
      this.isPropertyTypeSelected = true;
    } else {
      this.isPropertyTypeSelected = false;
    }
  }
  showSkipCheckButton(checked) {
    this.searchCriteria.IsSkipped = checked
  }

  onIsReviewedChacked(checked) {
    this.searchCriteria.IsNotReviewed = checked ? 1 : 0;
    if (this.searchCriteria.IsNotReviewed) {
      this.searchCriteria.LastReviewedDateMax = null;
      this.searchCriteria.LastReviewedDateMaxFormatted = null;
      this.searchCriteria.LastReviewedDateMin = null;
      this.searchCriteria.LastReviewedDateMinFormatted = null;
    }
  }

  onExcludeHiddenChecked(checked) {
    this.searchCriteria.ExcludeHidden = checked;
  }

  onNavigationPreferenceChange(selectedNavigationType) {
    sessionStorage.setItem(SessionStorageKeys.NavigationPreference, JSON.stringify(selectedNavigationType));
  }

  closeMapModal() {
    this.showMapModal = false;
  }

  openMapModel() {
    if (this.currentPagePropertyList.find(property => property.IsSelected === true)) {
      this.showMapModal = true;
    }
    else
      this._notificationService.ShowErrorMessage("No records selected to view on Map");
  }

  stringToArray(strValue) {
    const returnArray = [];
    if (strValue) {
      const array = strValue.split(',');
      array.forEach(value => {
        returnArray.push(parseInt(value, 0));
      });
      return returnArray;
    } else {
      return null;
    }
  }

  arrayToString(List: any) {
    let saleCon = '';
    if (List) {
      if (List.length > 0) {
        for (const con of List) {
          saleCon = saleCon + con + ',';
        }
        return saleCon.slice(0, -1);
      }
    } else {
      return null;
    }
  }

  onSearchResetClick() {

    this.searchCriteria = new PropertySearchCriteria();
    sessionStorage.removeItem(SessionStorageKeys.SearchCriteria);
    sessionStorage.removeItem(SessionStorageKeys.SearchResults);

    this.searchCriteria.ListingType = 'all';
    this.searchCriteria.ExcludeHidden = true;
    this.searchCriteria.StartingIndex = 1;
    if (this.propertyTypes) {
      this.propertyTypes.forEach(type => {
        type.IsSelected = false;
      });
    }
    this.isPropertyTypeSelected = false;
    this.searchPropertyFields = true;
    this.searchPropertyTable = false;
  }


  public onSearchClick() {
    this.propertyList = [];
    this.propertyListCopy = [];
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.PropertyList);
    this.searchCriteria.OffsetValue = 500;
    this.searchCriteria.FrontEndOffsetValue = 150;
    const sortCriteriaFromStorage = sessionStorage.getItem(SessionStorageKeys.SortCriteria);
    if (sortCriteriaFromStorage) {
      this.sortCriteria = JSON.parse(sortCriteriaFromStorage);
    } else {
      this.sortCriteria.SortParam = 'defaultSort';
      this.sortCriteria.SortOrder = '';
      sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
    }
    this.searchCriteria.CityId = !!this.searchCriteria.CityId ? this.arrayToString(this.searchCriteria.CityId) : null;
    this.searchCriteria.State = !!this.searchCriteria.State ? this.searchCriteria.State.toString() : null;
    this.searchCriteria.ResearcherIDs = this.searchCriteria.PersonIDArray ? (this.searchCriteria.PersonIDArray.length > 0 ? this.arrayToString(this.searchCriteria.PersonIDArray) : null) : null;
    this.searchCriteria.AuditStatus = this.searchCriteria.AuditStatusArray ? (this.searchCriteria.AuditStatusArray.length > 0 ? this.arrayToString(this.searchCriteria.AuditStatusArray) : null) : null;
    this.searchCriteria.LastReviewedDateMax = this.searchCriteria?.LastReviewedDateMaxFormatted ? this.searchCriteria?.LastReviewedDateMaxFormatted?.singleDate?.formatted : null;
    this.searchCriteria.LastReviewedDateMin = this.searchCriteria?.LastReviewedDateMinFormatted ? this.searchCriteria?.LastReviewedDateMinFormatted?.singleDate?.formatted : null;

    this.initializeSearchCriteria();
    this.getResultFromDB();
  }

  initializeSearchCriteria() {
    let selectedPropTypes = '';
    const propArray = [];
    if (this.propertyTypes) {
      this.propertyTypes.forEach(value => {
        if (!!value.IsSelected) {
          if (value.IsSelected) {
            propArray.push(value.UseTypeID);
          }
        }
      });
    }
    selectedPropTypes = propArray.join(',');
    this.searchCriteria.ResearchStatusIDs = this.searchCriteria.researchTypeArray ? this.searchCriteria.researchTypeArray.join(',') : '';
    this.searchCriteria.StrataTypeIDs = this.searchCriteria.StrataTypeArray ? this.searchCriteria.StrataTypeArray.join(',') : '';
    this.searchCriteria.PropertyType = selectedPropTypes;
    this.searchCriteria.IncludeStrataPropertyForMaster = 1;
    if (this.searchCriteria.NotStrata) {
      this.searchCriteria.StrataTypeIDs = "1";
    } else {
      this.searchCriteria.StrataTypeIDs = null;
      if (!!this.searchCriteria.ResearchStatusIDs || !!this.searchCriteria.PropertyType || !!this.searchCriteria.IsSkipped || !!this.searchCriteria.StreetName || !!this.searchCriteria.StreetMax || !!this.searchCriteria.StreetMin || !!this.searchCriteria.PropertyId || !!this.searchCriteria.IsNotReviewed || !!this.searchCriteria?.LastReviewedDateMax || !!this.searchCriteria?.LastReviewedDateMin) {
        this.searchCriteria.StrataFilter = 1;
      } else {
        this.searchCriteria.StrataFilter = 2;
      }
    }
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    sessionStorage.setItem(SessionStorageKeys.SearchCriteria, JSON.stringify(this.searchCriteria));
  }

  getResultFromDB() {
    this.propertyLoading = true;
    this.IsSelectAll = false;
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    this.tmpSearchCriteria.LoginEntityID = this.loginService.UserInfo.EntityID;
    this.tmpSearchCriteria.HasNoBuildingFootprints = this.searchCriteria.HasNoBuildingFootprints ? 1 : 0;
    this.tmpSearchCriteria.HasNoExistingParcelInTileLayer = this.searchCriteria.HasNoExistingParcelInTileLayer ? 1 : 0;
    this.propertyList = [];
    this.propertyListCopy = [];
    const response_propertSearch = this._propertyService.propertyCRESearch(this.tmpSearchCriteria);
    response_propertSearch.subscribe(result => {
      this.propertyList = result.body.responseData[0] || [];
      this.propertyList.forEach(property => {
        if (property.AddressStreetName !== null) {
          // Trim extra spaces from the AddressStreetName field
          property.AddressStreetName = property.AddressStreetName.trim();
        }
      });
      if (this.searchCriteria.StrataFilter === 2) {
        this.propertyListCopy = this.propertyList.slice();
      } else {
        this.removeChildStrataFromListIfItsMasterStrataAlreadyExistsInList();
        this.propertyListCopy = this.propertyList.slice();
      }
      if (this.sortCriteria.SortParam === "defaultSort") {
        this.defaultSortOrder();
      }
      else {
        this.sortProperty(this.sortCriteria.SortParam, this.sortCriteria.SortOrder);
      }
      if (this.propertyList) {
        this.propertyCount = this.propertyList.length;
        this.totalRecords = this.propertyCount;
      }
      this.IsSearchInProgres = false;
      this.propertyLoading = false;
    });
    this.searchPropertyFields = false;
    this.searchPropertyTable = true;
  }

  getPropertyRecordDisplayNumber(index) {
    const currentPropertyRowNumber = ((index + 1) + (this.searchCriteria.FrontEndOffsetValue * (this.currentPage - 1))).toString().padStart(3, '0');
    return currentPropertyRowNumber;
  }

  defaultSortOrder() {
    this.propertyList = this.propertyListCopy.slice();
    this.sortCriteria.SortParam = 'defaultSort';
    this.sortCriteria.SortOrder = '';
    sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
    this.propertyList.sort((a, b) => {
      // Compare ZipCode
      let zipComparison;
      if (a.ZipCode === null && b.ZipCode !== null) {
        zipComparison = 1;
      } else if (a.ZipCode !== null && b.ZipCode === null) {
        zipComparison = -1;
      } else if (a.ZipCode !== null && b.ZipCode !== null) {
        zipComparison = a.ZipCode.localeCompare(b.ZipCode);
      } else {
        zipComparison = 0;
      }
      if (zipComparison !== 0) return zipComparison;

      // If ZipCode is the same, compare AddressStreetName
      let addressComparison;
      if (a.AddressStreetName === null && b.AddressStreetName !== null) {
        addressComparison = 1;
      } else if (a.AddressStreetName !== null && b.AddressStreetName === null) {
        addressComparison = -1;
      } else if (a.AddressStreetName !== null && b.AddressStreetName !== null) {
        addressComparison = a.AddressStreetName.localeCompare(b.AddressStreetName);
      } else {
        addressComparison = 0;
      }
      if (addressComparison !== 0) return addressComparison;   // Handle null values in StreetNumberMin: null values come after any non-null value
      if (a.StreetNumberMin === null && b.StreetNumberMin !== null) return 1;
      if (a.StreetNumberMin !== null && b.StreetNumberMin === null) return -1;
      if (a.StreetNumberMin === null && b.StreetNumberMin === null) return 0;

      let matchA = (a.StreetNumberMin || "").match(/(\d+)(\D*)/);
      let matchB = (b.StreetNumberMin || "").match(/(\d+)(\D*)/);
      let [numA, alphaA] = matchA ? matchA.slice(1) : ["", ""];
      let [numB, alphaB] = matchB ? matchB.slice(1) : ["", ""];


      // Convert `numA` and `numB` to strings and pad single digits with leading zeros before comparison
      numA = numA.padStart(2, '0');
      numB = numB.padStart(2, '0');

      // Compare numeric parts of StreetNumberMin
      if (numA !== numB) return parseInt(numA, 10) - parseInt(numB, 10);

      // If numeric parts are equal, compare non-numeric parts alphabetically for StreetNumberMin
      let alphaComparison = alphaA.localeCompare(alphaB);
      if (alphaComparison !== 0) return alphaComparison;

      // Compare CondoTypeID, handling null values
      if (a.CondoTypeID === null && b.CondoTypeID !== null) return 1;
      if (a.CondoTypeID !== null && b.CondoTypeID === null) return -1;
      if (a.CondoTypeID === null && b.CondoTypeID === null) return 0;

      // Sort CondoTypeID in descending order
      return b.CondoTypeID - a.CondoTypeID;
    });
    this.setPageDefaultValuesOnSortAndOffsetChange()

  }

  appendChildStratasAboveMasterStrataInTheList() {
    for (let i = 0; i < this.propertyList.length; i++) {

      // Check if CondoTypeID is 3 and if StrataProperties exist
      if ((this.propertyList[i]?.CondoTypeID === EnumCondoTypeName.Master || this.propertyList[i]?.CondoTypeID === EnumCondoTypeName.Master_Freehold) && this.propertyList[i].StrataProperties) {
        // Parse StrataProperties
        let strataProperties = JSON.parse(this.propertyList[i].StrataProperties);
        // Sort them by CondoUnit in ascending order
        strataProperties.sort((a, b) => {
          if (a.CondoUnit === null && b.CondoUnit === null) {
            return 0;
          } else if (a.CondoUnit === null) {
            return 1;
          } else if (b.CondoUnit === null) {
            return -1;
          } else {
            // Pad single-digit numbers with leading zeros before comparison
            const condoUnitA = a.CondoUnit.toString().padStart(2, '0');
            const condoUnitB = b.CondoUnit.toString().padStart(2, '0');
            // Convert padded CondoUnit to numbers before comparison
            return parseInt(condoUnitA) - parseInt(condoUnitB);
          }
        });

        // Append the sorted list above the current property
        this.propertyList.splice(i, 0, ...strataProperties);
        // Move the loop's index to the next position after the last inserted property
        i += strataProperties.length;

      }
    }
  }


  removeChildStrataFromListIfItsMasterStrataAlreadyExistsInList() {
    // Iterate over the properties
    for (let i = 0; i < this.propertyList.length; i++) {
      // Check if CondoTypeID is 2
      if (this.propertyList[i]?.CondoTypeID === EnumCondoTypeName.Strata || this.propertyList[i]?.CondoTypeID === EnumCondoTypeName.Child_Freehold) {
        // Check if it has a MasterPropertyID
        if (this.propertyList[i].MasterPropertyID !== null) {
          const masterPropertyId = this.propertyList[i].MasterPropertyID;
          // Find the index of the property with the matching MasterPropertyID
          const index = this.propertyList.findIndex(property => property.PropertyId === masterPropertyId);
          // If found, remove the property initially checked for CondoTypeID
          if (index !== -1) {
            this.propertyList.splice(i, 1);
            // Decrement i to account for the removed property
            i--;
            // check i is non-negative
            if (i < 0) {
              i = 0;
            }
          }
        }
      }
    }

  }

  getPropertyType() {
    const propertyType = this._lookupService.GetAllPropertyType();
    propertyType.subscribe(result => {
      if (!result.body.error) {
        this.propertyTypes = result.body.responseData;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.PropertyTypeList, value: this.propertyTypes });
      }
    });
  }

  getResearchers = () => {
    const arealyticsUsers = this.userService.getArealyticsUsers();
    arealyticsUsers.subscribe(result => {
      if (!result.body.error) {
        const data = result.body.responseData[0];
        this.researchersList = data.filter(person => person.RoleID == UserRoles.Research_Analyst);
        this.researchersList.forEach(person => person.Fullname = `${person.FirstName} ${person.LastName}`);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.ResearchersList, value: this.researchersList });
      }
    });
  }

  getAuditStatuses = () => {
    const auditStatus = this._lookupService.GetPropertyAuditStatuses();
    auditStatus.subscribe(result => {
      if (!result.body.error) {
        const data = result.body.responseData[0];
        this.auditStatusList = data;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.AuditStatusList, value: this.auditStatusList });
      }
    })
  }

  getStateList() {
    const response_States = this._lookupService.GetStateListByCountryId(this.CountryId);
    response_States.subscribe(result => {
      if (!result.body.error) {
        this.states = result.body.responseData.StateList;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.StateList, value: this.states });
      }
    });
  }

  getResearchStatuses() {
    const researchStatus = this._lookupService.GetAllPropertyResearchType();
    researchStatus.subscribe(result => {
      if (!result.body.error) {
        this.researchTypes = result.body.responseData;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.ResearchStatusesList, value: this.researchTypes });
      }
    });
  }

  getCondoTypes() {
    const response_condoTypes = this._propertyService.GetAllCondoType();
    response_condoTypes.subscribe(result => {
      if (!result.body.error) {
        this.condoTypes = result.body.responseData;
        this._sharedDataService.condoTypeList = this.condoTypes;
      }
      else {
        this._sharedDataService.condoTypeList = null;
      }
    });
  }

  clearPropertyResultsFromSessionStorage() {
    const KeysToRemoveFromSS = [SessionStorageKeys.IsNavigationFromSearch,
    SessionStorageKeys.ActiveGridRowNumber,
    SessionStorageKeys.PropertyId,
    SessionStorageKeys.SelectedPropertyPos,
    SessionStorageKeys.SearchResultsPageDetails,
    SessionStorageKeys.SearchResults,
    SessionStorageKeys.NavigationPreference,
    ];
    clearSessionStorage(KeysToRemoveFromSS);
  }

  clearSearchModuleData(){
    const metaDataKeysToDelete = [
      MetaDataCollectionKeys.PropertyTypeList,
      MetaDataCollectionKeys.ResearchStatusesList,
      MetaDataCollectionKeys.StateList,
      MetaDataCollectionKeys.MultiFloors
    ];
    metaDataKeysToDelete.forEach(key => {
      this.metaDataIndexedDBService.deleteDataFromMetaData(key);
    });
    this.clearPropertyResultsFromSessionStorage();
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
  }

  navigateToMapSearch(isMapSearch = 'true') {
    this.clearSearchModuleData();
    sessionStorage.removeItem(SessionStorageKeys.LastVisitedStrataProperty);
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    sessionStorage.setItem(SessionStorageKeys.IsFromMapSearch, isMapSearch);
    this.router.navigate(['/expressmapsearch']);
  }

  backToSearch() {
    this.clearPropertyResultsFromSessionStorage();
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.PropertyList);
    if (sessionStorage.getItem(SessionStorageKeys.SearchCriteria)) {
      this.restoreSearchCriteriaFromCache();
    }
    this.currentPagePropertyList = [];
    this.searchPropertyFields = true;
    this.searchPropertyTable = false;
  }



  getPropertyList() {
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    sessionStorage.setItem(SessionStorageKeys.SearchCriteria, JSON.stringify(this.searchCriteria));
    const offset = parseInt(this.searchCriteria.FrontEndOffsetValue);
    if (this.propertyList) {
      this.propertyCount = this.propertyList.length;
      this.totalRecords = this.propertyCount;
    }
    this.propertypager = this.pagerService.getPager(this.propertyCount, this.searchCriteria.FrontEndStartingIndex, offset);
    this.currentPagePropertyList = this.propertyList.slice(this.propertypager.startIndex, this.propertypager.endIndex + 1);
    const propertySearchResult = new PropertySearchResult();
    propertySearchResult.PropertyCount = this.propertyCount;
    propertySearchResult.Pager = this.propertypager;
    propertySearchResult.CurrentPagePropertyList = this.currentPagePropertyList;
    sessionStorage.setItem(SessionStorageKeys.SearchResults, JSON.stringify(propertySearchResult));
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.PropertyList, value: { propertyList: this.propertyList, propertyListCopy: this.propertyListCopy } });
    this.scrollToRow(0);
  }



  selectAll(event) {
    if (event && event.target) {
      this.currentPagePropertyList.forEach(x => x.IsSelected = event.target.checked);
      this.IsSelectAll = event.target.checked;
    }
  }


  dynamicIntegerSort(Property: string, sortDirection: string) {
    let sortOrder: number;
    sortOrder = sortDirection === 'Ascending' ? 1 : -1;
    return function (a, b) {
      const result = (getInt(a[Property]) < getInt(b[Property])) ? -1 : (getInt(a[Property]) > getInt(b[Property])) ? 1 : 0;
      return result * sortOrder;
    };
  }

  dynamicSort(Property: string, sortDirection: string) {
    let sortOrder = 1;
    sortOrder = sortDirection === 'Ascending' ? 1 : -1;
    return function (a, b) {
      const result = (TryParseInt(a[Property], a[Property]) < TryParseInt(b[Property], b[Property]))
        ? -1 : (TryParseInt(a[Property], a[Property]) > TryParseInt(b[Property], b[Property])) ? 1 : 0;
      return result * sortOrder;
    };
  }

  dynamicAlphaNumericSort(property: string, sortDirection: string) {
    let sortOrder = sortDirection === 'Ascending' ? 1 : -1;
    return function (a, b) {
      const valueA = a[property];
      const valueB = b[property];
      // Function to extract numeric and non-numeric parts from string
      const extractParts = (value) => {
        const numPart = parseInt(value);
        return isNaN(numPart) ? [NaN, value] : [numPart, value.replace(numPart.toString(), '')];
      };
      const [numA, alphaA] = extractParts(valueA);
      const [numB, alphaB] = extractParts(valueB);
      // Compare numeric parts
      if (numA !== numB) {
        return (numA < numB) ? -1 * sortOrder : 1 * sortOrder;
      }
      // Compare alphabetic parts
      return alphaA.localeCompare(alphaB) * sortOrder;
    };
  }

  sortProperty(sortParam, sortOrder) {
    this.propertyList = this.propertyListCopy.slice();
    this.sortCriteria.SortOrder = sortOrder;
    this.sortCriteria.SortParam = sortParam;
    sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
    if (StringValuedSortParams.includes(sortParam)) {
      this.propertyList = this.propertyList.sort(this.dynamicSort(sortParam, sortOrder));
    } else if (sortParam === 'StreetNumberMin' || sortParam === 'StreetNumberMax') {
      this.propertyList = this.propertyList.sort(this.dynamicAlphaNumericSort(sortParam, sortOrder));
    }
    else {
      this.propertyList = this.propertyList.sort(this.dynamicIntegerSort(sortParam, sortOrder));
    }
    this.setPageDefaultValuesOnSortAndOffsetChange();
  }


  setPageDefaultValuesOnSortAndOffsetChange() {
    this.appendChildStratasAboveMasterStrataInTheList();
    this.currentPage = 1;
    this.searchCriteria.FrontEndStartingIndex = 1;
    this.getPropertyList();
  }



  onOffsetChange() {
    this.currentPage = 1;
    this.searchCriteria.FrontEndStartingIndex = 1;
    this.getPropertyList();
  }

  setPropertyPage(page: number) {
    this.currentPage = page;
    this.searchCriteria.FrontEndStartingIndex = page;
    this.getPropertyList();
  }

  scrollToRow(rowIndex: number): void {
    const instance = this;
    //  Use nativeElement to access the DOM element
    setTimeout(() => {
      const rowElement = instance.dataTable.el.nativeElement.getElementsByClassName(`row-${rowIndex}`)[0];
      if (rowElement) {
        rowElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 500);
  }

  getClassName(index) {
    return `row-${index}`;
  }

  onDateChange() {
    const minDate = this.parseDate(this.searchCriteria.LastReviewedDateMinFormatted);
    const maxDate = this.parseDate(this.searchCriteria.LastReviewedDateMaxFormatted);
    // Validate date range
    if(minDate && maxDate){
      this.reviewedDateError = !!(minDate > maxDate);
    }else{
      this.reviewedDateError = false;
    }
  }
  
  parseDate(date: any): Date | null {
    if (date && date.singleDate && date.singleDate.jsDate) {
      return new Date(date.singleDate.jsDate);
    }
    return null;
  }
  

  clearDate(field: 'min' | 'max'): void {
    if (field === 'min') {
      this.searchCriteria.LastReviewedDateMinFormatted = null;
    } else if (field === 'max') {
      this.searchCriteria.LastReviewedDateMaxFormatted = null;
    }
    this.onDateChange(); // Revalidate dates
  }

  getColumnWidth(header: string): string {
    switch (header) {
      case 'Number': return '80px';
      case 'Property ID': return '100px';
      case 'Zip Code': return '60px';
      case 'Street Number Min': return '80px';
      case 'Street Number Max': return '80px';
      case 'Street Name': return '200px';
      case 'City': return '180px';
      case 'General Use': return '140px';
      case 'Specific Use': return '200px';
      case 'Status': return '180px';
      case 'Record Type': return '105px';
      case 'Unit': return '55px';
      case 'Skip': return '55px';
      case 'Comments': return '150px';
      case 'Audit Status': return '150px';
      default: return '120px';
    }
  }
  getScrollHeight() {
    return this.currentPagePropertyList?.length > 15 ? '70vh' : 'auto';
  }
}
