import { Component, Input, OnInit } from '@angular/core';
import { Property } from '../../models/Property';
import { PropertyLocation } from '../../models/PropertyLocation';

@Component({
  selector: 'app-legal-information',
  templateUrl: './legal-information.component.html',
  styleUrls: ['./legal-information.component.css']
})
export class LegalInformationComponent implements OnInit {
@Input() property = new Property();
@Input() propertyLocation = new PropertyLocation();
@Input() locationDetailsForm;
  constructor() { }

  ngOnInit(): void {
  }

}
