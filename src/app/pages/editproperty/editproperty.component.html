<ng-container *ngIf="showParcelWindow && !isAnExistingProperty">
  <app-select-parcels-for-property (closeParcelWindow)="closeParcelWindow($event)"
    (addPropertyFromParcels)="addPropertyFromParcels($event)" (addNewProperty)="addNewProperty($event)"
    [parcelInformation]="parcelInformation" [hasNoExistingParcelInTileLayer]="hasNoExistingParcelInTileLayer"
    [selectedLatLng]="{Latitude: initialDetails.latLng.Latitude, Longitude: initialDetails.latLng.Longitude}"></app-select-parcels-for-property>
</ng-container>
<div class="map-main mt-3">
  <div class="details-wrapper" *ngIf="isAnExistingProperty">Last Modified By<b>&nbsp;{{property?.PropertyModifiedByName}}&nbsp;</b>on<b>
    &nbsp;{{property.PropertyModifiedDate | date:dateFormat}}&nbsp;</b> </div>
  <div class="col-md-12 no-padding-right">
    <!-- Address details -->
    <header class="header-container stickyTop pt-2" id="myHeader">
    
      <div class="header-details">
        <h4><span *ngIf="currentPropertyRowNumber && isNavigationFromSearch"> {{currentPropertyRowNumber}} - </span>
          {{ (displayPropertyId>0) ? 'Edit' : 'Add' }} Property
          <span *ngIf="displayPropertyId>0"> - {{displayPropertyId}}</span>
          <br />
          <div>
            <div *ngIf="!!initialDetails.address" class="head-address">
              <b> Address:</b> {{initialDetails.address}}
            </div>
            <span *ngIf="!!initialDetails.latLng" class="head-lat">
              <b> Lat:</b> {{initialDetails.latLng.Latitude}}
              <b>Long:</b> {{initialDetails.latLng.Longitude}}
            </span>
          </div>
        </h4>
      </div>
      <div class="buttons-wrapper">
        <div class="save-cancel-wrapper">
          <input type="button" class="btn btn-primary" value="Save" (click)="propertySave(false)">
          <input *ngIf="initialDetails.propertyId && roleID == userRoles.Auditor" type="button" class="btn btn-primary"
            value="Next" (click)="onNext(true)">
          <input type="button" class="btn btn-danger" value="Cancel" (click)="onCancel()">
        </div>
        <div *ngIf="isAnExistingProperty">
          <input type="button" class="btn btn-primary" value="Save and Next"
            [class.save-and-next-btn]="roleID === userRoles.Auditor"
            [class.save-and-nxt-button]="roleID !== userRoles.Auditor" (click)="propertySave(true)">
        </div>
      </div>
    </header>

    <app-update-sold-sqm *ngIf="showUpdateSoldSqmModal"
    [property]="property"
    [sales]="sales"
    [displayPropertyId]="displayPropertyId"
    (close)="closeSoldSqmModal()"
    (continue)="saveSoldSqm()">
  </app-update-sold-sqm>
    <form [formGroup]="propertyForm" class="form-align mt-3 dropdown-container">
    
      <!-- Skip button -->
      <div class="col-sm-1 skip-button-position"
        [ngStyle]="{'top': initialDetails.address ? '36px' : '14px','right': initialDetails.address ? '150px' : '200px'}">
        <input formControlName="IsSkipped" type="checkbox" [(ngModel)]="IsSkipped" />Skip
      </div>
    
      <div class="row">
        <!-- Property Use , Floors , Bldgsqm and Record type -->
        <div class="col-md-12">
          <div class="row">
            <div class="col-md-4 position-relative">
              <div id="propertyUse" class="form-group row">
                <div class="col-md-12 no-padding">
                  <div class="form-group row">
                    <label class="col-md-12 form-control-label default-property-use" for="text-input">Default Property Use</label>
                    <div class="default-property-use">
                      <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                        <input type="radio" id="propertyType_{{i}}" name="PropertyTypeName" [value]="useType.UseTypeID"
                          [(ngModel)]="selectedUseTypeID" [ngModelOptions]="{ standalone : true }"
                          (change)="onSelectUseTypeButton(useType);getSelectedValue('UseTypeID', useType.UseTypeID, 'UseTypeName')">
                        <label for="propertyType_{{i}}" title="{{useType.UseTypeName}}"
                          class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer" [ngClass]="{'blue' : useType.UseTypeID == '5',
                                      'red' : useType.UseTypeID == '3',
                                      'orange' : useType.UseTypeID == '2',
                                      'brown' : useType.UseTypeID == '4',
                                      'magenta' : useType.UseTypeID == '9',
                                      'green': useType.UseTypeID == '7',
                                      'ash': useType.UseTypeID == '12',
                                    'blueCheckedLabel': (selectedUseTypeID == '5' && useType.UseTypeID == '5') ,
                                      'redCheckedLabel': (selectedUseTypeID == '3' && useType.UseTypeID == '3'),
                                      'orangeCheckedLabel': (selectedUseTypeID == '2' && useType.UseTypeID == '2'),
                                      'brownCheckedLabel': (selectedUseTypeID == '4' && useType.UseTypeID == '4'), 
                                      'magentaCheckedLabel': (selectedUseTypeID == '9' && useType.UseTypeID == '9'),
                                      'greenCheckedLabel': (selectedUseTypeID == '7' && useType.UseTypeID == '7'),
                                      'ashCheckedLabel': (selectedUseTypeID == '12' && useType.UseTypeID == '12')
                                    }">{{useType.UseTypeLabel}}</label>
                      </div>
    
    
                    </div>
                    <div class="col-md-12" *ngIf="false">
                      <ng-select formControlName="PropertyTypeName" [items]="propertyTypes" [virtualScroll]="true"
                        bindLabel="UseTypeName" bindValue="UseTypeID" placeholder="--Select--"
                        [(ngModel)]="property.UseTypeID"
                        [ngClass]="{'error-field':(!propertyForm.controls['PropertyTypeName'].valid && isInitialProperty)}"
                        (change)="changePropertyUse($event);getSelectedValue('UseTypeID', $event, 'UseTypeName')">
                      </ng-select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
              <div class="col-md-3 position-relative" *ngIf="(propertyLocation.Condo !== EnumCondoTypeNames.Master || (propertyLocation.Condo == EnumCondoTypeNames.Master && isAverageEstimationEnabled)) && property.UseTypeID !== propertyTypeValues.Land">
                <div class="form-group row floorBox">
                  <label class="col-md-12 form-control-label no-padding ml-1" for="text-input">Total Floors
                    <span *ngIf="!propertyForm.controls['Floors'].valid" class="mandatory">*</span>
                  </label>
                  <ng-container *ngIf="propertyLocation.Condo !== EnumCondoTypeNames.Master_Freehold; else masterRolloutInputFloors">
                    <div class="radio-toolbar no-padding">
                      <input type="radio" id="floor1" name="floor" [value]="1" [(ngModel)]="selectedFloor"
                        [ngModelOptions]="{ standalone : true }" (ngModelChange)="onSelectFloor();">
                      <label for="floor1"
                        class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer floor-radio"
                        [ngClass]="{'blueCheckedLabel' : property.Floors == '1'}">1</label>
                      <input type="radio" class="fl-lbl" id="floor2" name="floor" [value]="2" [(ngModel)]="selectedFloor"
                        [ngModelOptions]="{ standalone : true }" (ngModelChange)="onSelectFloor();">
                      <label for="floor2"
                        class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer floor-radio"
                        [ngClass]="{'blueCheckedLabel' : property.Floors == '2'}">2</label>
                      <input type="radio" class="fl-lbl" id="floor3" name="floor" [value]="3" [(ngModel)]="selectedFloor"
                        [ngModelOptions]="{ standalone : true }" (ngModelChange)="onSelectFloor();">
                      <label for="floor3"
                        class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer floor-radio"
                        [ngClass]="{'blueCheckedLabel' : property.Floors == '3'}">3</label>
              
              
                      <input type="number" min="0" max="999" inputmode="" formControlName="Floors"
                        class="fl-lbl form-control maxnumbervalidation floor-txt" [(ngModel)]="property.Floors"
                        (change)="checkValidations();onSelectFloor(true);">
                    </div>
                  </ng-container>
                  <ng-template #masterRolloutInputFloors>
                    <input type="text" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.Floors" readonly>
                  </ng-template>
                </div>
              </div>
            
            <div class="col-md-2 position-relative"
              *ngIf="!showContributedGBA && !footPrintNotAvailable && propertyLocation.Condo !== 4 && property.UseTypeID !== propertyTypeValues.Land">
              <div class="form-group row">
                <label class="col-md-12 form-control-label building-label" for="text-input">Building {{UnitDisplayTextSize}}
                  <span
                    *ngIf="(propertyLocation.Condo !== EnumCondoTypeNames.Master || (propertyLocation.Condo == EnumCondoTypeNames.Master && isAverageEstimationEnabled)) && !propertyForm.controls['BuildingSF'].valid"
                    class="mandatory">*</span>
                </label>
                <div class="col-md-12 position-relative">
                  <input type="text" maxlength="11" numericOnly allowNegative="false" allowDecimal="true"
                    class="form-control maxnumbervalidation buildingInput" formControlName="BuildingSF"
                    (paste)="validatePasteInput($event, true)"
                    (keypress)="validateIntegerInput($event, true)"
                    [(ngModel)]="property.BuildingSF" (blur)="ValidateBuildingSize();validateSizeFieldsAgainstBuildingSize()"
                    [ngClass]="{'error-field':(propertyLocation.Condo !== EnumCondoTypeNames.Master && !propertyForm.controls['BuildingSF'].valid)}"
                    [readonly]="propertyLocation.Condo === EnumCondoTypeNames.Strata && this.isAverageEstimationEnabledForMaster">
                  <div
                    *ngIf="propertyForm.controls['BuildingSF'].invalid && (propertyForm.controls['BuildingSF'].dirty || propertyForm.controls['BuildingSF'].touched)">
                    <div *ngIf="propertyForm.controls['BuildingSF'].errors?.numeric && showBuildingSizeValidationError()" class="error-message">Please enter a
                      valid number.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-2 position-relative" *ngIf="footPrintNotAvailable && propertyLocation.Condo !== 4 && propertyLocation.Condo !== 3  && property.UseTypeID !== propertyTypeValues.Land">
              <div class="form-group row">
                <label class="col-md-12 form-control-label building-label" for="text-input">Contributed GBA
                  <span *ngIf="footPrintNotAvailable" class="mandatory">*</span>
                </label>
                <div class="col-md-12 position-relative">
                  <input type="text" maxlength="11" numericOnly allowNegative="false" allowDecimal="true"
                    class="form-control maxnumbervalidation buildingInput" formControlName="ContributedGBA_SF"
                    [(ngModel)]="property.ContributedGBA_SF" readonly>
                </div>
              </div>
            </div>
            <div class="col-md-2 position-relative" *ngIf="showContributedGBA && (propertyLocation.Condo == 4 || propertyLocation.Condo == 3)">
              <div class="form-group row">
                <label class="col-md-12 form-control-label building-label" for="text-input">Contributed GBA</label>
                <div class="col-md-12 position-relative">
                  <input type="text" maxlength="11" numericOnly allowNegative="false" allowDecimal="true"
                    class="form-control maxnumbervalidation buildingInput" [value]="property.ContributedGBA_SF" readonly>
                </div>
              </div>
            </div>
            <div class="col-md-3 position-relative" *ngIf="property.UseTypeID !== propertyTypeValues.Land">
    
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Record Type
                </label>
                <div class="col-md-12" *ngIf="!disableStrataBtn">
                  <ng-select formControlName="Condo" [items]="condos" [virtualScroll]="true" bindLabel="CondoTypeName"
                    bindValue="CondoTypeID" placeholder="--Select--" [(ngModel)]="propertyLocation.Condo"
                    (change)="getSelectedValue('Condo',$event,'CondoTypeName');showStrataConfirmation();onStrataChange();"
                    [ngClass]="{'error-field':(!propertyForm?.controls['Condo']?.valid )}"></ng-select>
                  <div class="col-md-12" *ngIf="disableStrataBtn">
                  </div>
                  <span class="noteText" *ngIf="showMasterStrataAlert">
                    <span *ngIf="propertyLocation.Condo == EnumCondoTypeNames.Master">Please remove the child stratas to change the type</span>
                    <span *ngIf="propertyLocation.Condo == EnumCondoTypeNames.Master_Freehold">Please remove the child freehold to change the type</span>
                  </span>
                </div>
                <div class="col-md-12" *ngIf="disableStrataBtn">
                  <input [disabled]="disableStrataBtn" [value]="isFreehold ? 'Child Freehold' : 'Strata'"
                    class="form-control">
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-5 position-relative avg-estimation-wrapper" *ngIf="propertyLocation.Condo == 3 && avgEstimationFF">
            <div class="form-group row">
              <label class="col-md-7 form-control-label building-label" for="text-input">Average Estimation Method</label>
              <div class="col-md-5 position-relative avg-estimation-checkbox-wrapper">
                <input type="checkbox" formControlName="IsAverageEstimationEnabled" [(ngModel)]="isAverageEstimationEnabled" [attr.disabled]="isAnExistingProperty ? '' : null"/>
              </div>
            </div>
          </div>
        </div>
    
        <!-- Strata related fields -->
        <div class="col-md-12" *ngIf="property.UseTypeID !== propertyTypeValues.Land">
          <div class="row">
            <!-- Condo Unit -->
            <div class="col-md-4 position-relative"
              *ngIf="(propertyLocation.Condo === 2 || propertyLocation.Condo === 5) && !isMultiStrata">
              <div class="form-group row">
                <div class="form-group row">
                  <label class="col-md-12 form-control-label" for="text-input"> {{getCondoType()}} Unit
                  </label>
                  <div class="col-md-12">
                    <input type="text" class="form-control" formControlName="CondoUnit"
                      [(ngModel)]="propertyLocation.CondoUnit"
                      [ngClass]="{'error-field':(!propertyForm.controls[PropertyFormControlsEnum.CondoUnit].valid && (propertyLocation.Condo === EnumCondoTypeNames.Strata || propertyLocation.Condo === EnumCondoTypeNames.Child_Freehold))}">
                  </div>
                </div>
    
    
              </div>
            </div>
    
            <div class="col-md-8">
              <div class="row">
                <!-- Strata or Freehold Range -->
                <div class="col-md-6 row" *ngIf="isMultiStrata" [formGroup]="rangeForm">
                  <div class="col-md-6 no-padding">
                    <label class="col-md-12 form-control-label" for="text-input"> {{isFreehold ? 'Start' : 'Min'}}
                    </label>
                    <div class="col-md-12">
                      <input type="number" class="form-control" formControlName="StrataMin" [(ngModel)]="pRange.StrataMin"
                        [ngClass]="{'error-field':((!rangeForm.controls['StrataMin'].valid)||StrataMinMaxError)}">
                    </div>
                  </div>
                  <div class="col-md-6 no-padding">
                    <label class="col-md-12 form-control-label" for="text-input"> {{isFreehold ? 'End' : 'Max'}}
                    </label>
                    <div class="col-md-12">
                      <input type="number" class="form-control" formControlName="StrataMax" [(ngModel)]="pRange.StrataMax"
                        [ngClass]="{'error-field':((!rangeForm.controls['StrataMax'].valid)||StrataMinMaxError)}">
                    </div>
                  </div>
                </div>
                <!-- Master Property For Strata -->
                <div class="col-md-6 " *ngIf="propertyLocation.Condo === 2">
                  <div class="form-group row">
                    <label class="col-md-12 form-control-label"> Master Property
                    </label>
                    <div class="col-md-12">
                      <div class="input-with-icon">
                        <i class="fa fa-search" aria-hidden="true" title="Find Property Record To Create Master Strata"
                          (click)="addProperty()"></i>
                        <input type="text" class="form-control background-white" formControlName="MasterPropertyId"
                          [(ngModel)]="propertyLocation.MasterPropertyId" readonly
                          [ngClass]="{'error-field':(!propertyForm.controls[PropertyFormControlsEnum.MasterPropertyId].valid && propertyLocation.Condo === EnumCondoTypeNames.Strata)}">
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Master Strata for Child Freehold -->
                <div class="col-md-6 " *ngIf="propertyLocation.Condo === 5">
                  <div class="form-group row">
                    <label class="col-md-12 form-control-label"> Master Freehold Property
                    </label>
                    <div class="col-md-12">
                      <div class="input-with-icon">
                        <i class="fa fa-search" aria-hidden="true" title="Find Property Record To Create Master Freehold"
                          (click)="addProperty(true)"></i>
                        <input type="text" class="form-control background-white" formControlName="MasterPropertyId"
                          [(ngModel)]="propertyLocation.MasterPropertyId"
                          [ngClass]="{'error-field':(!propertyForm.controls[PropertyFormControlsEnum.MasterPropertyId].valid && propertyLocation.Condo === EnumCondoTypeNames.Child_Freehold)}"
                          readonly>
                      </div>
                    </div>
                  </div>
                </div>
    
                <!-- Add and Select Bldgs buttons for Master Freehold -->
                  <div class="d-grid gap-2 d-md-block" *ngIf="propertyLocation.Condo === 4 && isAnExistingProperty" style="gap:4px">
                    <button (click)="addBldgs()" class="btn btn-sm btn-primary mr-2 add-bldgs">
                      <i class="fa fa-plus"></i> Add Bldgs
                    </button>
                    <button (click)="selectBuildings()" class="btn btn-sm btn-primary add-bldgs">
                      Select Bldgs
                    </button>
                  </div>
              </div>
            </div>
          </div>
        </div>
    
    
        <!-- Construction type,status and Zoning code -->
        <div class="col-md-12" *ngIf="property.UseTypeID !== propertyTypeValues.Land">
          <div class="row">
            <div class="col-md-4 position-relative">
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Construction Type </label>
                <div class="col-md-12">
                  <ng-select  *ngIf="propertyLocation.Condo !== EnumCondoTypeNames.Master_Freehold" formControlName="ConstructionType" [items]="constructTypes" [virtualScroll]="true"
                    bindLabel="ConstructionTypeName" bindValue="ConstructionTypeID" placeholder="--Select--"
                    [(ngModel)]="property.ConstructionTypeID" name="ConstructionType" id="ConstructionType"
                    (change)="getSelectedValue('ConstructionTypeID',$event,'ConstructionTypeName')"
                    [ngClass]="{'error-field':(!propertyForm.controls['ConstructionType'].valid )}">
                  </ng-select>
                  <input *ngIf="propertyLocation.Condo === EnumCondoTypeNames.Master_Freehold" readonly [value]="rollupMasterFreeholdFieldsObject?.ConstructionTypeName" class="form-control" type="text">
                </div>
              </div>
            </div>
    
            <div class="col-md-4 position-relative">
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Construction Status
                </label>
                <div class="col-md-12">
                  <ng-select *ngIf="propertyLocation.Condo !== EnumCondoTypeNames.Master_Freehold" formControlName="ConstructionStatus" [items]="constructStatuses" [virtualScroll]="true"
                    bindLabel="ConstructionStatusName" bindValue="ConstructionStatusID" placeholder="--Select--"
                    [(ngModel)]="property.ConstructionStatusID"
                    (change)="getSelectedValue('ConstructionStatusID',$event,'ConstructionStatusName')"
                    [ngClass]="{'error-field':(!propertyForm.controls['ConstructionStatus'].valid )}"></ng-select>
                    <input type="text" *ngIf="propertyLocation.Condo === EnumCondoTypeNames.Master_Freehold" [value]="rollupMasterFreeholdFieldsObject?.ConstructionStatusName"  class="form-control" readonly>
                </div>
              </div>
            </div>
    
            <div class="col-md-4 position-relative">
    
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Zoning Code</label>
                <div class="col-md-12">
                  <ng-container *ngIf="propertyLocation.Condo !== EnumCondoTypeNames.Master_Freehold; else masterRolloutInputZoningCode">
                    <input type="text" class="form-control" formControlName="ZoningCode" name="ZoningCode" id="ZoningCode"
                      [(ngModel)]="property.ZoningCode">
                  </ng-container>
                  <ng-template #masterRolloutInputZoningCode>
                    <input type="text" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.ZoningCode" readonly>
                  </ng-template>
                </div>
              </div>
            </div>
    
          </div>
        </div>
    
        <div class="footprint-btn-wrapper" *ngIf="!isMultiStrata">
          <div class="add-floor-button" *ngIf="initialDetails.latLng && (propertyLocation.Condo !== 3 || (propertyLocation.Condo == 3 && isAverageEstimationEnabled)) && this.propertyLocation.Condo !== 4 && property.UseTypeID !== propertyTypeValues.Land">
            <ng-container *ngIf="property.PropertyID && propertyCopy.HasNoBuildingFootprints && propertyCopy.HasNoBuildingFootprints == property.HasNoBuildingFootprints; else showButton">
              <div class="no-footprint-label" >Footprint is Not Available</div>
            </ng-container>
            <ng-template #showButton>
              <button type="button" class="btn btn-primary" (click)="footPrintNotAvailablePopup()" [disabled]="isFootprintNotAvailableDisabled()">Footprint Not Available</button>
            </ng-template>
          </div>
          <div class="add-floor-button" *ngIf="initialDetails.latLng && (propertyLocation.Condo !== 3 || (propertyLocation.Condo == 3 && isAverageEstimationEnabled)) && this.propertyLocation.Condo !== 4 && property.UseTypeID !== propertyTypeValues.Land && !isAverageEstimationEnabledForMaster">
            <button type="button" class="btn btn-info add-floor-btn" [disabled]="isAddPolygonButtonDisabled"
                (click)="addfloor()">Add Polygon</button>
          </div>
        </div>
        <!-- Polygons Tabs -->
        <div class="row expandBox polygons-accordion"
          *ngIf="(propertyLocation.Condo !== 3 || (propertyLocation.Condo == 3 && isAverageEstimationEnabled)) && propertyLocation.Condo !== 4 && initialDetails.latLng && !isMultiStrata && property.UseTypeID !== propertyTypeValues.Land && !isAverageEstimationEnabledForMaster">
          <div class="col-md-12 p-0">
            <button type="button" class="searchBtnActions selectExpandToggle" data-toggle="collapse"
              data-target="#PropertyPolygons" [attr.aria-expanded]="!isPolygonCollapsed" (click)="expandCollapsePolygons()">
              <i class="fas fa-draw-polygon" style="font-size: 18px;"></i>Property Floor/Section Polygons
            </button>
          </div>
    
          <div id="PropertyPolygons" [ngClass]="{'show': !isPolygonCollapsed}" class="collapse mb-2 mt-2 pl-2 pr-2">
            <hr class="mt-0">
            
            <div style="flex-direction: column;" *ngIf="multifloors.length > 0">
              <div *ngFor="let item of multifloors; let floorIndex=index">
                <div>
                  <div class="icons-style">
                    <button type="button" class="btn btn-info copy-floor-btn" [disabled]="isAddPolygonButtonDisabled"
                  (click)="onCopyPolygonClicked(item)">Copy Polygon</button>
                    <span (click)="deleteFloorConfirmation(item, floorIndex)"><i
                        class="fas fa-trash deleteFloor"></i></span>
                  </div>
                  <div class="col" [ngClass]="{'highlight': !(item.floorSize && item.minFloor && item.maxFloor)}">
                    <div class="floor-data">
                      <div class="col-md-4 specific-use-wrapper">
                        <div id="propertyUse" class="row">
                          <div class="col-md-12 no-padding">
                            <div class="form-group row">
                              <label class="col-md-12 form-control-label" style="margin-left: 15px;"
                                for="text-input">Property Use
                              </label>
                              <div class="default-property-use" style="margin-left: 18px;">
                                <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                                  <input type="radio" id="propertyType2_{{i}}_{{floorIndex}}" name="specificUseName"
                                    [value]="useType.UseTypeID"
                                    (change)="onSpecificUseChange(item, floorIndex, 'useType.UseTypeID')"
                                    [(ngModel)]="item.specificUse" [ngModelOptions]="{ standalone : true }"
                                    *ngIf="useType.UseTypeID != '7'">
                                  <label for="propertyType2_{{i}}_{{floorIndex}}" title="{{useType.UseTypeName}}"
                                    *ngIf="useType.UseTypeID != '7'"
                                    class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer"
                                    [ngClass]="{'blue' : useType.UseTypeID == '5',
                                                'red' : useType.UseTypeID == '3',   
                                                'orange' : useType.UseTypeID == '2',
                                                'brown' : useType.UseTypeID == '4',
                                                'magenta' : useType.UseTypeID == '9',
                                                'green': useType.UseTypeID == '7',
                                                'ash': useType.UseTypeID == '12',
                                              'blueCheckedLabel': (item.specificUse == '5' && useType.UseTypeID == '5') ,
                                                'redCheckedLabel': (item.specificUse == '3' && useType.UseTypeID == '3'),
                                                'orangeCheckedLabel': (item.specificUse == '2' && useType.UseTypeID == '2'),
                                                'brownCheckedLabel': (item.specificUse == '4' && useType.UseTypeID == '4'), 
                                                'magentaCheckedLabel': (item.specificUse == '9' && useType.UseTypeID == '9'),
                                                'greenCheckedLabel': (item.specificUse == '7' && useType.UseTypeID == '7'),
                                                'ashCheckedLabel': (item.specificUse == '12' && useType.UseTypeID == '12')
                                              }">{{useType.UseTypeLabel}}</label>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3 form-group">
                        <label class="form-control-label no-padding">Additional Use</label>
                        <ng-select [items]="additionalUseTypes" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="UseTypeLabel"
                          bindValue="UseTypeID" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="item.additionalUse"
                          (change)="onChangeAdditionalUse(floorIndex)">
                        </ng-select>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="form-group row">
                          <label class="col-md-9 form-control-label" for="text-input">Floor Size
                          </label>
                          <div class="col-md-9 position-relative">
                            <input type="number" [(ngModel)]="item.floorSize" [ngModelOptions]="{standalone: true}"
                              class="form-control maxnumbervalidation floorsizeinput" [disabled]="true">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row des-floor">
                      <div class="col-md-4 description form-group">
                        <label class="form-control-label no-padding" for="text-input"> Description </label>
                        <input type="text" class="form-control description" [(ngModel)]="item.description"
                          [ngModelOptions]="{ standalone: 'true' }"
                          (change)="onChangeDescription(item, floorIndex, 'description')">
                      </div>
                      <div class="col-md-3">
                        <div class="form-group row d-flex justify-content-around" style="display: flex; align-items: center;">
                          <div class="col-md-3" style="display: flex; flex-direction: column; align-items: center;">
                            <label class="form-control-label"> Min </label>
                            <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px;" bindLabel="key"
                              bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="item.minFloor"
                              (change)="onChangeFloorOrUseType(item, floorIndex, 'minFloor')">
                            </ng-select>
                      
                          </div>
                          <div class="col-md-3" style="display: flex; flex-direction: column; align-items: center; ">
                            <label class="form-control-label"> Max </label>
                            <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="key"
                              bindValue="value" placeholder="" [ngModelOptions]="{ standalone: 'true' }" [(ngModel)]="item.maxFloor"
                              (change)="onChangeFloorOrUseType(item, floorIndex, 'maxFloor')">
                            </ng-select>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3 floorcount form-group">
                        <label class="form-control-label" for="text-input"> Floor Count </label>
                        <input type="number" class="form-control floor-count" [(ngModel)]="item.floorCount"
                          [ngModelOptions]="{standalone: true}" [disabled]="true">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="clearfix" *ngIf="property.UseTypeID !== propertyTypeValues.Land"></div>
    
        <!-- Aggreagate Parcel Size and Parcel count for Land use -->
        <div class="col-md-12">
          <div class="row">
    
            <div class="col-md-4 position-relative">
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Parcel(s) count
                </label>
                <div class="col-md-12">
                  <input formControlName="ParcelCount" class="form-control" readonly [(ngModel)]="parcelCount">
                </div>
              </div>
            </div>
    
            <div *ngIf="!propertyLocation.Condo ||[EnumCondoTypeNames.Master, EnumCondoTypeNames.Master_Freehold, EnumCondoTypeNames.NotStrata].includes(propertyLocation.Condo) || property.UseTypeID === propertyTypeValues.Land"  class="col-md-4  position-relative">
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Aggregate Size of Parcel(s)
                </label>
                <div class="col-md-12">
                  <input formControlName="AggregateParcelSize" class="form-control" readOnly
                    [(ngModel)]="aggregateParcelSize">
                </div>
              </div>
            </div>
    
            <div *ngIf="!propertyLocation.Condo || [EnumCondoTypeNames.Master, EnumCondoTypeNames.Master_Freehold, EnumCondoTypeNames.NotStrata].includes(propertyLocation.Condo) || property.UseTypeID === propertyTypeValues.Land" class="col-md-4 position-relative">
              <div class="form-group row">
                <label class="col-md-12 form-control-label" for="text-input">Lot Size Source
                  <span *ngIf="!propertyForm.controls['LotSizeSourceID'].valid" class="mandatory">*</span>
                </label>
                <div class="col-md-12">
                  <ng-select formControlName="LotSizeSourceID" [items]="sizeSource" [virtualScroll]="true"
                    bindLabel="SizeSourceName" bindValue="SizeSourceID" placeholder="--Select--"
                    [(ngModel)]="property.LotSizeSourceID"
                    (change)="getSelectedValue('LotSizeSourceID',$event,'SizeSourceName')"
                    [ngClass]="{'error-field':(!propertyForm.controls['LotSizeSourceID']?.valid )}"></ng-select>
                </div>
              </div>
            </div>
          </div>
        </div>
    
        <!-- Parcel Tab -->
        <app-parcel-list [isAnExistingProperty]="isAnExistingProperty" [property]="property" [propertyCopy]="propertyCopy" [initialDetails]="initialDetails"></app-parcel-list>
    
        <!-- Reviewed button -->
        <div class="review-details-wrapper" *ngIf="isAnExistingProperty">
          <div *ngIf="property?.PropertyReviewedByName">Last Reviewed By<b>&nbsp;{{property?.PropertyReviewedByName}}&nbsp;</b>on<b>&nbsp;{{property?.LastReviewedDate | date:dateFormat}}&nbsp;</b></div>
          <button type="button" class="btn btn-primary" [ngClass]="{reviewed: property.IsReviewed === 1}" (click)="onReviewed()">Reviewed</button>
        </div>

          <div>
            <div>
              <div class="row mx-auto research-status" style="display: flex; justify-content: space-between; padding: 18px 15px 15px 0;">
                <ng-container *ngFor="let research of propertyResearchStatus">
                  <div *ngIf="isResearchVisible(research.PropertyResearchTypeID, property.PropertyID)">
                    <label title="{{research.PropertyResearchTypeName}}" class="research-name" style="margin-right: 10px;">
                      <img class="research-icon" [src]="research.ResearchStatusPin">
                      <input class="research-button" type="radio" name="research.PropertyResearchTypeName" [value]="research.PropertyResearchTypeID"
                        [(ngModel)]="selectedOption" [ngModelOptions]="{ standalone : true }" [disabled]="propertyLocation.Condo === 2"
                        (change)="onResearchStatusChange(research.PropertyResearchTypeID,research.PropertyResearchStatusID,$event)">
                      {{research.PropertyResearchTypeName}}
                    </label>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
    
          <div class="col-md-11" *ngIf="selectedOption == 5">
            <div class="row" style="display: flex; align-items: center; padding: 10px 0;">
              <div class="col-md-4 position-relative" style="padding-left: 0;">
                <label style="display: flex; align-items: center; gap: 10px;">
                  <input type="checkbox" formControlName="IsMultiplePolygonsNeeded"
                    [(ngModel)]="property.IsMultiplePolygonsNeeded" />
                  <label class="col-md-12 form-control-label" style="padding: 0px; margin-bottom: 0px;" for="text-input">Multiple
                    Polygons Needed
                  </label>
                </label>
              </div>
          
              <div class="col-md-4 position-relative" style="padding-left: 10px;">
                <label>
                  <textarea type="text" formControlName="NeedsResearchComments" [(ngModel)]="property.NeedsResearchComments" class="form-control"
                    placeholder="Comments" style="width: 300px;" >{{property.NeedsResearchComments}} </textarea>
                </label>
              </div>
          
            </div>
          </div>
      </div>
    </form>
  </div>
  <mat-tab-group [(selectedIndex)]="selectedTab" (selectedTabChange)="changeTab($event)" *ngIf="isAnExistingProperty">
  
    <mat-tab label="Property">
      <form [formGroup]="propertyForm" class="form-align mt-3">
  
        <!--Property Details Start-->
  
        <div class="form-group row expandBox">
          <div class="col-md-12 p-0">
            <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" data-target="#PropertyDetails" [attr.aria-expanded]="isPropertyDetailsExpanded" (click)="togglePropertyDetails()">
              <i class="fa fa-building"></i>
              {{property.UseTypeID == propertyTypeValues.Office ? 'Office' :
              property.UseTypeID == propertyTypeValues.Industrial ? 'Industrial' :
              property.UseTypeID == propertyTypeValues.Retail ? 'Retail':
              property.UseTypeID == propertyTypeValues.Land ? 'Land' :
              property.UseTypeID == propertyTypeValues.Apartments ? 'Apartments' :
              property.UseTypeID == propertyTypeValues.SpecialUse ? 'Special Use' : 'Property'}} Details
            </button>
            <div id="PropertyDetails" [ngClass]="{'show': !!isPropertyDetailsExpanded}" class="collapse mb-2 mt-2 pl-2 pr-2">
              <hr class="mt-0">
              <app-property-details [propertyForm]="propertyForm" [property]="property" [dataArray]="DataArray" [propertyCopy]="propertyCopy" [lookupDropdowns]="propertyLookups" [propertyStatus]="selectedOption" [condo]="propertyLocation?.Condo" [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"></app-property-details>
            </div>
            <!--Left-->
  
          </div>
        </div>
  
        <!--Property Details End-->
  
        <!-- Location details Start-->
  
        <app-location-details
          [propertyLocation]="propertyLocation"
          [locationDetailsForm]="propertyForm?.get('locationDetailsForm')"
          [quadrants]="quadrants"
          [streetMinMaxError]="streetMinMaxError"
          [streetPrefixes]="streetPrefixes"
          [streetSufixes]="streetSufixes"
          [dataArray]="DataArray"
          [cities]="cities"
          [states]="states"
          (changeMapLocation)="showRoofTopModal()"
          [propertyCopy]="propertyCopy"
          [propertyLocationCopy]="propertyLocationCopy"
          [counties]="counties"
          [countries]="countryList"
          [property]="property"
          [showMasterStrataAlert]="showMasterStrataAlert"
          (onChangeStrata)="showStrataConfirmation();onStrataChange();"
          (setAddressAsPropertyName)="addressAsPropertyName($event)"
          [propertyLookups]="propertyLookups"
        ></app-location-details>
  
        <!-- location details End -->
  
        <!-- Additional Address Start-->
  
        <!-- Additional Address Start-->

        <app-additional-address [property]="property" [propertyLocation]="propertyLocation"></app-additional-address>

        <!-- Additional Address End -->
  
        <!-- Additional Address End -->
  
        <!-- Allocations and Uses Start-->
  
        <div class="row expandBox" *ngIf="!isNewProperty">
          <div class="col-md-12 p-0">
            <button type="button" class="searchBtnActions selectMoreToggle" (click)="showAllocationsAndUses()" data-toggle="collapse" data-target="#AllocationsAndUses" aria-expanded='false'><i class="fa fa-map-marker" style="font-size: 1.5rem;"></i>Allocations and Uses
            </button>
          </div>
          <div id="AllocationsAndUses" class="collapse mb-2 mt-2 pl-2 pr-2" *ngIf="property.UseTypeID !== propertyTypeValues.Land">
            <hr class="mt-0">
            <app-property-allocation-details [UnitId]="UnitId" [metricUnit]="metricUnit" [UnitDisplayTextSize]="UnitDisplayTextSize" [propertyTypes]="propertyTypes" #propertyAllocation>
            </app-property-allocation-details>
            <app-additional-use-details [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject" [multifloors]="multifloors" [propertyTypes]="propertyTypes" [allspecificuses]="allspecificuses" [propertyLocation]="propertyLocation" (updateMultiFloors)="updateMultiFloors()" [propertyLocation]="propertyLocation"></app-additional-use-details>
          </div>
        </div>
  
        <!--Allocations and Uses End-->
  
        <!--Legal Information Start-->
  
        <div class="row expandBox">
          <div class="col-md-12 p-0">
            <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" data-target="#LegalInformation" aria-expanded='false'><i class="fa fa-gavel"></i>Legal Information
            </button>
          </div>
          <div id="LegalInformation" class="collapse mb-2 mt-2 pl-2 pr-2">
            <hr class="mt-0">
            <app-legal-information [property]="property" [propertyLocation]="propertyLocation" [locationDetailsForm]="propertyForm?.get('locationDetailsForm')"></app-legal-information>
          </div>
        </div>
  
        <!--Legal Information End-->
  

        <!-- Change-log -->
        <app-change-log [propertyId]="this.property?.PropertyID" [location]="location"></app-change-log>
  
        <div class="clearfix"></div>
      </form>
    </mat-tab>
    <mat-tab *ngIf="initialDetails.propertyId > 0" label="Media">
      <ng-container *ngIf="isMedia">
        <app-media [initialDetails]="initialDetails">
  
        </app-media>
      </ng-container>
    </mat-tab>
    <mat-tab *ngIf="initialDetails.propertyId > 0" label="Notes">
      <ng-container *ngIf="isNotes">
        <app-notes [initialDetails]="initialDetails">
  
        </app-notes>
      </ng-container>
    </mat-tab>
    <mat-tab *ngIf="showStrataOrFreeHoldTab(propertyLocation.Condo)" [label]="getStrataOrFreeholdTabText(propertyLocation.Condo)">
      <ng-container *ngIf="isStrata && !isFreeholdProp(propertyLocation.Condo)">
        <app-strata [initialDetails]="initialDetails" (showPropertyInfo)="showPropertySummary($event)" [property]="property" [selectedUseTypeID]="selectedUseTypeID" (addNewStrataUnit)="addNewStrataUnit($event)"></app-strata>
      </ng-container>
      <ng-container *ngIf="isFreeholdProp(propertyLocation.Condo)">
        <app-free-hold #freehold [initialDetails]="initialDetails" (showPropertyInfo)="showPropertySummary($event)" [property]="property" (addNewFreeholdUnit)="addNewFreeholdUnit($event)" [selectedUseTypeID]="selectedUseTypeID"></app-free-hold>
      </ng-container>
    </mat-tab>
  </mat-tab-group>

  <div class="backDropWrapper" *ngIf="redirectionLoader">
    <i class="spinRotate fa fa-spinner fa-spin"></i>
  </div>
</div>

<!-- Page modals -->

<!-- file upload modal -->
<div *ngIf="showFileUpload" class="upload-scroll">
  <imperium-modal [(visible)]="showFileUpload" [title]="mediaTitle" [size]="tenantModalSize"  [width]="'medium'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-file-upload-modal (onUploadEvent)="onFileUploaded($event)" [oddRow]="oddRow" [media]="files[0]"
        [initialDetails]="initialDetails" (onSave)="onUploadEvent($event)" (onClose)="closeMediaUploadModal($event)"></app-file-upload-modal>
    </ng-template>
  </imperium-modal>
</div>

<div *ngIf="isFullSizeView">
  <imperium-modal [(visible)]="isFullSizeView" [title]="" [size]="tenantModalSize" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-image-viewer-modal [imgUrls]="imageUrls" [(imgIndex)]="ImageIndex"></app-image-viewer-modal>
    </ng-template>
  </imperium-modal>
</div>

<div *ngIf="showNoteModal">
  <imperium-modal [(visible)]="showNoteModal" [title]="notesTitle" [size]="tenantModalSize" [width]="'medium'" [height]="'medium'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-add-note-modal [selectedNote]="selectedNote" [initialDetails]="initialDetails" (onClose)="closeNoteModal()">
      </app-add-note-modal>
    </ng-template>
  </imperium-modal>
</div>


<!--RoofTop Utility-->
<div *ngIf="showRoofTop">
  <imperium-modal  [(visible)]="showRoofTop" [title]="'Rooftop Utility '"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-property-rooftop-utility-modal [rooftopUtility]="rooftopUtility" (onClose)="closeRoofTop()">
      </app-property-rooftop-utility-modal>
    </ng-template>
  </imperium-modal>
</div>
<div *ngIf="ShowProperties">
  <imperium-modal  [(visible)]="ShowProperties" [title]="isFreehold ? 'Add Master Freehold Property': 'Add Master Property'" [width]="'large'"
  [bodyTemplate]="bodyTemplate">
  <ng-template #bodyTemplate>
    <app-link-property-lookup-modal [multiSelect]="false" (onSave)="onAddProperties($event)"
        (onClose)="closeProperty()"  [isMasterProperty]="true" [isFreehold]="isFreehold"></app-link-property-lookup-modal>
  </ng-template>
</imperium-modal>
</div>


<!--***********************Add AdditionalUse ************************* -->
<div *ngIf="showAdditionalUseModal">
  <imperium-modal [width]="'medium'" [height]="'medium'" [(visible)]="showAdditionalUseModal"
      [title]="'Add Additional Use'" [bodyTemplate]="bodyTemplate" [closeOnClickOutside]="false">
      <ng-template #bodyTemplate>
          <app-property-additional-use-modal [additionalUse]="additionalUse" (onSave)="onAddAdditionalUse($event)"
              (onClose)="closeAdditionalUse()"></app-property-additional-use-modal>
      </ng-template>
  </imperium-modal>
</div>

<!--***********************Change Log ************************* -->
<div *ngIf="showChangeLog">
  <imperium-modal [width]="'large'" [height]="'large'" [(visible)]="showChangeLog" [title]="'Changelog'"
      [bodyTemplate]="bodyTemplate">
      <ng-template #bodyTemplate>
          <app-changelog-modal [changelogType]="changelogType" [parentId]="parentId"
              (onClose)="closeChangelogModal()"></app-changelog-modal>
      </ng-template>
  </imperium-modal>
</div>

<!--*********************** Parking Space ************************* -->
<div *ngIf="showParkingSpace">
  <imperium-modal [width]="'xl-large'" [height]="'large'" [(visible)]="showParkingSpace" [title]="'Parking Space'"
      [bodyTemplate]="bodyTemplate">
      <ng-template #bodyTemplate> 
          <app-property-parking-space-modal [latLng]="latLong"
          [parkingSpacePolygon]="{singleSlot: parkingPolygon.singleSlotPolygon, parkingAreaPolygon: parkingPolygon.parkingAreaPolygon, parkingSpaces: parkingPolygon.parkingSpaces}"
          (onParkingSave)="onPropertySpacesSave($event)"></app-property-parking-space-modal>
      </ng-template>
  </imperium-modal>
</div>

<!--*********************** Copy Polygon ************************* -->
<div *ngIf="showCopyPolygonModal">
  <imperium-modal [width]="'xl-large'" [height]="'large'" [(visible)]="showCopyPolygonModal"
    [title]="'Copy Polygon'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-polygon-copy-modal [propertyTypes]="propertyTypes" [additionalUseTypes]="additionalUseTypes" (onPolygonSave)="onCopyPolygonSave($event)"
        [originalPolygon]="polygonToBeCopied" [floorOptions]="floorOptions"></app-polygon-copy-modal>
    </ng-template>
  </imperium-modal>
</div>

<div *ngIf="addMultiFreeholdModal">
  <imperium-modal [width]="'small'" [height]="'large'" [(visible)]="addMultiFreeholdModal"
    [title]="'Create Freehold Child Buildings'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-add-multi-freehold [freeholdMin]="childFreeholdMin" (onSave)="onSaveMultiFreeholdUnits($event)"
        (onCancel)="onCancelMultiFreehold($event)"></app-add-multi-freehold>
    </ng-template>
  </imperium-modal>
</div>

<div *ngIf="showPropertyNameModal">
  <imperium-modal [width]="'medium'" [height]="'large'" [(visible)]="showPropertyNameModal"
    [title]="'Property Location Details'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-property-name-and-address [propertyLocation]="propertyLocation" (onSavePropeertyNameAndAddress)="onSavePropertyAddress()"
        (onClosePropertyNameModal)="onClosePropertyNameModal()"></app-property-name-and-address>
    </ng-template>
  </imperium-modal>
</div>
<div *ngIf="showContributedFieldsPopup">
  <imperium-modal [width]="'large'" [height]="'large'" [(visible)]="showContributedFieldsPopup"
    [title]="'Add Contributed Sizes Info'" [bodyTemplate]="manualFootprintAdd">
    <ng-template #manualFootprintAdd>
      <app-manual-footprint-add
        (close)="closeManualfootprintAddPopup()"
        (save)="saveManualFootprintInfo($event)"
        [property]="contributedGBAProperty"
        [condo]="propertyLocation?.Condo"
        [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
        [lookupDropdowns]="propertyLookups"
        [propertyForm]="propertyForm"
        [constructTypes]="constructTypes"
        [constructStatuses]="constructStatuses"
        [condos]="condos"
        [propertyTypes]="propertyTypes"
        [dataArray]="DataArray"
        [propertyCopy]="propertyCopy"
        ></app-manual-footprint-add>
    </ng-template>
  </imperium-modal>
</div>

  <div *ngIf="showUpdateSoldSqmModal">
    <imperium-modal [width]="'large'" [height]="'medium'" [(visible)]="showUpdateSoldSqmModal" [title]="'Enrich Sold SQM'"
      [bodyTemplate]="soldSqmUpdate">
      <ng-template #soldSqmUpdate>
        <app-update-sold-sqm [sales]="sales" (close)="closeSoldSqmModal()" (continue)="saveSoldSqm()"
         [property]="property" [displayPropertyId]="displayPropertyId">
        </app-update-sold-sqm>
      </ng-template>
    </imperium-modal>
  </div>
