import { Component, OnInit, Output, Input, OnDestroy, NgZone, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { EventEmitter } from '@angular/core';
import { DatePipe } from '@angular/common';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { Subscription } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import AES from 'crypto-js/aes';
import encUtf8 from 'crypto-js/enc-utf8';
import { CommonStrings, DefaultDateFormat, DefaultUnitDisplayTextSize } from '../../constants';
import { EditPropertyTabs } from '../../enumerations/editPropertyTabs';
import { EnumApplication } from '../../enumerations/application';
import { EnumCondoTypeName, EnumCondoTypeNameFromTiles } from '../../enumerations/condoType';
import { EnumConstructionStatus } from '../../enumerations/constructionStatus';
import { EnumSizeSource } from '../../enumerations/sizeSource';
import { LocationFormControls } from '../../enumerations/locationFormControls';
import { NavigationPreferences } from '../../enumerations/searchGridNavigationTypes';
import { OfficeControls } from '../../enumerations/officeControlKeys';
import { PropertyFormControls } from '../../enumerations/propertyFormControls';
import { PropertyTypes } from '../../enumerations/enums';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { UnitConversionEnum } from '../../enumerations/unitConversion';
import { UseTypes } from '../../enumerations/useTypes';
import { UserRoles } from '../../enumerations/userRoles';
import { IndexedDBCollections, MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { BuildingClass } from '../../models/BuildingClass';
import { ConstructTypeStatus, CondoTypeChangeListenerType, MultiPolygon } from '../../models/Common';
import { Country } from '../../models/Country';
import { County } from '../../models/County';
import { LatLng } from '../../modules/map-module/models/LatLng';
import { Media } from '../../models/MediaType';
import { MediaType } from '../../enumerations/MediaTypes';
import { Notes } from '../../models/notes';
import { ParkingSpace } from '../../models/ParkingSpace';
import { ParcelDetails, Property } from '../../models/Property';
import { PropertyAdditionalUse } from '../../models/propertyAdditionalUse';
import { PropertyAllocation } from '../../models/propertyAllocation';
import { PropertyLocation } from '../../models/PropertyLocation';
import { PropertyMasterRollup } from '../../models/PropertyMasterRollup';
import { PropertyParcel } from '../../models/PropertyParcel';
import { PropertyResearchStatus, PropertyResearchStatusSaveInput } from '../../models/PropertyResearchStatus';
import { PropertyUse } from '../../models/PropertyUse';
import { RooftopUtility } from '../../models/rooftopUtility';
import { ResearchType } from '../../enumerations/researchType';
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { AddFloorService } from '../../services/add-floor.service';
import { AddressService } from '../../services/address.service';
import { BuildingFootPrintService } from '../../services/building-footprint.service';
import { CommunicationModel, CommunicationService } from '../../services/communication.service';
import { IndexedDBService, IIndexedDBMedia } from '../../services/indexeddb.service';
import { LoginService } from '../../services/login.service';
import { LookupDataService } from '../../services/api-lookup-data.service';
import { MapService } from '../../modules/map-module/service/map-service.service';
import { MetaDataIndexedDBService, IMetaData } from '../../services/indexed-db-service.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { PropertyService } from '../../services/api-property.service';
import { SharedDataService } from '../../services/shareddata.service';
import { StagingIndexedDBService } from '../../services/indexeddb-staging.service';
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
import { confirmSettings, dialogConfirmSettings } from '../../modules/notification/models/confirmSettings';
import { FreeHoldComponent } from '../free-hold/free-hold.component';
import { PropertyAllocationDetailsComponent } from '../../components/common/property-allocation-details/property-allocation-details.component';
import { environment } from '../../../environments/environment';
import { BindFormControlToPropertyVariable, BindLookupNameToVariable, IndustrialYesNoFields, YesNoFields, BindNamesWithLookupName, ContributedSourceFields, dropdownFields, YesOrNoList } from '../../common/constants';
import { buildAddressDetails, getCondoType, mapChangeLogUtil, preparePropertyData, preparePropertyFromResult, preparePropertyLocationData, preparePropertyLocationFromResult, processPropertyResearchStatus } from '../core.utils';
import { getFormNameByUseType, getRange, numericValidator, setPropertyName, setResearchStatusPin, validateBuildingSize, validateIntegerInput, validatePasteInput, getSizeOfNewStrataUnit, getPreviousData } from '../../utils';
import { SaleComp } from '../../models/SaleComp';

@Component({
  selector: 'app-editproperty',
  templateUrl: './editproperty.component.html',
  styleUrls: ['./editproperty.component.scss']
})
export class EditpropertyComponent implements OnInit, OnDestroy {
  @ViewChild('freehold', { static: false }) freehold: FreeHoldComponent;
  @ViewChild('propertyAllocation') propertyAllocation: PropertyAllocationDetailsComponent;
  @Input() tabSelected;
  @Input() userLat;
  @Input() userLong;
  @Input() isStreetView;
  @Input() isAnExistingProperty;
  @Input() hasStreetView: boolean;
  @Input() isParcelLayerEnabled: boolean;
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() FloorSize: any;
  @Input() oldPolygon: any;
  @Input() editedFootprintID: any;
  @Input() newPropertyLocation: LatLng;
  @Input() floorPolygon: any;
  @Output() clearMultiStrataObj: EventEmitter<any> = new EventEmitter<any>();
  @Output() openStreetViewClicked = new EventEmitter<void>();
  @Output() openAerialView = new EventEmitter<void>();
  @Input() aerialCanvas: ElementRef;
  @Input() aerialViewCaptureDiv: ElementRef;
  @Output() onComplete: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() setSelectedPropertyCondoType: EventEmitter<EnumCondoTypeName> = new EventEmitter<EnumCondoTypeName>();
  @Output() fetchProperty: EventEmitter<any> = new EventEmitter<any>();
  @Output() fetchStrataProperty: EventEmitter<string> = new EventEmitter<string>();
  @Output() fetchNextProperty: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewStrataUnitToMaster: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewFreeholdUnitToMaster: EventEmitter<any> = new EventEmitter<any>();
  avgEstimationFF: boolean = environment.EnableAverageEstimationMethod;
  private isPropertyDirtyListner: Subscription;
  private propertyRelocationListner: Subscription;
  private mediaShowFileUploadModal: Subscription;
  private notesModal: Subscription;
  private imageViewerModal: Subscription;
  private updateFormSubscription: Subscription;
  private updatePropertyAlocations: Subscription;
  floorShapeChangeListener: Subscription;
  updateParcelSizeAndCountListener: Subscription;
  propertySaveOnParcelChangeListener: Subscription;
  showContributedGBAInTopSectionListener: Subscription;
  updateIsAverageEstimationEnabledForStrataListener: Subscription;
  showContributedGBA = false;
  private _zone: NgZone;
  private _communicationService: CommunicationService;
  private _propertyService: PropertyService;
  private _addressService: AddressService;
  private _loginService: LoginService;
  private _addFloorService: AddFloorService;
  private _notificationService: NotificationService;
  _sharedDataService: SharedDataService;
  _lookupService: LookupDataService;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  IndexedDBService: IndexedDBService;
  StagingIndexedDBService: StagingIndexedDBService
  PropertyFormControlsEnum = PropertyFormControls;
  selectedTab: number = EditPropertyTabs.Property;
  propertyUseTypes = UseTypes;
  EnumCondoTypeNames = EnumCondoTypeName;
  propertyTypeValues = PropertyTypes;
  userRoles = UserRoles;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  pRange: { StrataMin: string, StrataMax: string } = { StrataMin: '', StrataMax: '' };
  additionalUse: PropertyUse;
  additionalUseList: Array<PropertyAdditionalUse> = new Array<PropertyAdditionalUse>();
  propertyLocation: PropertyLocation;
  public propertyLocationCopy: PropertyLocation = new PropertyLocation();
  parcelInfo: ParcelDetails;
  property: Property;
  rollupMasterFreeholdFieldsObject: PropertyMasterRollup;
  propertyDetails: Property;
  public propertyCopy: Property = new Property();
  countryList: Array<Country>;
  counties: Array<County>;
  buildingClass: Array<BuildingClass>;
  propertyResearchStatusInput: PropertyResearchStatusSaveInput;
  public propertyResearchStatus: Array<PropertyResearchStatus>;
  public propertyResearchStatusOriginal: Array<PropertyResearchStatus>;
  propertyParcelList: Array<PropertyParcel>;
  selectedParcel: PropertyParcel;
  rooftopUtility: RooftopUtility;
  multifloors: MultiPolygon[] = [];
  files: Media[] = [];
  mediaTitle = "Media Upload";
  selectedNote: Notes
  notesTitle = "Notes";
  floorOptions: { key: string, value: number }[] = [{ key: "G", value: 1 }];
  public addressTypeValues: any = { Address: 0, Intersection: 1 };
  SearchType = 2;
  propertyAllocationList: Array<PropertyAllocation> = new Array<PropertyAllocation>();
  propertyAllocationListCopy: Array<PropertyAllocation> = new Array<PropertyAllocation>();
  parkingPolygon: { singleSlotPolygon: ParkingSpace, parkingAreaPolygon: ParkingSpace[], parkingSpaces: number } = { singleSlotPolygon: { polygonId: null, area: 0, polygon: null }, parkingAreaPolygon: [], parkingSpaces: null };
  retailFrontagePolyline: { distance: number, polyline: any };
  hardstandAreaPolygon: { area: number, polygon: any };
  polygonToBeCopied: MultiPolygon;
  location: LatLng;
  stagingMedia: IIndexedDBMedia[] = [];
  rangeForm: FormGroup;
  propertyForm: FormGroup;
  showNoteModal: boolean = false;
  isPropertyDetailsExpanded: boolean = false;
  showDeletePolygonModal = false;
  buildingFootPrintIdToBeDeleted: any;
  showParcelWindow: boolean;
  selectedParcels: any[] = []
  showParcelDetails = false;
  selectedParcelInfo = null;
  parcelInformation: any;
  ShowProperties: boolean = false;
  disableStrataBtn = false;
  isMultiStrata = false;
  StrataMinMaxError = false;
  StreetSuffix1Text = "";
  CityName = "";
  StateName = "";
  StreetNumber: any = null;
  isFreehold = false;
  selectedFloor: any;
  selectedUseTypeID: any;
  activeGridRowNumber: any;
  isNavigationFromSearch: any;
  visitedPropertyIds: any = [];
  currentPropertyRowNumber: any;
  isAddPolygonButtonDisabled: boolean = false;
  pageDetails: any;
  strataDisplayNumber: any;
  editedPropertyIds: any = [];
  visitedStrataIdsFromSS: any;
  editedStrataIds: any = [];
  floorSizeArea: any = [];
  navigationPreference: string;
  selectedMasterParcel: any;
  LastStrataUnit: number;
  isFloorBtnDisable: boolean = false;
  IsSkipped: boolean = false;
  latLong = {};
  showPropertyNameModal: boolean = false;
  isAutoClose: boolean;
  aggregateParcelSize: number = 0;
  parcelCount: number = 0;
  showMap: boolean = false;
  allocationsAndUses = false;
  additionalSpecificUseList: any = {};
  showAdditionalUseModal = false;
  buildingSizeGBACopy = 0;
  buildingSizeNLACopy = 0;
  BldgSizeSourceIDCopy = null;
  roleID: number;
  IsAddStrataProperty = false;
  showChangeLog = false;
  showParkingSpace = false;
  isDeleteFloor = false;
  changelogType = '';
  parentId: number;
  isOffice: boolean;
  isRetail: boolean;
  isIndustrial: boolean;
  quadrants: any;
  streetPrefixes: any;
  streetSufixes: any;
  states: any;
  cities: any;
  condos: any;
  propertyTypes: any;
  additionalUseTypes: any;
  constructStatuses: any;
  roofTypes: any;
  sizeSource: any;
  constructTypes: any;
  validationError: boolean = false;
  CountryId: number;
  EntityID: number;
  specificUses: any;
  public sprinklerTypes: any;
  isNewProperty: boolean = true;
  displayPropertyId: any;
  zoningClasses: any;
  officeId: string = "";
  selectedSuffix: string = "";
  floorLoadingError: boolean = false;
  yearBuildError: boolean = false;
  yearRenovatedError: boolean = false;
  minFloorError: boolean = false;
  maxFloorError: boolean = false;
  streetMinMaxError: boolean = false;
  buildingSFError: boolean = false;
  clearHeightMinError: boolean = false;
  dockHighError: boolean = false;
  noOfOfficeFloorError: boolean = false;
  previousPropertyName: string = ""; 
  shouldUpdateLotSize = false;  
  isAustralia: boolean = false;
  floorlabels: boolean = false;
  dateFormat: string;
  showFileUpload: boolean = false;
  isFullSizeView: boolean = false;
  imageUrls: string[];
  ImageIndex: number;
  private countryCode: string;
  private stateCode: string;
  allspecificuses: any;
  public NABERSList: any;  
  showMasterStrataAlert = false;
  UnitId: number;
  metricUnit = 1;  
  DataArray: Array<any> = [];
  isListing = false;
  isNotes = false;
  isMedia = false;
  isStrata = false;  
  showRoofTop: boolean;
  selectedOption: number = null;
  redirectionLoader = false;
  floors: any[] = [];
  isPolygonCollapsed = true;
  floorsWithFootprint: any;
  UnitDisplayTextSize: any;
  isCityStateLoaded = false;
  researchStatusClicked = false;
  parcelTabClick = false;
  isInitialProperty: any;
  IsUsetypeChanged = false;
  initialPropertyForm: string;
  researchChanged: boolean = false;
  propertyLocationChanged = false;
  auditStatusList: any;
  isFootprintModified: boolean = false;
  addMultiFreeholdModal: boolean = false;
  childFreeholdMin: any;
  loaderMasterPIDAfterChildsVisit = false;
  GRESBScoreError: boolean = false;
  propertyLookups: any;
  propertyLookupsCopy: any;
  showCopyPolygonModal: boolean = false;
  hasNoExistingParcelInTileLayer: boolean = false;
  footPrintNotAvailable: boolean = false;
  showContributedFieldsPopup: boolean = false;
  isAverageEstimationEnabled: boolean = false;
  isAverageEstimationEnabledForMaster: boolean = false;
  contributedGBAProperty: any;
  showUpdateSoldSqmModal = false;
  sales: SaleComp[] = [];
  private pendingIsSaveAndNext: boolean;

  set disableCountySelection(value: boolean) {
    if (value) {
      this.propertyForm.controls['County'].disable();
    } else {
      this.propertyForm.controls['County'].enable();
    }
  }
  set disableCitySelection(value: boolean) {
    if (value) {
      this.propertyForm.controls['City'].disable();
    } else {
      this.propertyForm.controls['City'].enable();
    }
  }

  constructor(propertyService: PropertyService,
    private _mapService: MapService,
    addFloorService: AddFloorService,
    loginService: LoginService,
    communicationService: CommunicationService,
    notificationService: NotificationService,
    zone: NgZone,
    addressService: AddressService,
    private sharedDataService: SharedDataService,
    private _datePipe: DatePipe,
    private buildingFootprintService: BuildingFootPrintService,
    private formBuilder: FormBuilder,
    lookupService: LookupDataService) {
    this._zone = zone;
    this._lookupService = lookupService;
    this._notificationService = notificationService;
    this._loginService = loginService;
    this._addFloorService = addFloorService,
    this._propertyService = propertyService;
    this._addressService = addressService;
    this._communicationService = communicationService;
    this._sharedDataService = sharedDataService;
    this.CountryId = this._loginService.UserInfo.CountryId;
    this.EntityID = this._loginService.UserInfo.EntityID;
    this.dateFormat = this._loginService.UserInfo.DateFormat;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.roleID = this._loginService.UserInfo.RoleID;
    this.IndexedDBService = new IndexedDBService();
    this.StagingIndexedDBService = new StagingIndexedDBService();
    this.init();
    this.UnitId = this._loginService.UserInfo.UnitID;
    this.metricUnit = UnitConversionEnum.Metric;
    this.updateFormSubscription = this._communicationService.subscribe('updatePropertyForm').subscribe(result => {
      this.showContributedGBA = false;
      this.footPrintNotAvailable = false;
      this.isPolygonCollapsed = true;
      this.redirectionLoader = true;
      this.isAverageEstimationEnabled = false;
      this.isAverageEstimationEnabledForMaster = false;
      this._zone.run(() => {
        this.property = new Property();
          this.propertyLocation = new PropertyLocation();
          this.propertyCopy = new Property();
          this.propertyLocationCopy = new PropertyLocation();
          this.initialDetails = result.data;
          this.multifloors = [];
          this.sharedDataService.deleteBuildingFootPrintIds = [];
          setTimeout(() => {
            this.isAnExistingProperty = !!result.data.propertyId;
          }, 10)
          this.initData();
          if (this.initialDetails.propertyId != result.data.propertyId) {
            setTimeout(() => {
              if (this.tabSelected) {
                this.selectedTab = this.tabSelected
                this.isStrata = true;
              }
            }, 2000)
          }
      });
    });

    this.updatePropertyAlocations = this._communicationService.subscribe('updatePropertyAlocations').subscribe(result => {
      if (result.data) {
        this.propertyAllocationList = JSON.parse(JSON.stringify(result.data.allocations));
        if(result.data.isInit) {
          this.propertyAllocationListCopy = JSON.parse(JSON.stringify(result.data.allocations));
        }
      }
    });

    this.floorShapeChangeListener = this._communicationService.subscribe('floorShapeChange').subscribe(result => {
      const isChildOrNotStrata = this.propertyLocation.Condo == EnumCondoTypeName.Child_Freehold || this.propertyLocation.Condo === EnumCondoTypeName.NotStrata;
      const isMasterStrataWithAverageEstimation = this.propertyLocation.Condo === EnumCondoTypeName.Master && this.isAverageEstimationEnabled; 
      // Allow adding footprints only for:
      // - Non-strata records
      // - Child Freehold records
      // - Strata records whose Master does NOT have average estimation enabled
      // - Master Strata records WITH average estimation enabled
      if (result.data && (isChildOrNotStrata || isMasterStrataWithAverageEstimation || this.propertyLocation.Condo === EnumCondoTypeName.Strata && !this.isAverageEstimationEnabledForMaster)) {
        const { multiFloors, isUpdated, isClearPolygon } = result.data;
        this.isFootprintModified = isUpdated;
        this.multifloors = JSON.parse(JSON.stringify(multiFloors));
        this.getAdditionalUses();

        this.isDeleteFloor = isClearPolygon;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
        if (isUpdated) {
          if (this.isAnExistingProperty) {
            this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
          }
          this.updateBuildingSqmOnFloorChange();
          const sortedList = multiFloors.sort((a, b) => {
            const floorSizeA = parseFloat(a.floorSize);
            const floorSizeB = parseFloat(b.floorSize);

            return floorSizeB - floorSizeA;
          });
          if (sortedList && sortedList[0] && sortedList[0].floorSize && parseFloat(sortedList[0].floorSize) > parseFloat(this.property.LargestFloor)) {
            this.property.LargestFloor = sortedList[0].floorSize;
          }
        } else if (result.data) {
          const { multiFloors } = result.data;
          this.multifloors = JSON.parse(JSON.stringify(multiFloors));
        }
        this.updateAddPolygonButtonDisable();
      }
    });
    this.updateParcelSizeAndCountListener = this._communicationService.subscribe('updateParcelSizeAndCount').subscribe(result => {
      if (result.data ){
        this.aggregateParcelSize = result.data.size;
        this.parcelCount = result.data.count;
      }
    });

    this.propertySaveOnParcelChangeListener = this._communicationService.subscribe('propertySaveOnParcelChange').subscribe(result => {
      if (result.data){
        this.propertyForm.get('LotSizeSF').setValue(result.data.size);
        this.propertyForm.get('LotSizeSF').markAsDirty();
        this.propertySave(false);
      }
    });

  }

  groupByRetired(item) {
    return item.Retired;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isAnExistingProperty) {
      if (changes.initialDetails) {
        if (this.initialDetails.propertyId != changes.initialDetails.currentValue.propertyId) {
          this.getPropertyDetails(changes.initialDetails.currentValue.propertyId, () => undefined);
        }
        this.initialDetails = changes.initialDetails.currentValue;
        if (changes.initialDetails.currentValue.locationData) {
          this.stateCode = changes.initialDetails.currentValue.locationData.StateCode;
        } else if (changes.initialDetails.currentValue.parcels && changes.initialDetails.currentValue.parcels.length > 0) {
          this.stateCode = this.initialDetails.parcels[0].State;
        }
      }
      if (changes.newPropertyLocation && changes.newPropertyLocation.currentValue) {
        this.propertyLocationChanged = true;
        this.propertyForm.get('locationDetailsForm')?.get('Latitude').markAsDirty();
        this.propertyForm.get('locationDetailsForm')?.get('Longitude').markAsDirty();
        this.propertyLocation.Latitude = changes.newPropertyLocation.currentValue.Latitude;
        this.propertyLocation.Longitude = changes.newPropertyLocation.currentValue.Longitude;
      }
    } else {
      this.isStreetView = changes.isStreetView ? changes.isStreetView.currentValue : this.isStreetView;
      if (changes.newPropertyLocation && changes.newPropertyLocation.currentValue) {
        this.propertyLocationChanged = true;
        this.propertyLocation.Latitude = changes.newPropertyLocation.currentValue.Latitude;
        this.propertyLocation.Longitude = changes.newPropertyLocation.currentValue.Longitude;
      }
      if (changes.initialDetails) {
        if (this.initialDetails.propertyId == 0 || !this.initialDetails.propertyId) {
          this.metaDataIndexedDBService && this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
          localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
          this.multifloors = [];
        }
      }
    }
  }

  ngOnDestroy(): void {
    this.isPropertyDirtyListner?.unsubscribe();
    this.propertyRelocationListner?.unsubscribe();
    this.mediaShowFileUploadModal?.unsubscribe();
    this.notesModal?.unsubscribe();
    this.imageViewerModal?.unsubscribe();
    this.updateFormSubscription?.unsubscribe();
    this.updatePropertyAlocations?.unsubscribe();
    this.floorShapeChangeListener?.unsubscribe();
    this.updateParcelSizeAndCountListener?.unsubscribe();
    this.propertySaveOnParcelChangeListener?.unsubscribe();
    this.showContributedGBAInTopSectionListener.unsubscribe();
    this.updateIsAverageEstimationEnabledForStrataListener?.unsubscribe();
    this._sharedDataService.propertyResearchStatus = [];
    this._sharedDataService.selectedPropertyParcel = [];
    this._sharedDataService.additionalAddressList = [];
    this._sharedDataService.propertyMedia = [];
    this._sharedDataService.deleteBuildingFootPrintIds = [];
    this.showContributedGBA = false;
    this.isAverageEstimationEnabled = false;
    this.isAverageEstimationEnabledForMaster = false;
  }

  removeCommas(str: string): any {
    return str.replace(/,/g, '');
  }

  private init() {
    this.propertyLocation = new PropertyLocation();
    this.property = new Property();
    this.propertyCopy = new Property();
    this.parcelInfo = new ParcelDetails();
    this.property.UseTypeID = "0";
    this.propertyResearchStatusInput = new PropertyResearchStatusSaveInput();
    this.propertyResearchStatus = new Array<PropertyResearchStatus>();
    this.propertyResearchStatusOriginal = new Array<PropertyResearchStatus>();
    // Assign loggin user id.

    this.propertyLocation.EntityID = this.EntityID;
    this.property.EntityID = this.EntityID;
    this.isPropertyDirtyListner = this._communicationService.subscribe('IsPropertyDirty').subscribe(result => {
      this.cancelConfirmation(false, () => {
        this.propertySave(false);
        this.allowMapToChangePinSelection(true, result.data);
      }, () => {
        this.allowMapToChangePinSelection(true, result.data);
      }, () => {
        this.allowMapToChangePinSelection(false, result.data);
      });
    });
    this.propertyRelocationListner = this._communicationService.subscribe('propertyRelocated').subscribe(result => {
      if (this.property.PropertyID == result.data.PropertyID) {
        this.propertyForm.get('locationDetailsForm')?.get('Latitude').markAsDirty();
        this.propertyForm.get('locationDetailsForm')?.get('Longitude').markAsDirty();
        this.propertyLocation.Latitude = result.data.Latitude;
        this.propertyLocation.Longitude = result.data.Longitude;
        this.property.Latitude = result.data.Latitude;
        this.property.Longitude = result.data.Longitude;
      }
      this.showRoofTop = false;
    });
    this.notesModal = this._communicationService.subscribe('showNoteModal').subscribe(result => {
      this.initialDetails = result.data.initialDetails;
      this.selectedNote = result.data.selectedNote;
      this.notesTitle = !!this.selectedNote ? 'Note Edit' : 'Add Note';
      this.showNoteModal = true;
    });
    this.mediaShowFileUploadModal = this._communicationService.subscribe('showFileUploadModal').subscribe(result => {
      this.initialDetails = result.data.initialDetails;
      this.files = result.data.files;
      this.mediaTitle = !!this.files[0].MediaID ? 'Media Edit' : 'Media Upload';
      this.showFileUpload = true;
    });
    this.imageViewerModal = this._communicationService.subscribe('imageViewer').subscribe(result => {
      this.imageUrls = result.data.imageUrls;
      this.ImageIndex = result.data.ImageIndex;
      this.isFullSizeView = true;
    });

    this.showContributedGBAInTopSectionListener = this._communicationService.subscribe('showContributedGBAInTopSection').subscribe(result => {
      if (result.data) {
        setTimeout(() => {
          this.showContributedGBA = result.data;
        }, 10);
      }
    });
    this.updateIsAverageEstimationEnabledForStrataListener = this._communicationService.subscribe('updateIsAverageEstimationEnabledForStrata').subscribe(result => {
      if (result.data) {
        setTimeout(() => {
          this.isAverageEstimationEnabledForMaster = result.data;
        }, 10);
      }
    });

    setTimeout(() => {
      this.showMapModal(true, this.userLat, this.userLong, false);
    }, 100);
  }

  private allowMapToChangePinSelection(isAllowed: boolean, result) {
    if (isAllowed) {
      result.callback(result.latlng, result.marker)
    }
  }

  onUpdateSoldSqmCancel() {
    // User clicked cancel - don't update sales but proceed with save
    this.showUpdateSoldSqmModal = false;
    this.saveTheProperty(this.pendingIsSaveAndNext);
  }

  onUpdateSoldSqmContinue(updatedSales: any[]) {
    // User clicked update - the update component has already called the update API
    // Now proceed with the save operation
    this.showUpdateSoldSqmModal = false;
    this.saveTheProperty(this.pendingIsSaveAndNext);
  }

  updateMarketList(){
    let commModel = new CommunicationModel();
    commModel.Key = 'updateMarketList';
    commModel.data = {useTypeID: this.property.UseTypeID};
    this._communicationService.broadcast(commModel);
  }

  broadCastNewPropertyFetch(){
    let commModel = new CommunicationModel();
    commModel.Key = 'newPropertyFetched';
    this._communicationService.broadcast(commModel);
  }

  broadCastFetchChangeLog() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchChangeLog';
    commModel.data = this.property.PropertyID;
    this._communicationService.broadcast(commModel);
  }

  updateAdditionalUsesList() {
    // Excluding land and property general use for additonal use list
    this.additionalUseTypes = this.propertyTypes?.filter(type => type.UseTypeID !== UseTypes.Land && type.UseTypeID !== this.property.UseTypeID)
  }

  onSelectUseTypeButton(useType) {
    if (this.isAnExistingProperty) {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.property.UseTypeID = this.selectedUseTypeID;
      this.updateMarketList();
      this.onPropertyUseChange(true, this.selectedUseTypeID, previousUseType);
    } else {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.property.UseTypeID = this.selectedUseTypeID;
      this.changePropertyUse(useType, previousUseType);
    }
    this.getAdditionalUses();
    this.updateAdditionalUsesList();
    this.propertyForm.get('PropertyTypeName').setValue(this.property.UseTypeID)
  }

  changePropertyUse(event, previousUseType) {
    if (!!event) {
      this.getPropertySpecificUse()
      if ((event.UseTypeID === this.propertyTypeValues.Land) && this.multifloors.length>0) {
        this.multiFloorConfirmation(previousUseType);
      }
    }
  }

  updateMultiFloors() {
    this.isFootprintModified = true;
  }

  multiFloorConfirmation(previousUseType) {
    const okCallback = () => {
      this.multifloors = [];
      this.property.BuildingSF = 0;
      this.propertyLocation.Condo = null;
      this.propertyLocation.MasterPropertyId = null;
      this.propertyLocation.CondoUnit = null;
      if (this.isAnExistingProperty) {
        //To trigger alloacations save
        this.isFootprintModified = true;
        this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
      }
      let commModel = new CommunicationModel();
      commModel.Key = 'propertyUseTypeUpdate';
      commModel.data = this.property.UseTypeID;
      this._communicationService.broadcast(commModel);
    }
    const cancelCallback = () => {
      this.selectedUseTypeID = previousUseType;
      this.property.UseTypeID = previousUseType;
      this.propertyForm.get("PropertyTypeName")?.setValue(previousUseType);
    }
    const message = CommonStrings.DialogConfigurations.Messages.PropertyUseChangeConfirmationMessage;
    const title = CommonStrings.DialogConfigurations.Title.Allocations;
    const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: okCallback };
    const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: cancelCallback };
    this.showDialogConfirmation(message, title, okButton, cancelButton);
  }

  onSpecificUseChange(item, index, key) {
    this.isFootprintModified = true;
    this.multifloors[index][key] = item.specificUse;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.getAdditionalUses();
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
  }

  onChangeFloorOrUseType(floor, index, key) {
    if (this.isAnExistingProperty) {
      this.isFootprintModified = true;
    }
    const item = { ...floor };
    const result = [];

    for (const key in this.floorsWithFootprint) {
      if (key !== index.toString()) {
        result.push(...this.floorsWithFootprint[key]);
      }
    }
    const isValid = (item.minFloor && item.maxFloor) ? item.minFloor <= item.maxFloor : true;
    const hasGreaterFloor = item.minFloor > this.property.Floors || item.maxFloor > this.property.Floors;
    if (hasGreaterFloor) {
      this._notificationService.ShowErrorMessage('Min or Max Floor connot be more than property floors');
      setTimeout(() => {
        this.multifloors.forEach((floor, i) => {
          if (i === index) {
            if (key === 'minFloor') {
              floor.minFloor = undefined;
            } else if (key === 'maxFloor') {
              floor.maxFloor = undefined;
            }
          }
        })
      }, 0)
    } else if (isValid) {
      setTimeout(() => {
        this.multifloors.forEach(floor => {
          if (floor.minFloor && !floor.maxFloor) {
            floor.maxFloor = floor.minFloor;
          }
          if (!floor.minFloor && floor.maxFloor) {
            floor.minFloor = floor.maxFloor;
          }
          if (floor.maxFloor && floor.minFloor) {
            floor.floorCount = (floor.maxFloor - floor.minFloor) + 1
          } else if ((floor.maxFloor && !floor.minFloor) || (!floor.maxFloor && floor.minFloor)) {
            floor.floorCount = 1;
          }

        });
        this.updateBuildingSqmOnFloorChange();
        this.updateAddPolygonButtonDisable();
        if (this.isAnExistingProperty) {
          this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
        }
        const range = item.minFloor && item.maxFloor ? getRange(item.minFloor, item.maxFloor) : item.minFloor ? [item.minFloor] : [item.maxFloor];
        this.floorsWithFootprint = { ...this.floorsWithFootprint, [index]: range };
        this.getAdditionalUses();
        let commModel = new CommunicationModel();
        commModel.Key = 'floorInfoChange';
        commModel.data = this.multifloors;
        this._communicationService.broadcast(commModel);
      }, 10);
    } else {
      this._notificationService.ShowErrorMessage('Min Floor connot be more than max floor');
      setTimeout(() => {
        this.multifloors.forEach(item => {
          if (item.minFloor > item.maxFloor) {
            if (key === 'minFloor') {
              item.minFloor = item.maxFloor;
            } else if (key === 'maxFloor') {
              item.maxFloor = item.minFloor;
            }
          }
        });
        if (this.isAnExistingProperty) {
          this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
        }
      }, 0)
    }
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
  }

  getAdditionalUses() {
    let commModel = new CommunicationModel();
    commModel.Key = 'updateAdditionalUses';
    commModel.data = { multiFloors: this.multifloors, useType: this.property.UseTypeID };
    this._communicationService.broadcast(commModel);
  }

  onChangeAdditionalUse(index) {
    this.isFootprintModified = true;
    this.multifloors[index].additionalSpecificUseTypeId = null;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.getAdditionalUses();
  }

  onChangeDescription(item, index, key) {
    if (this.isAnExistingProperty) {
      this.isFootprintModified = true;
    }
    this.multifloors[index][key] = (item.description === null || item.description === undefined) ? '' : item.description;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
  }

  setFloorOptions() {
    const options: { key: string, value: number }[] = [];
    if (this.property && this.property.Floors > 0) {
      for (let i = 1; i <= this.property.Floors; i++) {
        if (i === 1) options.push({ key: "G", value: 1 });
        else options.push({ key: (i - 1).toString(), value: i });
      }
      this.floorOptions = options;
    }
  }

  validateSizeFieldsAgainstBuildingSize() {
    if (this.isAnExistingProperty) {
      const formName = getFormNameByUseType(this.property?.UseTypeID);
      const useTypeForm = this.propertyForm?.get(formName);
      validateBuildingSize(this.property.BuildingSF, this.property, useTypeForm);
    }
  }

  updateBuildingSqmOnFloorChange() {
    if ((this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold) {
      return;
    }
    const sum = this.multifloors.reduce((sum, item) => item.floorCount ? sum + (item.floorCount * (item.floorSize || 0)) : sum + 0, 0);
    this.property.BuildingSF = sum?.toFixed(2);
    if (sum > 0) {
      this.footPrintNotAvailable = false;
      this.property.HasNoBuildingFootprints = 0;
      this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBA_SF');
      this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBASource');
    }
    this.validateSizeFieldsAgainstBuildingSize();
  }

  showCustomMessage(fromInput) {
    const applyFloorSelection = () => {
      const floor = this.isAnExistingProperty ? this._sharedDataService.selectedFloor : (fromInput ? this.selectedFloor : this.property.Floors);
      this.property.Floors = floor;
      this.selectedFloor = floor;
      this.setFloorOptions();
    };
    const message = CommonStrings.DialogConfigurations.Messages.SomeFloorsHaveFootprintMessage;
    const title = CommonStrings.DialogConfigurations.Title.FloorwisePolygon;
    const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: applyFloorSelection };
    const cancelBtn = { Visible: false }
    this.showDialogConfirmation(message, title, okButton, cancelBtn);
  }

  onSelectFloor(fromInput = false) {
    let floorsWithFootprint = [];
    this.multifloors.forEach(floor => {
      if (floor) {
        const range = (floor.minFloor && floor.maxFloor) ? getRange(floor.minFloor, floor.maxFloor) : floor.minFloor ? [floor.minFloor] : [floor.maxFloor];
        floorsWithFootprint = [...floorsWithFootprint, ...range];
      }
    });
    const check = fromInput ? floorsWithFootprint.some((value) => value > this.property.Floors) : floorsWithFootprint.some((value) => value > this.selectedFloor);
    if (check) {
      this.showCustomMessage(fromInput);
    } else {
      if (!fromInput) {
        this.property.Floors = this.selectedFloor;
      } else {
        this.selectedFloor = this.property.Floors;
      }
      this.updateBuildingSqmOnFloorChange();
      if (this.isAnExistingProperty) {
        if (fromInput) {
          this.property.Floors = this.selectedFloor;
        } else {
          this.selectedFloor = this.property.Floors;
        }
      }
      this._sharedDataService.selectedFloor = this.selectedFloor;
    }
    this.setFloorOptions();
  }

  showDeleteModal(shapeId) {
    this.showDeletePolygonModal = true;
    this.buildingFootPrintIdToBeDeleted = shapeId;
  }

  deletePolygon() {
    this.buildingFootprintService.deleteBuildingFootPrint(this.buildingFootPrintIdToBeDeleted, this.property.PropertyID).subscribe(result => {
      this.close();
      let commModel = new CommunicationModel();
      commModel.Key = 'deleteBuildingFootprint';
      commModel.data = result;
      this._communicationService.broadcast(commModel);
    })
  }

  close() {
    this.showDeletePolygonModal = false;
    this.buildingFootPrintIdToBeDeleted = undefined;
  }

  setSelectedUseType() {
    this.selectedUseTypeID = this.property.UseTypeID;
  }
  updateAddPolygonButtonDisable() {
    if (this.multifloors && this.multifloors.length === 0 || this.multifloors.every(item => (item.floorSize > 0 && !!item.minFloor && !!item.maxFloor))) {
      this.isAddPolygonButtonDisabled = false;
    } else {
      this.isAddPolygonButtonDisabled = true;
    }
  }

  expandCollapsePolygons() {
    setTimeout(() => {
      this.isPolygonCollapsed = !this.isPolygonCollapsed;
    }, 0);
  }
  
  addfloor() {
    const multipolyObj: MultiPolygon = {
      specificUse: this.selectedUseTypeID, maxFloor: undefined, minFloor: undefined, floorCount: undefined,
      floorSize: undefined, shape: undefined, localBuildingFootPrintID: uuidv4(), description: null
    };
    this.isPolygonCollapsed = false;
    this.multifloors = [...this.multifloors, multipolyObj];
    this._addFloorService.enableFloorLables(true);
    this.isAddPolygonButtonDisabled = true;
    this.setFloorOptions();
  }
  deleteFloorData(item, index) {
    if (this.isAnExistingProperty) {
      const currentIds = [...this.sharedDataService.deleteBuildingFootPrintIds];
      const newId = item.BuildingFootPrintID;
      if (newId && !currentIds.includes(newId)) {
        currentIds.push(newId);
      }
      this.sharedDataService.deleteBuildingFootPrintIds = currentIds;
      this.isDeleteFloor = true;
      if (this.floorsWithFootprint && this.floorsWithFootprint[index]) {
        delete this.floorsWithFootprint[index];
      }
      this.multifloors = this.multifloors.filter((floor) => {
        if (floor.BuildingFootPrintID && item.BuildingFootPrintID) {
          return floor.BuildingFootPrintID !== item.BuildingFootPrintID
        } else {
          return floor.localBuildingFootPrintID !== item.localBuildingFootPrintID
        }
      });
    } else {
      if (this.floorsWithFootprint && this.floorsWithFootprint[index]) {
        delete this.floorsWithFootprint[index];
      }
      this.multifloors = this.multifloors.filter((floor) => floor.localBuildingFootPrintID !== item.localBuildingFootPrintID);
    }
    //To trigger allocations update
    this.isFootprintModified = true;
    this.updateAddPolygonButtonDisable();
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
    this.getAdditionalUses();
    this.updateBuildingSqmOnFloorChange();
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
    const buildingFootPrintID = item.BuildingFootPrintID ? item.BuildingFootPrintID.toString() : item.localBuildingFootPrintID;
    let commModel = new CommunicationModel();
    commModel.Key = 'deleteFloor';
    commModel.data = { floorId: buildingFootPrintID, multifloors: this.multifloors };
    this._communicationService.broadcast(commModel);
  }

  deleteFloorConfirmation(item, index: number) {
    if (item.minFloor || item.maxFloor || item.floorSize || item.shape) {
      const message = CommonStrings.DialogConfigurations.Messages.DeleteFloorData;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => this.deleteFloorData(item, index) };
      const cancelBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { } };
      this.showDialogConfirmation(message, title, okButton, cancelBtn);
    } else {
      this.deleteFloorData(item, index);
    }
  }

  allocatedFloorsConfirmationPopup() {
    // Confirmation modal to update allocations before updating floors
    const configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.AllocatedFloorsMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Ok;
    this._notificationService.CustomDialog(configuration);
  }

  isResearchVisible(researchTypeID: number, propertyID: number): boolean {
    return !(researchTypeID === ResearchType.NotStarted);
  }

  onFileUploaded($event) {
    let model = new CommunicationModel();
    model.data = $event;
    model.Key = 'onFileUpload';
    this._communicationService.broadcast(model);
    this.showFileUpload = false;
  }

  loadResearchStatus() {
    // To get all research status.
    if (!!this._sharedDataService.researchStatusList && this._sharedDataService.researchStatusList.length > 0) {
      this.propertyResearchStatus = this._sharedDataService.researchStatusList;
      if (this.initialDetails.propertyId) {
        this.getPropertyResearchStatus(this.initialDetails.propertyId);
      } else {
        this.copyPropertyResearchStatus();
      }
    } else {
      const response_ResearchStatus = this._propertyService.GetAllPropertyResearchStatus();
      response_ResearchStatus.subscribe(result => {
        if (!result.body.error)
          this.propertyResearchStatus = result.body.responseData || [];
        this.propertyResearchStatus.sort((a, b) => Number(a.Sequence) - Number(b.Sequence));
        this.propertyResearchStatus = processPropertyResearchStatus(this.propertyResearchStatus);
        this.propertyResearchStatus.forEach(emp => {
          emp.IsActive = false;
          emp.PropertyResearchStatusID = 0;
          setResearchStatusPin(emp);
        });
        this._sharedDataService.researchStatusList = this.propertyResearchStatus;
        if (this.initialDetails.propertyId) {
          this.getPropertyResearchStatus(this.initialDetails.propertyId);
        } else {
          this.copyPropertyResearchStatus();
        }
      });
    }
  }

  getUserInfoFromStorage() {
    const loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
    if (loginData != "" && !!loginData) {
      const bytes = AES.decrypt(loginData?.toString(), environment?.EncryptionKey);
      const loggedinData = JSON.parse(bytes?.toString(encUtf8));
      if (loggedinData) {
        const { UnitID, EntityID, DateFormat, UnitDisplayTextSize, RoleID } = loggedinData;
        this.UnitId = UnitID || this.metricUnit;
        this.EntityID = EntityID;
        this.dateFormat = DateFormat || DefaultDateFormat;
        this.UnitDisplayTextSize = UnitDisplayTextSize || DefaultUnitDisplayTextSize;
        this.roleID = RoleID;
      }
    }
  }

  ngOnInit() {
    this.getUserInfoFromStorage();
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    if (!this.isAnExistingProperty) {
      this.property = new Property();
      this.propertyCopy = new Property();
      this.property.TypicalFloorSizeSM = 0;
    }
    this.isNavigationFromSearch = JSON.parse(sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch));
    if (this.isNavigationFromSearch) {
      this.getEditedProperties();
      this.getVisitedProperties();
      const activeGridRowNumberFromSs = sessionStorage.getItem(SessionStorageKeys.ActiveGridRowNumber);
      const PageDetailsFromSs = sessionStorage.getItem(SessionStorageKeys.SearchResultsPageDetails);
      if (activeGridRowNumberFromSs && PageDetailsFromSs) {
        this.activeGridRowNumber = JSON.parse(activeGridRowNumberFromSs);
        this.pageDetails = JSON.parse(PageDetailsFromSs);
        this.setPropertyRecordDisplayNumber(this.activeGridRowNumber);
      }
    }
    this.getAuditStatusesFromMetaData();
    this.getMultiFloorsFromMetaData();
    this.visitedStrataIdsFromSS = JSON.parse(sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)) || [];
    this.editedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.EditedStrataIds)) || [];
    this.navigationPreference = JSON.parse(sessionStorage.getItem(SessionStorageKeys.NavigationPreference)) || NavigationPreferences.UnVisited;
    this.initData();
  }

  getPropertyLookupData(onLoad: () => void) {
    if (this._sharedDataService.getLookupDropdowns()) {
      this.propertyLookups = this._sharedDataService.getLookupDropdowns();
      onLoad();
    } else {
      const response = this._lookupService.getPropertyFieldsDropdownItems(1);
      response.subscribe((result) => {
        if (!result.body.error) {
          this.propertyLookups = result.body.responseData;
          this._sharedDataService.setLookupDropdowns = this.propertyLookups;
          this.propertyLookupsCopy = JSON.parse(JSON.stringify(this.propertyLookups));
          onLoad();
        } else {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.LookupDataFailureMessage);
        }
      })
    }
  }

  async getMultiFloorsFromMetaData() {
    try {
      const floorsData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
      if (floorsData) {
        this.multifloors = floorsData.value;
        this._addFloorService.enableFloorLables(true)
        this.updateAddPolygonButtonDisable();
        this.updateBuildingSqmOnFloorChange();
        let commModel = new CommunicationModel();
        commModel.Key = 'floorInfoChange';
        commModel.data = this.multifloors;
        this._communicationService.broadcast(commModel);
      } else if (this.isAnExistingProperty) {
        this._addFloorService.enableFloorLables(true)
      }
      if (!this.isAnExistingProperty) {
        this.property.TypicalFloorSizeSM = 0;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getAuditStatusesFromMetaData() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.AuditStatusList);
      if (searchData) {
        this.auditStatusList = searchData.value;
      } else {
        this.getAuditStatuses()
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  getAuditStatuses = () => {
    const auditStatus = this._lookupService.GetPropertyAuditStatuses();
    auditStatus.subscribe(result => {
      if (!result.body.error) {
        const data = result.body.responseData[0];
        this.auditStatusList = data;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.AuditStatusList, value: this.auditStatusList });
      }
    })
  }

  async getVisitedProperties() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.VisitedPropertyIds);
      if (searchData) {
        this.visitedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  async getEditedProperties() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.EditedPropertyIds);
      if (searchData) {
        this.editedPropertyIds = searchData.value;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  loadCountryStateCities() {
    // Normalize country list
    this.countryList = this.countryList?.map(c => {
      const country = c as Country;
      country.Alpha2Code = c['Alpha-2Code'];
      country.Alpha3Code = c['Alpha-3Code'];
      delete country['Alpha-2Code'];
      delete country['Alpha-3Code'];
      return country;
    });
    let countryId = this.countryList?.find(x => x.Alpha2Code == this.countryCode)?.CountryID;
    if (countryId) {
      this.states = this.states?.filter(state => state.CountryID === countryId);
      let state = this.states?.find(x => x.StateAbbr.trim() == this.stateCode);
      this.cities = this.cities?.filter(city => city.StateID === state.StateID);
      this.counties = this.counties?.filter(county => county.StateId === state.StateID);
      this.populatePropertyLocation();
    }
  }

  initData() {
    this.showContributedGBA = false;
    this.isAverageEstimationEnabledForMaster = false;
    this.isAverageEstimationEnabled = false;
    if (this.initialDetails && this.initialDetails.locationData) {
      this.countryCode = this.initialDetails.locationData.CountryCode;
      this.stateCode = this.initialDetails.locationData.StateCode;
    }
    else if (this.initialDetails && this.initialDetails.parcels && this.initialDetails.parcels.length > 0) {
      this.countryCode = this.initialDetails.parcels[0].CountryCode;
      this.stateCode = this.initialDetails.parcels[0].State;
    }
    this.loadResearchStatus();
    // Map lookup to variables
    this.getPropertyLookupData(() => {
      // Bind lookup data
      Object.keys(BindLookupNameToVariable).forEach(key => this[BindLookupNameToVariable[key]] = this.propertyLookups[key]);
    
      // Process construct types
      this.constructTypes = (this.constructTypes || []).sort((a, b) => a.Retired - b.Retired).map(type => ({
        ...type,
        Retired: type.Retired === 1 ? ConstructTypeStatus.Retired : ConstructTypeStatus.InUse,
        disabled: type.Retired === 1
      }));
    
      // Process property types
      this.propertyTypes?.forEach(data => {
        data.isSelected = false;
        if (!data.UseTypeLabel) data.UseTypeLabel = data.UseTypeName.charAt(0);
        if (data.UseTypeName === "Office") this.officeId = data.UseTypeID;
      });
    
      this.updateAdditionalUsesList();
      this.loadCountryStateCities();
    });
    
    this.propertyForm = new FormGroup({
      locationDetailsForm: new FormGroup({
        'Latitude': new FormControl('', Validators.required),
        'Longitude': new FormControl('', Validators.required)
      }),
      //Top section fields
      'Condo': new FormControl('', Validators.required),
      'IsAverageEstimationEnabled': new FormControl(''),
      'CondoUnit': new FormControl(''),
      'MasterPropertyId': new FormControl(''),
      'ZoningCode': new FormControl(''),
      'ConstructionStatus': new FormControl('', Validators.required),
      'ConstructionType': new FormControl(''),
      'BuildingSF': new FormControl('', [Validators.required, numericValidator(), Validators.min(1)]),
      'BldgSizeSourceID': new FormControl('', [Validators.required, Validators.min(1)]),
      'LotSizeSourceID': new FormControl('', Validators.required),
      'LotSizeSF': new FormControl('', [numericValidator()]),
      'PropertyTypeName': new FormControl('', [Validators.required, Validators.min(1)]),
      'Floors': new FormControl('', Validators.required),
      'PropertyNameAsAddress': new FormControl(''),
      'IsMultiplePolygonsNeeded': new FormControl(''),
      'NeedsResearchComments': new FormControl(''),
      'IsSkipped': new FormControl(''),
      'ParcelCount': new FormControl(''),
      'AggregateParcelSize': new FormControl(''),
      'ContributedSourceComments': new FormControl(''),
      'OfficeForm': new FormGroup({}),
      'IndustrialForm': new FormGroup({}),
      'RetailForm': new FormGroup({}),
      'LandForm': new FormGroup({})
    });

    // Dynamically add controls based on LocationFormControls enum
    Object.values(LocationFormControls).forEach(controlName => {
      const locationDetailsForm = this.propertyForm?.get('locationDetailsForm') as FormGroup;
    
      if (!locationDetailsForm.contains(controlName)) {
        locationDetailsForm.addControl(controlName, new FormControl(''));
      } 
    });

    this.propertyForm.get('PropertyTypeName').valueChanges.subscribe((PropertyType) => {
      const formName = getFormNameByUseType(this.property?.UseTypeID); 
      const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
      if (PropertyType === this.officeId.toString()) {
        useTypeForm?.get('BuildingClass')?.clearValidators();
        useTypeForm?.get('BuildingClass')?.setValidators([Validators.required]);
      } else {
        useTypeForm?.get('BuildingClass')?.clearValidators();
      }
      useTypeForm?.get('BuildingClass')?.updateValueAndValidity();
      this.handleFieldsOnUseTypeOrStrataChange(this.property.UseTypeID,this.propertyLocation?.Condo);
      Object.keys(this.propertyLookupsCopy).forEach((lookup) => {
        if(lookup == 'BuildingClassID') {
          this.propertyLookups[lookup] = this.propertyLookupsCopy[lookup].filter((item) => {
            return item.UseTypeID == PropertyType;
          });
        }
        if(lookup == 'SpecificUsesID'){
          this.propertyLookups[lookup] = this.propertyLookupsCopy[lookup].filter((item)=>{
            return item.UseTypeId == PropertyType;
          })
        }
      })
    })

    this.toggleBuildingSFValidators();
    this.rangeForm = this.formBuilder.group({
      StrataMin: ['', [Validators.required, Validators.maxLength(6)]],
      StrataMax: ['', [Validators.required, Validators.maxLength(6)]]
    });

    if (!this.isAnExistingProperty && this.isParcelLayerEnabled && this._sharedDataService && this._sharedDataService.parcelInfoPickedFromTileLayer) {
      const parcels = this._sharedDataService.parcelInfoPickedFromTileLayer || [];
      this.parcelInformation = parcels;
      this.showParcelWindow = true;
      this.hasNoExistingParcelInTileLayer = parcels.length === 0;
      this.initTypeAndFloor();
    }

    this.propertyForm.get('Condo').valueChanges.subscribe((Condo) => {
      if (!Condo) return;
      const form = this.propertyForm;
      const enable = (name, validators = []) => {
        form.get(name).enable();
        form.get(name).setValidators(validators);
        form.get(name).markAsTouched();
      };
      const disable = (name) => {
        form.get(name).disable();
        form.get(name).clearValidators();
        form.get(name).markAsUntouched();
      };
      if ([EnumCondoTypeName.Strata, EnumCondoTypeName.Child_Freehold].includes(this.propertyLocation.Condo)) {
        enable('CondoUnit', [Validators.required]);
        enable('MasterPropertyId', [Validators.required]);
        disable('LotSizeSourceID');
        disable('AggregateParcelSize');
      } else if ([this.EnumCondoTypeNames.Master_Freehold, this.EnumCondoTypeNames.Master].includes(Condo)) {
        this.property.SmallestFloor = 0;
        this.property.LargestFloor = 0;
        disable('CondoUnit');
        enable('LotSizeSourceID', [Validators.required]);
        enable('AggregateParcelSize');
      } else {
        disable('CondoUnit');
        disable('MasterPropertyId');
        form.get('BuildingSF')?.setValidators([Validators.required, numericValidator(), Validators.min(1)]);
        enable('LotSizeSourceID');
        disable('AggregateParcelSize');
      }
      ['CondoUnit', 'MasterPropertyId', 'LotSizeSourceID', 'AggregateParcelSize'].forEach(c => form.get(c).updateValueAndValidity());
    });    

    if (!this.propertyLocation.AddressType) {
      this.propertyLocation.AddressType = this.addressTypeValues.Address;
    }

    if (this.isAnExistingProperty) {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.onPropertyUseChange(false, this.property.UseTypeID ? this.property.UseTypeID : 5, previousUseType);
    }

    setTimeout(() => {
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
    }, 2000)
  }

  toggleBuildingSFValidators() {
    this.propertyForm.get('IsAverageEstimationEnabled').valueChanges.subscribe((value) => {
      if (value) {
        this.enableBuildingSF();
        this.propertyForm.get('Floors')?.enable();
        this.propertyForm.get('Floors')?.setValidators([Validators.required]);
        this.propertyForm.get('Floors')?.markAsTouched();
      } else {
        this.disableBuildingSF();
        this.propertyForm.get('Floors')?.disable();
        this.propertyForm.get('Floors')?.clearValidators();
        this.propertyForm.get('Floors')?.markAsUntouched();
      }
      this.propertyForm.get('BuildingSF')?.updateValueAndValidity();
      this.broadCastAverageEstimationChange(value);
    })
  }

  broadCastAverageEstimationChange(value) {
    let commModel = new CommunicationModel();
    commModel.Key = 'AverageEstimationEnabled';
    commModel.data = value;
    this._communicationService.broadcast(commModel);
  }

  private enableBuildingSF(): void {
    const control = this.propertyForm.get('BuildingSF');
    control?.enable();
    control?.setValidators([Validators.required, numericValidator(), Validators.min(1)]);
    control?.markAsTouched();
    control?.updateValueAndValidity(); 
  }

  private disableBuildingSF(): void {
    const control = this.propertyForm.get('BuildingSF');
    control?.disable();
    control?.clearValidators();
    control?.markAsUntouched();
    control?.updateValueAndValidity(); 
  }

  initTypeAndFloor() {
    this.selectedUseTypeID = null;
    this.selectedFloor = !!this._sharedDataService.selectedFloor ? this._sharedDataService.selectedFloor : 1;
    this.property.UseTypeID = this.selectedUseTypeID;
    setTimeout(() => {
      this.property.Floors = this.selectedFloor;
      this.property.ConstructionStatusID = this.property.ConstructionStatusID ?? EnumConstructionStatus?.Existing;
      this.setFloorOptions();
    }, 100);
  }

  private landValidator =   function () {
    const formName = getFormNameByUseType(this.property?.UseTypeID); 
    const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
    useTypeForm.get('BuildingClass').clearValidators();
  }

  handleFieldsOnUseTypeOrStrataChange(useType, condoType) {
    const form = this.propertyForm;
    const enable = (ctrl, validators = []) => {
      form.get(ctrl)?.enable();
      form.get(ctrl)?.setValidators(validators);
      form.get(ctrl)?.markAsTouched();
      form.get(ctrl)?.updateValueAndValidity();
    };
    const disable = (ctrl) => {
      form.get(ctrl)?.disable();
      form.get(ctrl)?.clearValidators();
      form.get(ctrl)?.markAsUntouched();
      form.get(ctrl)?.updateValueAndValidity();
    };
  
    if (useType === this.propertyTypeValues.Land) {
      if (!this.isAnExistingProperty) this.checkForParcelLayer();
      ['ConstructionType', 'ConstructionStatus', 'Condo'].forEach(c => {
        form.get(c)?.clearValidators(); form.get(c)?.updateValueAndValidity();
      });
    } else {
      if (condoType !== this.EnumCondoTypeNames.Master_Freehold) {
        enable('ConstructionStatus', [Validators.required]);
        enable('ConstructionType', [Validators.required]);
      } else {
        disable('ConstructionStatus');
        form.get('ConstructionType')?.clearValidators();
        form.get('ConstructionType')?.updateValueAndValidity();
      }
      enable('Condo', [Validators.required]);
    }
  
    const isLandOrMaster = useType === this.propertyTypeValues.Land ||
      [this.EnumCondoTypeNames.Master_Freehold, this.EnumCondoTypeNames.Master].includes(condoType);
  
    if (isLandOrMaster) {
      this.property.TypicalFloorSizeSM = 0.0;
      if (this.isAnExistingProperty) {
        this.property.SmallestFloor = 0.0;
        this.property.LargestFloor = 0.0;
      }
      if (!this.isAverageEstimationEnabled) {
        this.disableBuildingSF();
        form.get('Floors')?.disable();
        form.get('Floors')?.clearValidators();
        form.get('Floors')?.markAsUntouched();
      }
      ['TypicalFloorSizeSM'].forEach(disable);
      enable('LotSizeSourceID', [Validators.required]);
      form.get('ConstructionType')?.clearValidators();
      form.get('ConstructionType')?.updateValueAndValidity();
      enable('AggregateParcelSize');
    } else {
      enable('Floors', [Validators.required]);
      enable('TypicalFloorSizeSM', [Validators.required]);
      enable('BuildingSF', [Validators.required, numericValidator(), Validators.min(1)]);
      disable('LotSizeSourceID');
      enable('ConstructionType', [Validators.required]);
      disable('AggregateParcelSize');
    }
  
    if (condoType === this.EnumCondoTypeNames.NotStrata || !condoType) {
      enable('LotSizeSourceID', [Validators.required]);
    }
  }

  private applyValidation(action: 'clear' | 'update') {
    const controls = [ 'Floors', 'ConstructionStatus', 'ConstructionType', 'BldgSizeSourceID', 'LotSizeSourceID', 'ZoningCode', 'BuildingSF' ];
    controls.forEach(control => {
      const ctrl = this.propertyForm.get(control);
      if (!ctrl) return;
      if (action === 'clear') {
        ctrl.clearValidators();
      }
      ctrl.updateValueAndValidity();
    });
  }

  addSpecificValidation(PropertyType) {
    this.applyValidation('clear');
    switch (Number(PropertyType)) {
      case this.propertyTypeValues.Land:
        if (!(this.propertyResearchStatus.length > 0 && this.propertyResearchStatus[3].IsActive)) { this.landValidator(); }
        break;
    }
    this.applyValidation('update'); 
  }

  broadCastFetchAdditionalAddress() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchAdditionalAddress';
    commModel.data = this.property.PropertyID;
    this._communicationService.broadcast(commModel);
  }

  broadcastFetchParcels() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchParcels';
    commModel.data = { shouldUpdate: false };
    this._communicationService.broadcast(commModel);
  }

  private populatePropertyLocation() {
    // If add new property, bind country, city, state with what we get from google
    this.propertyLocation = new PropertyLocation();
    if (this.initialDetails.propertyId && !this.initialDetails.isNewProperty) {
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
      this.isNewProperty = false;
      this.displayPropertyId = this.initialDetails.propertyId;
      this.property.PropertyID = this.initialDetails.propertyId;
      this.getPropertyDetails(this.property.PropertyID, () => {
        if (this.isAnExistingProperty) {
          this.getPropertyResearchStatus(this.property.PropertyID);
          this.broadcastFetchParcels();
          this.broadCastFetchAdditionalAddress();
          if (this.isAnExistingProperty) {
            this.propertyAllocation?.getPropertyAllocationsAndUses(this.initialDetails.propertyId);
          }
        }
      });
    }
    else if (this.initialDetails.isNewProperty || !!this.initialDetails.locationData) {
      this.redirectionLoader = false;
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
      this.property = new Property();
      this.propertyCopy = new Property();
      this.property.UseTypeID = 0;
      this.property.SpecificUseID = 0;
      this.property.SizeSourceID = null;
      this.property.BuildingClass = '';
      this.property.PropertyID = 0;
      this.isNewProperty = true;
      this.displayPropertyId = 0;
      if (!this.isAnExistingProperty) {
        this.propertyForm.get('ConstructionStatus').setValue(EnumConstructionStatus?.Existing);
        this.property.ConstructionStatusID = EnumConstructionStatus?.Existing;
        this.property.ConstructionTypeID = undefined;
      }
      // Binding country based on the google country code
      this.defaultPropertyLocationForNewProperty();
      if (this.isAnExistingProperty) {
        if (this.initialDetails.selectedParcel) {
          this.populatePropertyWithTaxData();
        }
        //To revert to previous property use type
        const previousUseType = this.property.UseTypeID;
        this.onPropertyUseChange(false, this.property.UseTypeID ? this.property.UseTypeID : 5, previousUseType);
      }
    } else {
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
    }
  }
  
  private populatePropertyWithTaxData() {
    let parcel = this.initialDetails.selectedParcel;
    this.parcelInfo.ParcelNo = parcel.ParcelNumber;
    this.parcelInfo.ParcelSF = parcel.Area;
    this.property.UseTypeID = parcel.PropertyUse;
    if (!(this.initialDetails.parcelProperties && this.initialDetails.parcelProperties.length > 0)) {
      this.property.BuildingSF = parcel.Area?.toFixed(2);
      this.property.SizeSourceID = 5;
    }
  }

  private defaultPropertyLocationForNewProperty(): void {
    this.initializeNewLocationAndParcel();
    this.setBasicLocationDetails();
    this.setLocationDataDetails();
    if (!this.isAnExistingProperty) {
      this.initTypeAndFloor();
      this.buildAddress();
    }
    this.showMapModal(true, null, null, false);
    this.addressAsPropertyName(false);
    if (this.initialDetails.fromMasterStrata && this.initialDetails.masterStrataObj.property) {
      this.handleMasterStrata();
    }
  }
  
  private initializeNewLocationAndParcel(): void {
    this.propertyLocation = new PropertyLocation();
    this.parcelInfo = new ParcelDetails();
    this.propertyLocation.AddressType = this.propertyLocation.AddressType ?? this.addressTypeValues.Address;
  }
  
  private setBasicLocationDetails(): void {
    const latLng = this.initialDetails.latLng;
    if (latLng.Latitude) this.propertyLocation.Latitude = latLng.Latitude;
    if (latLng.Longitude) this.propertyLocation.Longitude = latLng.Longitude;
  }
  
  private setLocationDataDetails(): void {
    const data = this.initialDetails.locationData;
    if (!data) return;
    if (data.ZipCode) this.propertyLocation.Zip = parseInt(data.ZipCode);
    if (data.MaximumStreetNumber) this.propertyLocation.StreetNumberMax = data.MaximumStreetNumber;
    if (data.MinimumStreetNumber) {
      this.propertyLocation.StreetNumberMin = data.MinimumStreetNumber;
    } else if (data.StreetNumber) {
      this.propertyLocation.StreetNumberMin = data.StreetNumber;
    }  
    this.setState(data.StateCode);
    this.setCity(data.City, data.StateCode);
    this.setCounty(data.CountyName);
    this.setStreetName(data.StreetNameShort);
  }
  
  private setState(stateCode: string): void {
    if (!stateCode) return;
    const state = this.states.find(s => s.StateAbbr.trim() === stateCode);
    if (state) this.propertyLocation.State = state.StateID;
  }
  
  private setCity(cityName: string, stateCode: string): void {
    if (!cityName) return;
    const matchedCity = this.cities.find(c => c.CityName.toUpperCase().trim() === cityName.toUpperCase());
    if (matchedCity) {
      this.propertyLocation.City = matchedCity.CityID;
    } else {
      const state = this.states.find(s => s.StateAbbr.trim() === stateCode);
      if (state) {
        this._addressService.createCity(cityName.trim(), state.StateID).subscribe(result => {
          if (!result.body.error && result.body.responseData?.length) {
            this.propertyLocation.City = result.body.responseData[0].CityID;
          }
        });
      }
    }
  }
  
  private setCounty(countyName: string): void {
    if (!countyName) return;
    let county = (this.counties || []).find(c => c.CountyName.toUpperCase().trim() === countyName.toUpperCase());
    if (!county) {
      const cleaned = countyName.toUpperCase().replace('CITY', '').trim();
      county = (this.counties || []).find(c => c.CountyName.toUpperCase().trim() === cleaned);
    }
    if (county) {
      this.propertyLocation.County = county.CountyID;
    }
  }
  
  private setStreetName(streetName: string): void {
    if (!streetName) return;
    this.propertyLocation.AddressStreetName = streetName;
    const suffixParts = streetName.split(" ");
    this.selectedSuffix = suffixParts[suffixParts.length - 1];
    const matchedSuffix = this.streetSufixes.find(s =>
      s?.Suffix?.toUpperCase() === this.selectedSuffix?.toUpperCase() ||
      s?.SuffixName?.toUpperCase() === this.selectedSuffix?.toUpperCase()
    );
    if (matchedSuffix) {
      this._zone.run(() => {
        const lastIndex = streetName.lastIndexOf(" ");
        this.propertyLocation.AddressStreetName = streetName.substring(0, lastIndex);
        this.propertyLocation.StreetSuffix1 = matchedSuffix.SuffixId;
      });
    }
  }
  
  private handleMasterStrata(): void {
    const masterStrata = this.initialDetails.masterStrataObj;
    this.disableStrataBtn = true;
    this.isFreehold = masterStrata.isFreehold;
    this.isMultiStrata = masterStrata.isMultiStrata;
    this.pRange.StrataMin = masterStrata.minStrataUnit;  
    this.property = { ...masterStrata.property };
    this.propertyLocation = { ...masterStrata.location };
    this.propertyLocation.MasterPropertyId = masterStrata.property.PropertyID;
    this.property.PropertyID = 0;
    this.propertyLocation.PropertyID = 0;
    this.propertyLocation.Condo = this.isFreehold ? EnumCondoTypeName.Child_Freehold : EnumCondoTypeName.Strata;
    this.propertyLocation.CondoUnit = masterStrata.minStrataUnit;
    this.property.BuildingSF = undefined;
    this.property.BuildingSizeSM = undefined;
    this.property.TypicalFloorSizeSM = undefined;  
    this.selectedOption = masterStrata.property.PropertyResearchTypeID;
    this.selectedFloor = '1';
    this.property.Floors = 1;
    if (masterStrata?.location?.Condo === EnumCondoTypeName.Master && masterStrata.property.IsAverageEstimationEnabled) {
      this.isAverageEstimationEnabledForMaster = true;
      !this.isMultiStrata && this.getLinkedProperties(masterStrata.property.PropertyID, 1);
    }
    this.rangeForm.valueChanges.subscribe(() => {
      this.checkStrataMinMaxError();
    });
    this.propertyForm.patchValue({ Condo: this.propertyLocation.Condo });
    this.setFormFieldValidators('CondoUnit');
    this.setFormFieldValidators('MasterPropertyId');
    this.handleFieldsOnUseTypeOrStrataChange(
      this.property.UseTypeID,
      this.propertyLocation.Condo
    );
  }
  
  private setFormFieldValidators(fieldName: string): void {
    const control = this.propertyForm.get(fieldName);
    control?.enable();
    control?.setValidators([Validators.required]);
    control?.markAsTouched();
  }
  
  private getPropertyDetails(propertyId, onPropertyFetched: () => void) {
    let apiCounter = 0;
    apiCounter++;
    this.footPrintNotAvailable = false;
    const response_location = this._propertyService.GetPropertyDetailsByPropertyId(propertyId);
    response_location.subscribe(result => {
      preparePropertyFromResult({result, component: this, form: this.propertyForm, unitId: this.UnitId, dateFormat: this.dateFormat, datePipe: this._datePipe, sharedDataService: this.sharedDataService});
    });

    apiCounter++;
    const response_location_data = this._propertyService.GetPropertyLocationByPropertyId(propertyId, this.SearchType);
    response_location_data.subscribe(result => {
      const location = preparePropertyLocationFromResult({
        result,
        newPropertyLocation: this.newPropertyLocation,
        isAnExistingProperty: this.isAnExistingProperty,
        _datePipe: this._datePipe,
        dateFormat: this.dateFormat,
        buildAddress: this.buildAddress.bind(this),
        onPropertyFetched: onPropertyFetched, // or this.onPropertyFetched if it's a method
        setTab: (tab) => this.selectedTab = tab,
      });
      
      this.propertyLocation = location;
      this.propertyLocationCopy = JSON.parse(JSON.stringify(location));
    });
    if (!this.isAnExistingProperty) {
      this.loadResearchStatus();
      this.resetControls()
    }
    if (this.isAnExistingProperty) {
      this.broadCastFetchChangeLog();
    }
    this.handleFieldsOnUseTypeOrStrataChange(this.property.UseTypeID, this.propertyLocation.Condo);
    const propertyTabExpandStatus = sessionStorage.getItem(SessionStorageKeys.PropertyDetailsTabStatus);
    this.isPropertyDetailsExpanded = propertyTabExpandStatus ? JSON.parse(propertyTabExpandStatus) : false;
    setTimeout(() => {
      this.getPropertySpecificUse();
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
      if (this.isAnExistingProperty) {
        this.broadCastNewPropertyFetch();
      }
    }, 800)

  }

  addProperty(isFreehold = false) {
    this.ShowProperties = true;
    this.isFreehold = isFreehold
  }

  getPropertySpecificUse(): void {
    const useTypeId = this.property.UseTypeID;
    if (!useTypeId || useTypeId === '0') return;
    // Clear existing specific uses
    this.specificUses = [];
    if (this.allspecificuses?.length) {
      this.specificUses = this.allspecificuses.filter(item => item.UseTypeID === useTypeId);
    } else {
      this._propertyService.GetSpecificUseByGenUseId(useTypeId).subscribe(result => {
        this.specificUses = result.body?.responseData || [];
      });
    }
  }
  
  getAdditionalSpecificUse(useTypeId) {
    let specificUses = []
    if(!!this.allspecificuses && this.allspecificuses.length > 0) {
      specificUses = [ ...this.allspecificuses ].filter((item) => item.UseTypeID == useTypeId);
    }
    return specificUses;
  }

  getLinkedProperties(propertyId: number, noOfUnits = 1) {
    const linkedPidsResponse = this._propertyService.getPropertyStrataDetails(propertyId, null, null);
    linkedPidsResponse.subscribe(result => {
      if (!result?.body?.error) {
        const linkedPids = result.body.responseData;
        const sizeOfNewChild = getSizeOfNewStrataUnit(linkedPids, this.UnitId, noOfUnits);
        this.property.BuildingSF = sizeOfNewChild?.toFixed(2);
        this.propertyForm.get('BuildingSF').setValue(sizeOfNewChild);
        this.propertyForm.get('BuildingSF').markAsDirty();
      }
    });
  }

  onAddProperties(SelectedProperties) {
    if (SelectedProperties) {
      this.propertyForm.get('MasterPropertyId').markAsDirty();
      this.propertyForm.get('MasterPropertyId').markAsUntouched();
      if (this.propertyLocation.Condo === EnumCondoTypeName.Strata) {
        this.propertyLocation.MasterPropertyId = SelectedProperties[0].PropertyID;
        this.selectedOption = SelectedProperties[0].ResearchTypeID;
        this.isAverageEstimationEnabledForMaster = !!SelectedProperties[0].IsAverageEstimationEnabled;
        if (!!SelectedProperties[0].IsAverageEstimationEnabled) {
          this.getLinkedProperties(SelectedProperties[0].PropertyID);
        }
      } else if (this.propertyLocation.Condo === EnumCondoTypeName.Child_Freehold) {
        if (SelectedProperties[0].ParcelNo === this.property.ParcelNumber) {
          this.propertyLocation.MasterPropertyId = SelectedProperties[0].PropertyID;
          //Update LotSize of the property to its Master Lotsize
          this.property.LotSizeSM = this._propertyService.convertUnit(this.CountryId, 'SF', 'SqM', SelectedProperties[0].LotSizeSF);
          this.property.LotSizeSF = this._propertyService.convertUnit(this.CountryId, 'SF', 'SqM', SelectedProperties[0].LotSizeSF);
          this.selectedOption = SelectedProperties[0].ResearchTypeID;
          this.selectedMasterParcel = SelectedProperties[0].ParcelNo;
          this.LastStrataUnit = SelectedProperties[0].LastStrataUnit ? parseInt(SelectedProperties[0].LastStrataUnit) : 0;
          if (!this.propertyLocation.CondoUnit) {
            this.propertyLocation.CondoUnit = this.LastStrataUnit + 1;
          }
        } else {
          this.completeChanges();
          this.onComplete.emit(false);
          this.footPrintNotAvailable = false;
          this._notificationService.ShowWarningMessage(CommonStrings.DialogConfigurations.Messages.MasterAndChildParcelMismatch);
        }
      }
    }
  }

  closeProperty() {
    this.ShowProperties = false;
  }

  onStrataChange() {
    this.handleFieldsOnUseTypeOrStrataChange(this.property?.UseTypeID, this.propertyLocation?.Condo);
    if (this.propertyLocation.Condo !== EnumCondoTypeName.Master_Freehold && this.propertyLocation.Condo !== EnumCondoTypeName.Master) {
      this.propertyForm.get('BuildingSF') && this.propertyForm.get('BuildingSF').setValidators([Validators.required, numericValidator(), Validators.min(1)]);
    }
    if (this.propertyLocation.Condo === EnumCondoTypeName.Strata || this.propertyLocation.Condo === EnumCondoTypeName.Child_Freehold) {
      this.propertyForm.get('CondoUnit').enable();
      this.propertyForm.get('CondoUnit').setValidators([Validators.required]);
      this.propertyForm.get('CondoUnit').markAsTouched();
      this.propertyForm.get('MasterPropertyId').enable();
      this.propertyForm.get('MasterPropertyId').setValidators([Validators.required]);
      this.propertyForm.get('MasterPropertyId').markAsTouched();
      this.propertyForm.get('LotSizeSourceID')?.clearValidators();
      this.propertyForm.get('LotSizeSourceID')?.updateValueAndValidity();
    } else {
      this.propertyForm.get('LotSizeSourceID')?.setValidators([Validators.required]);
      this.propertyForm.get('LotSizeSourceID')?.updateValueAndValidity();
    }
    if ((this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold) {
      this.multifloors = [];
      this.property.BuildingSF = 0.00;
      this.property.TypicalFloorSizeSM = 0.00;
      if (this.isAnExistingProperty) {
        //To trigger alloacations save
        this.isFootprintModified = true;
        this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
      }
    } else {
      this.isFloorBtnDisable = false;
    }

    let commModel = new CommunicationModel();
    commModel.Key = 'CondoTypeChange';
    const data: CondoTypeChangeListenerType = {
      isFloorBtnDisable: (this.propertyLocation.Condo === EnumCondoTypeName.Master || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold) ? true : false,
      Condo: this.propertyLocation.Condo,
      isAverageEstimationEnabled: this.isAverageEstimationEnabled
    }
    commModel.data = data;
    this._communicationService.broadcast(commModel);

    if (this.propertyLocation.Condo !== EnumCondoTypeName.Strata && this.propertyLocation.Condo !== EnumCondoTypeName.Child_Freehold) {
      this.propertyLocation.MasterPropertyId = null;
      this.propertyLocation.CondoUnit = null;
    }
    if (this.propertyLocation && (this.propertyLocation.Condo === EnumCondoTypeName.NotStrata || this.propertyLocation.Condo === EnumCondoTypeName.Strata ||
      this.propertyLocation.Condo === EnumCondoTypeName.Child_Freehold)) {
      this.updateAddPolygonButtonDisable()
    }
    this.setSelectedPropertyCondoType.emit(this.propertyLocation.Condo);
    if (!this.isAnExistingProperty && (this.propertyLocation.Condo !== EnumCondoTypeName.Strata && this.propertyLocation.Condo !== EnumCondoTypeName.Child_Freehold)) {
      this.checkForParcelLayer();
    }
  }

  showStrataConfirmation() {
    if (this.isAnExistingProperty && this.propertyLocation.Condo != EnumCondoTypeName.Strata && this.propertyLocation.Condo != EnumCondoTypeName.Master &&
      this.propertyLocation.Condo != EnumCondoTypeName.Child_Freehold && this.propertyLocation.Condo != EnumCondoTypeName.Master_Freehold && this.selectedTab == EditPropertyTabs.Strata) {
      this.selectedTab = EditPropertyTabs.Property;
    }
    this.setSelectedPropertyCondoType.emit(this.propertyLocation.Condo);
    const previousCondoType = this.propertyLocationCopy.Condo;
    if (previousCondoType === EnumCondoTypeName.Strata || previousCondoType === EnumCondoTypeName.Child_Freehold) {
      if (this.isAnExistingProperty) {
        const message = CommonStrings.DialogConfigurations.Messages.StrataTypeChangeConfirmationMessage;
        const title = CommonStrings.DialogConfigurations.Title.Arealytics;
        const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: () => { this.propertyLocation.MasterPropertyId = null;
          this.strataChange(this.propertyLocation.Condo);}}
        const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { this.propertyLocation.Condo = previousCondoType; }}
        this.showDialogConfirmation(message, title, okButton, cancelButton);
      } else {
        this.propertyLocation.MasterPropertyId = null;
        this.strataChange(this.propertyLocation.Condo);
      }
    }
    else if (this.propertyLocationCopy.Condo === EnumCondoTypeName.Master) {
      const response_contacts = this._propertyService.getPropertyStrataDetails(this.displayPropertyId, null, null);
      response_contacts.subscribe(result => {
        if (!result.body.error) {
          var propertyStrataList = result.body.responseData || [];
          if (this.propertyLocationCopy.Condo === EnumCondoTypeName.Master && propertyStrataList.length > 1) {
            this.showMasterStrataAlert = true;
            this.propertyLocation.Condo = this.propertyLocationCopy.Condo;
            this.propertyForm.get('Condo').disable();
          } else if (this.propertyLocation.Condo === EnumCondoTypeName.Master && propertyStrataList.length > 1) {
            this.showMasterStrataAlert = true;
            this.propertyForm.get('Condo').disable();
          } else {
            this.showMasterStrataAlert = false;
            this.propertyForm.get('Condo').enable();
          }
        }
      });
    }
    else if (this.propertyLocationCopy.Condo === EnumCondoTypeName.Master_Freehold) {
      const response_contacts = this._propertyService.getPropertyFreeholdDetails(this.displayPropertyId, null, null);
      response_contacts.subscribe(result => {
        if (!result.body.error) {
          var propertyFreeholdList = (result && result.body && result.body.responseData && result.body.responseData[0]) || [];
          if (this.propertyLocationCopy.Condo === EnumCondoTypeName.Master_Freehold && propertyFreeholdList.length > 1) {
            this.showMasterStrataAlert = true;
            this.propertyLocation.Condo = this.propertyLocationCopy.Condo;
            this.propertyForm.get('Condo').disable();
          } else if (this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold && propertyFreeholdList.length > 1) {
            this.showMasterStrataAlert = true;
            this.propertyForm.get('Condo').disable();
          } else {
            this.showMasterStrataAlert = false;
            this.propertyForm.get('Condo').enable();
          }
        }
      });
    }
  }

  strataChange(condo) {
    if (condo !== EnumCondoTypeName.Strata || condo !== EnumCondoTypeName.Child_Freehold) {
      this.propertyForm.get('CondoUnit').markAsDirty();
      this.propertyForm.get('MasterPropertyId').markAsDirty();
      this.propertyForm.get('CondoUnit').markAsUntouched();
      this.propertyForm.get('MasterPropertyId').markAsUntouched();
      this.propertyLocation.MasterPropertyId = null;
      this.propertyLocation.CondoUnit = null;
    }
  }
  resetControls() {
    this.showMasterStrataAlert = false;
    this.propertyForm.get('Condo').enable();
  }

  buildAddress() {
    const addressDetails = buildAddressDetails({
      propertyLocation: this.propertyLocation,
      streetSuffixes: this.streetSufixes,
      cities: this.cities,
      states: this.states
    });
    this.StreetNumber = addressDetails.StreetNumber;
    this.StreetSuffix1Text = addressDetails.StreetSuffix1Text;
    this.CityName = addressDetails.CityName;
    this.StateName = addressDetails.StateName;
  }


  checkStrataMinMaxError() {
    const pRangeMin = this.rangeForm.get('StrataMin').value;
    const pRangeMax = this.rangeForm.get('StrataMax').value;
    const strataMin = parseInt(pRangeMin);
    const strataMax = parseInt(pRangeMax);

    if (!strataMin || !strataMax) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMinMaxUnitMessage);
      this.StrataMinMaxError = true;
      return;
    }
    const units = strataMax - strataMin;

    if (strataMax <= strataMin) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMaxUnitMessage);
      this.StrataMinMaxError = true;
      return;
    }

    if ((units + 1) > 30) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataUnitsExceedingMessage);
      this.StrataMinMaxError = true;
      return
    }
    if (this.propertyLocation.Condo === EnumCondoTypeName.Strata && this.isAverageEstimationEnabledForMaster) {
      this.getLinkedProperties(this.propertyLocation.MasterPropertyId, units + 1);
    }
    this.StrataMinMaxError = false;
  }

  setLotInformation() {
    if (this.selectedParcelInfo) {
      this.property.LotSizeSF = this.selectedParcelInfo.Lot_Area;
      // Set the default value of LotSizeSource to 'County Source Data'.
      if (!this.property.LotSizeSourceID) {
        this.property.LotSizeSourceID = EnumSizeSource.CountyDataSource;
      }
    } 
  }

  onPropertyUseChange(valueChanged = false, UseTypeID, previousUseType) {
    if (!!UseTypeID && valueChanged) {
      const value = UseTypeID;
      if (UseTypeID === this.propertyTypeValues.Land) {
        this.IsUsetypeChanged = true;
        if(this.multifloors.length>0){
          this.multiFloorConfirmation(previousUseType);
        }else{
          this.propertyLocation.Condo = null;
          this.propertyLocation.MasterPropertyId = null;
          this.propertyLocation.CondoUnit = null;
          this.property.BuildingSF = 0;
        }
      }
    }
    if (!!event) {
      const value = UseTypeID;
      this.addSpecificValidation(value);

    }
    this.isIndustrial = false;
    this.isOffice = false;
    this.isRetail = false;
    switch (~~this.property.UseTypeID) {
      case 3:
        this.isIndustrial = true;
        break;
      case 2:
        this.isRetail = true;
        break;
      case 5:
        this.isOffice = true;
        break;
    }
    if (this.property.UseTypeID && this.property.UseTypeID != '0') {
      if (!!valueChanged) {
        this.property.SpecificUseID = 0;
        this.propertyForm.get('SpecificUse').updateValueAndValidity();
      }

      this.specificUses = [];
      if (!!this.allspecificuses && this.allspecificuses.length > 0) {
        this.allspecificuses.forEach(item => {
          if (item.UseTypeID == this.property.UseTypeID) {
            this.specificUses.push(item);
          }
        });
      } else {
        const response_constructtype = this._propertyService.GetSpecificUseByGenUseId(this.property.UseTypeID);
        response_constructtype.subscribe(result => {
          this.specificUses = result.body.responseData || [];
        });
      }
    }
  }

  checkForValidFloorSizes = () => {
    const isMasterFreehold = this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold;
    const isMaster = this.propertyLocation.Condo === EnumCondoTypeName.Master;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;
    const isValidBuildingSF = this.propertyForm.controls.BuildingSF.valid;
    if (isMasterFreehold || isLand || this.footPrintNotAvailable) {
      return true;
    }
    if (isMaster) {
      return this.isAverageEstimationEnabled ? isValidBuildingSF : true;
    }
    return isValidBuildingSF;
  }

  checkForMandatoryFieldsinTopSection() {    
      const isLotSizeSourceValid =    this.propertyForm?.controls['LotSizeSourceID']?.enabled ? this.propertyForm?.controls['LotSizeSourceID']?.valid : true;
      const isRecordTypeValid = this.property?.UseTypeID === this.propertyTypeValues?.Land ? true :  this.propertyForm?.controls['Condo']?.valid;
      const isConstructionStatus = this.propertyForm.controls['ConstructionStatus']?.disabled ? true : this.propertyForm.controls['ConstructionStatus']?.valid
      return (this.propertyForm.controls['ConstructionType']?.valid && isConstructionStatus && isLotSizeSourceValid && isRecordTypeValid);
  }

  isPinLocationValid() {
     // For Master records with average estimation enabled, check whether the marker lies within any of the polygons
     if (this.propertyLocation.Condo === EnumCondoTypeName.Master && this.isAverageEstimationEnabled) {
      return this.multifloors.some((floor) => {
        return this._mapService.checkPinLocationWithFootprint(floor.shape, {lat: Number(this.propertyLocation.Latitude), lng: Number(this.propertyLocation.Longitude)})
      })
    }
    // For Master without average estimation, Master Freehold, or when footprints are not available, no marker check is required
    if ((this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled)|| this.propertyLocation.Condo == EnumCondoTypeName.Master_Freehold  || this.footPrintNotAvailable ) {
      return true;
    }
    if (!this.multifloors || !this.multifloors.length) {
      return true;
    } else {
      return this.multifloors.some((floor) => {
        return this._mapService.checkPinLocationWithFootprint(floor.shape, {lat: Number(this.propertyLocation.Latitude), lng: Number(this.propertyLocation.Longitude)})
      })
    }
  }

  async savePropertyDetailsAndLocation(isAutoClose) {
    const changed = JSON.stringify(this.propertyForm.value) != this.initialPropertyForm;
    const isValid = this.propertyForm.dirty || changed || this.property.IsReviewed;
    const isValidMultiStrata = this.isMultiStrata ? this.isMultiStrata && !this.StrataMinMaxError : true;
    let researchStatusValidationError: boolean = false;
    //Research status validation check
    if (this.selectedOption === ResearchType.NeedsResearch) {
      if ((this.property && (this.property.IsMultiplePolygonsNeeded || (this.property.NeedsResearchComments && this.property.NeedsResearchComments.length >= 25))) || false
      ) {
        researchStatusValidationError = false;
      } else {
        researchStatusValidationError = true;
      }
    }

    if (!this.property.UseTypeID) {
      const message = CommonStrings.DialogConfigurations.Messages.SelectPropertyUseMessage;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok };
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    }

    const formName = getFormNameByUseType(this.property?.UseTypeID); 
    const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
    if (!useTypeForm) {
      return;
    }

    // check for mandatory fields and validations
    if (this.isAnExistingProperty && this.propertyLocation?.Condo === EnumCondoTypeName.Master) {
      let areAllFieldsValid = true;
      if (!useTypeForm?.valid) {
        const controls = useTypeForm?.controls;
        //Skip check for LargestFloor and SmallestFloor if record type is Master
        for (const key in controls) {
          if (key !== OfficeControls?.LargestFloor && key !== OfficeControls?.SmallestFloor) {
            const control = controls[key];
            if (control && !control.valid && control.enabled) {
              areAllFieldsValid = false;
            }
          }
        }
      }
      if (!this.propertyForm?.get('locationDetailsForm')?.valid || !areAllFieldsValid) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
        return;
      }
    } else if (this.isAnExistingProperty && (!this.propertyForm?.get('locationDetailsForm')?.valid || (!useTypeForm?.valid && this.propertyLocation?.Condo !== EnumCondoTypeName?.Master_Freehold))) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
      return;
    } 

    const isMasterStrata = (this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || this.isMultiStrata;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;

    const isBuildingSqmLessThanOne = this.propertyForm.controls['BuildingSF'].value ? parseInt(this.propertyForm.controls['BuildingSF'].value) < 1 : true;
    // If average estimation is enabled (either for master or strata), 
    // and footprint is available (i.e., footPrintNotAvailable is false),
    // and BuildingSF is either invalid or less than 1,
    // then show error and stop further execution.
    const isAverageEstimationActive = this.isAverageEstimationEnabled || this.isAverageEstimationEnabledForMaster;
    const isBuildingSFInvalid = !this.propertyForm.get('BuildingSF')?.valid && isBuildingSqmLessThanOne;

    if (isAverageEstimationActive && !this.footPrintNotAvailable && isBuildingSFInvalid) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage);
      return;
    }
    if (this.footPrintNotAvailable) {
      if (this.isAnExistingProperty) {
        if (useTypeForm.get('ContributedGBA_SF')?.valid && !useTypeForm.get('ContributedGBA_SF')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBAErrorMessage);
          return;
        } else if (!useTypeForm.get('ContributedGBASource')?.valid) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBASourceErrorMessage);
          return;
        }
      } else {
        if (this.propertyForm.get('ContributedGBA_SF')?.valid && !this.propertyForm.get('ContributedGBA_SF')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBAErrorMessage);
          return;
        } else if (!this.propertyForm.get('ContributedGBASource')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBASourceErrorMessage);
          return;
        }
      } 
    } else if (!isMasterStrata && !isLand) {
      if (isBuildingSqmLessThanOne) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
        return
      }
    } else {
      if (this.isMultiStrata && isBuildingSqmLessThanOne) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage);
        return
      }
    }
    if ((isValid|| this.researchChanged || this.propertyLocationChanged || this.isDeleteFloor || this.isFootprintModified || this.property.IsSkipped !== this.IsSkipped)&& this.isPinLocationValid() && this.checkForMandatoryFieldsinTopSection() && !researchStatusValidationError && this.selectedOption && isValidMultiStrata && this.checkForValidFloorSizes()) {
      const strataValidationError = this.propertyLocation.Condo === EnumCondoTypeName.Strata && !this.isMultiStrata &&
        (!this.propertyForm.controls[PropertyFormControls.MasterPropertyId].valid || !this.propertyForm.controls[PropertyFormControls.CondoUnit].valid);
      if (!!this.property.UseTypeID && this.checkMasterSizes() && !this.clearHeightMinError && !this.streetMinMaxError && !this.dockHighError && !this.floorLoadingError && !this.buildingSFError && !this.noOfOfficeFloorError && !this.yearRenovatedError && !strataValidationError && !this.GRESBScoreError) {
        if (this.isMultiStrata || this.property.PropertyID || await this.hasbothStreetAndAerialImages()) {
          this.setLotInformation();
          this.validationError = false;
          this.redirectionLoader = true;
          this.property.HasNoBuildingFootprints = this.footPrintNotAvailable ? 1 : 0;
          this.property.IsAverageEstimationEnabled = this.isAverageEstimationEnabled ? 1 : 0;
          this.property.IsLettableOverrideEnabled = this.property.IsLettableOverrideEnabled ? 1 : 0;
          this.mapChangeLog();
          if (this.isAnExistingProperty) {
            this.property.ChangeLogJSON = JSON.stringify(this.DataArray);
          }
          this.property.ApplicationID = EnumApplication.VST;
          if (this.property.HasNoBuildingFootprints) {
            this.property.SizeSourceID = null;
          } else if (!this.isAnExistingProperty) {
            this.property.SizeSourceID = EnumSizeSource.AerialMeasurement; // Aerial estimation
          } else if (this.isAnExistingProperty && !this.property.HasNoBuildingFootprints && this.property.SizeSourceID != EnumSizeSource.AerialMeasurement) {
            if (this.propertyLocation.Condo != EnumCondoTypeName.Master && this.propertyLocation.Condo != EnumCondoTypeName.Master_Freehold) {
              this.property.SizeSourceID = EnumSizeSource.AerialMeasurement;
            }
          }
          if (!this.isAnExistingProperty) {
            this.property.SizeSourceID = EnumSizeSource.AerialMeasurement; // Aerial Measurement
          }
          let propertyCopy = JSON.parse(JSON.stringify(this.property));
          this.footPrintNotAvailable = false;

          preparePropertyData(propertyCopy, this.property, this.isAnExistingProperty, this.hasNoExistingParcelInTileLayer, this.CountryId, this._propertyService);
          preparePropertyLocationData(this.propertyLocation, this.property, propertyCopy, this.initialDetails, this.isAnExistingProperty, this.isMultiStrata, this.selectedMasterParcel, this.CountryId, this.EntityID, this.DataArray, this.propertyTypeValues, this.addressTypeValues, this.streetPrefixes, this.streetSufixes, this.quadrants);

          // Save or update allocation only when footprints are modified,
          // and the record is neither Master Freehold nor Master Strata without average estimation
          if (this.isAnExistingProperty && this.isFootprintModified && ((this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold)) {
            this.propertyAllocation?.updateAllocationsData([], this.property.PropertyID);
            this.savePropertyAlloction([], this.property.PropertyID);
          }

          if (this.isMultiStrata && this.property.PropertyID === 0) {
            this.propertyCopy = propertyCopy;
            const pRangeMin = parseInt(this.pRange.StrataMin);
            const pRangeMax = parseInt(this.pRange.StrataMax);
            this.saveMultiStrata(pRangeMin, pRangeMax, true);
          } else if (!!this.property.PropertyID && this.sharedDataService.deleteBuildingFootPrintIds && this.sharedDataService.deleteBuildingFootPrintIds.length > 0) {
            var buildingFootPrintIDArr = this.sharedDataService.deleteBuildingFootPrintIds;
            this.buildingFootprintService.deleteBuildingFootPrint(buildingFootPrintIDArr, this.property.PropertyID).subscribe(
              (result: any) => {
                if (!result.error) {
                  this.savePropertyLocation(propertyCopy, isAutoClose);
                } else {
                  this.redirectionLoader = false;
                }
              }
            );
          } else {
            this.savePropertyLocation(propertyCopy, isAutoClose);
          }
          this.sharedDataService.deleteBuildingFootPrintIds = [];
        } else {
          this.addPropertyImages();
        }
      }
      else {
        this.validationError = true;
      }
      this.sharedDataService.deleteBuildingFootPrintIds = [];
      this.researchChanged = false;
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
    }
    else {
      if (!this.checkForValidFloorSizes() && this.isAnExistingProperty) {
        // Show building size error when:
        // - Record type is NOT Master Freehold
        // - NOT Master Strata without average estimation
        // - Master Strata WITH average estimation
        // - Use type is NOT 'Land'
        if (this.propertyLocation.Condo !== EnumCondoTypeName.Master_Freehold && !isLand && !this.propertyForm.controls['BuildingSF'].valid && (this.propertyLocation.Condo !== EnumCondoTypeName.Master || (this.isAverageEstimationEnabled && this.propertyLocation.Condo == EnumCondoTypeName.Master))) {
          isMasterStrata ? this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage) : this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
        }
      }  else if (this.propertyLocation.Condo !== EnumCondoTypeName.Master_Freehold && (this.propertyLocation.Condo !== EnumCondoTypeName.Master || (this.isAverageEstimationEnabled && this.propertyLocation.Condo == EnumCondoTypeName.Master)) && !this.footPrintNotAvailable && !isLand && !this.propertyForm.controls['BuildingSF'].valid) {
        // Show building size error when:
        // - Record type is NOT Master Freehold
        // - NOT Master Strata without average estimation
        // - Master Strata WITH average estimation
        // - Use type is NOT 'Land'
        // - footprint not available false
        isMasterStrata ? this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage) : this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
      } else if (researchStatusValidationError) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MinimumCommentsLengthErrorMessage);
      } else if (this.StrataMinMaxError) {
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMinMaxUnitMessage);
      } else if (!this.selectedOption) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ResearchStatusErrorMessage);
      } else if (!this.isPinLocationValid()) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PinInsideFootPrint);
      } else if (!this.checkForMandatoryFieldsinTopSection()){
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
      } else {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.NoPropertyChanges);
      } 
    }
  }

  isPropertyNameAndAddressValid(){
    return this.propertyLocation.PropertyName && this.propertyLocation.AddressStreetName && this.propertyLocation.StreetNumberMin;
  }

  onClosePropertyNameModal(){
    this.showPropertyNameModal = false;
  }

  onSavePropertyAddress(){
    this.showPropertyNameModal = false;
    //Build property name from location details
    this.addressAsPropertyName(false);
    this.checkForMultiFloors(this.isAutoClose);
  }

  // propertySave(isAutoClose: boolean = true) { 
  //   if (!this.propertyLocation.Latitude && !this.propertyLocation.Longitude && !this.isAnExistingProperty) {
  //     const message = CommonStrings.DialogConfigurations.Messages.DropPinMessage;
  //     const title = CommonStrings.DialogConfigurations.Title.Arealytics;
  //     const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok };
  //     const cancelButton = { Visible: false };
  //     this.showDialogConfirmation(message, title, okButton, cancelButton);
  //     return
  //   }
  //   if (this.isAnExistingProperty) {
  //     this.checkForMultiFloors(isAutoClose);
  //   } else {
  //     if (this.isPropertyNameAndAddressValid()) {
  //       this.checkForMultiFloors(isAutoClose);
  //     } else {
  //       this.showPropertyNameModal = true;
  //       this.isAutoClose = isAutoClose;
  //     }
  //   }

  // }






  propertySave(isSaveAndNext: boolean) {
    this.validationError = false;
    this.floorLoadingError = false;
    this.yearBuildError = false;
    this.yearRenovatedError = false;
    this.GRESBScoreError = false;

    if (this.propertyForm.valid && this.rangeForm.valid && !this.validationError && !this.floorLoadingError && !this.yearBuildError && !this.yearRenovatedError && !this.GRESBScoreError && this.checkMasterSizes()) {
      this.pendingIsSaveAndNext = isSaveAndNext;

      // Check for sale transactions that need updating before saving
      if (this.isAnExistingProperty) {
        this.checkSaleTransactionsBeforeSave(isSaveAndNext);
      } else {
        this.saveTheProperty(isSaveAndNext);
      }
    } else {
      this.isInitialProperty = true;
      if (!this.checkMasterSizes()) {
        this._notificationService.ShowErrorMessage('Please fill all the required fields');
      }
    }
  }

  private checkSaleTransactionsBeforeSave(isSaveAndNext: boolean) {
    // Use the update component to fetch and check sales
    this.sales = []; // Reset sales to let update component fetch them
    this.showUpdateSoldSqmModal = true;

    // The update component will automatically emit continue if no updates are needed
    // or show the modal if updates are required
  }
  
  private saveTheProperty(isSaveAndNext: boolean) {
    this.redirectionLoader = true;
    let newProperty = JSON.parse(JSON.stringify(this.property));
    newProperty.LotSizeSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', newProperty.LotSizeSF);
    newProperty.EntityID = this.EntityID;
    newProperty.PropertyResearchTypeID = this.selectedOption;
    newProperty.PropertyResearchTypeName = this.propertyResearchStatus.find(status => status.PropertyResearchTypeID == this.selectedOption)?.PropertyResearchTypeName;
    delete newProperty.ChangeLogJSON;
    newProperty.ConstructionStartDate = newProperty?.ConstructionStartDate ? newProperty?.ConstructionStartDate?.singleDate?.formatted : null;
    newProperty.EstCompletionDate = newProperty?.EstCompletionDate ? newProperty?.EstCompletionDate?.singleDate?.formatted : null;
    newProperty.ActualCompletion = newProperty?.ActualCompletion ? newProperty?.ActualCompletion?.singleDate?.formatted : null;
    newProperty.TitleReferenceDate = newProperty?.TitleReferenceDate ? newProperty?.TitleReferenceDate?.singleDate?.formatted : null;
    newProperty.BookValueDate = newProperty?.BookValueDate ? newProperty?.BookValueDate?.singleDate?.formatted : null;
  
    const formValue = this.propertyForm.value;
    const nestedFormValue = this.getFormGroupName();
    let specificUseType = null;
    let specificUseTypeName = null;
    if (nestedFormValue && formValue[nestedFormValue]?.SpecificUseTypeID) {
      specificUseType = formValue[nestedFormValue].SpecificUseTypeID;
      specificUseTypeName = this.allspecificuses.find(x => x.SpecificUseTypeID == formValue[nestedFormValue].SpecificUseTypeID)?.SpecificUseTypeName;
    }
    newProperty.SpecificUseTypeID = specificUseType;
    newProperty.SpecificUseTypeName = specificUseTypeName;
  
    const propertyLocation = JSON.parse(JSON.stringify(this.propertyLocation));
    propertyLocation.EntityID = this.EntityID;
    delete propertyLocation.ChangeLogJSON;
  
    if (this.isMultiStrata) {
      this.saveMultiStrata(Number(this.pRange.StrataMin), Number(this.pRange.StrataMax), isSaveAndNext);
    } else {
      preparePropertyData(
        newProperty, // propertyCopy
        this.property, // property (original Property object)
        this.isAnExistingProperty, // isAnExistingProperty
        this.hasNoExistingParcelInTileLayer, // hasNoExistingParcelInTileLayer
        this.CountryId, // CountryId
        this._propertyService // _propertyService
      );
      const response = this._propertyService.PropertySave(newProperty);
      response.subscribe({
        next: (result) => {
          if (!result.body.error) {
            const propertyId = result.body.responseData[0].Ret_PropId;
            this.savePropertyParcelInformation(propertyId);
            
            if (isSaveAndNext && this.isAnExistingProperty) {
              this.onNext(true);
            } else {
              this.completeChanges();
              this.onComplete.emit(false);
            }
            this.redirectionLoader = false;
          } else {
            this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
            this.redirectionLoader = false;
          }
        },
        error: (err) => {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
          this.redirectionLoader = false;
        }
      });
    }
  }





  checkForMultiFloors(isAutoClose){
    // If a new property is being added, and it does not have a lot size, and the condo type is neither Strata nor Child Freehold, display a notification prompting the user to select a parcel.
    if (!this.isAnExistingProperty && (!this.property?.LotSizeSF || !this.property?.LotSizeSM) && this.propertyLocation.Condo !== EnumCondoTypeName?.Strata && this.propertyLocation?.Condo !== EnumCondoTypeName?.Child_Freehold) {
      this.checkForParcelLayer();
      return;
    } 
    const isMasterStrata = (this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || this.isMultiStrata;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;
    let floorsWithFootprint = [];
    if(isLand){
      this.multifloors = []
    }else{
      this.multifloors.forEach(floor => {
        if (floor.floorSize) {
          const range = (floor.minFloor && floor.maxFloor) ? getRange(floor.minFloor, floor.maxFloor) : floor.minFloor ? [floor.minFloor] : [floor.maxFloor];
          floorsWithFootprint = [...floorsWithFootprint, ...range];
        }
      });  
    }
    const propertyRange = getRange(1, this.property.Floors);

    //Showing error if any of the polygons are missing bulding footprints
    const isFootPrintsMissing = this.multifloors.some(floor => !floor.floorSize || !floor.shape);
    if (isFootPrintsMissing) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintsAreMissing);
      return;
    }

    const allFloorsAllocated = propertyRange.every(element => floorsWithFootprint.includes(element)) || isMasterStrata || isLand || this.footPrintNotAvailable || this.isAverageEstimationEnabledForMaster;
    if (!isMasterStrata && !this.footPrintNotAvailable && !this.multifloors.some(floor => floor.floorSize > 0) && !isLand && !this.isAverageEstimationEnabledForMaster) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.AtleastOneFootprintIsRequired);
      return;
    }

    if (!allFloorsAllocated) {
      const message = CommonStrings.DialogConfigurations.Messages.AllFloorsDoesntHaveFootprintMessage;
      const title = CommonStrings.DialogConfigurations.Title.FloorwisePolygon;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: () => { this.savePropertyDetailsAndLocation(isAutoClose) } };
      const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.No, Callback: () => { } };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    } else {
      this.savePropertyDetailsAndLocation(isAutoClose);
    }
  }

  savePropertyLocation(propertyCopy: any, isAutoClose: boolean = true) {
    const response_location = this._propertyService.propertyLocationSave(this.propertyLocation);
    response_location.subscribe(result => {
      if (result.status === 201) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
      } else if (result.status === 200) {
        if (!result.body.error) {
          const PropertyData = result.body.responseData;
          this.property.PropertyID = propertyCopy.PropertyID = PropertyData[0][0].Ret_PropId;
          propertyCopy.EntityID = this.EntityID;
          propertyCopy.CountryID = this.CountryId;
          propertyCopy.IsSkipped = this.IsSkipped;
          this.propertyLocationChanged = false;
          this.sharedDataService.deleteBuildingFootPrintIds = [];
          this.isAddPolygonButtonDisabled = false;
          this.savePropertyDetails(propertyCopy, isAutoClose);
          this.clearParkingRetailAndHardStandPolygons();
            if (!this.isAnExistingProperty) {
            this.fetchAndProcessStagingMedia(this.property.PropertyID)   //processing the staging media before procedding further
          }
        } else {
          this.redirectionLoader = false
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
        }
      }
    });
  }

  savePropertyDetails(property: Property, isAutoClose: boolean = true) {
    const clearEmptyStrings = (property: any, fields: string[]) => {
      fields.forEach(field => {
        if (property[field] === "") property[field] = null;
      });
    };
    clearEmptyStrings(property, [
      'LotSizeSourceID', 'ConstructionTypeID', 'BuildingClass', 'IsEnergyStar',
      'IsOwnerOccupied', 'RoofTypeID', 'HasReservedCoveredParking', 'GRESBScore'
    ]);
    
    property.ClassTypeID = property.BuildingClass || null;

    const response_property = this._propertyService.propertyDetailsSave(property);
    response_property.subscribe(result => {

      if (result.status === 201) {
      } else if (result.status === 200) {
        if (!result.body.error) {
          if (this.isFootprintModified && this.multifloors && this.multifloors.length > 0) {
            const MultiBuildingFootPrintData = this.multifloors
              .map(({ shape, BuildingFootPrintID, floorSize, minFloor, maxFloor, specificUse, description, additionalUse, additionalSpecificUseTypeId, mainSpecificUseTypeId }) => ({
                Shape: shape ? `POLYGON((${shape}))` : null,
                BuildingFootPrintID: BuildingFootPrintID || null,
                Area: floorSize,
                MinFloorNumber: minFloor || maxFloor,
                MaxFloorNumber: maxFloor || minFloor,
                UseTypeId: specificUse,
                Description: description,
                AdditionalUseTypeId: additionalUse,
                AdditionalSpecificUseTypeId: additionalSpecificUseTypeId,
                MainSpecificUseTypeId: mainSpecificUseTypeId
              }))
              .filter(item => item.Shape);
            const requestBody = { MultiBuildingFootPrintData: JSON.stringify(MultiBuildingFootPrintData), PropertyID: property.PropertyID, IsNewProperty: !this.isAnExistingProperty, ApplicationID: EnumApplication.VST};
            this.buildingFootprintService.saveMultiFootprint(requestBody).subscribe(async (result: any) => {
              const response = result.body;
              if (!response.error) {
                let commModel = new CommunicationModel();
                commModel.Key = 'updatedBuildingFootprintAddedForProperty';
                commModel.data = { result, PropertyID: property.PropertyID };
                this._communicationService.broadcast(commModel);
                this.redirectionLoader = false;
                this.isFootprintModified = false;
                await this.savePropertyAlloction(response?.responseData, property.PropertyID);
              } else {
                this.redirectionLoader = false;
              }
            })
            this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
            localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
            this.multifloors = [];
            this.floorsWithFootprint = {};
          } else {
            this.redirectionLoader = false;
          }
          if (this.isAnExistingProperty) {
            if (this.shouldUpdateLotSize) {
              this.shouldUpdateLotSize = false;
              this.propertyForm.reset();
              this.DataArray = [];
              this.fetchProperty.emit(property.PropertyID as any)
            } else {
              this.savePropertyResearchStatus(property.PropertyID);
              this.saveNewPropertyParcelInformation();
              if (this._loginService.UserInfo.RoleID == this.userRoles.Auditor) {
                const auditStatus = this.auditStatusList.find(status => status.StatusName == "Corrected and Verified");
                this.onNext(isAutoClose, auditStatus.StatusDefinationID, true);
              } else {
                this.onSave(isAutoClose, property.PropertyID);
                this._notificationService.ShowSuccessMessage('Property Saved Successfully');
              }
              this.propertyForm.reset();
              this.DataArray = [];
              this.broadcastPropertySaveEvent();
              this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
              localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
            }
          } else {
            this.savePropertyResearchStatus(property.PropertyID);
            this.savePropertyParcelInformation(property.PropertyID);
            this.onSave(isAutoClose, property.PropertyID);
            this.broadcastPropertySaveEvent();
            this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull);
            this.propertyForm.reset();
            this.DataArray = [];
          }
        } else {
          this.redirectionLoader = false;
          this.onSave(isAutoClose, this.property.PropertyID);
          this.broadcastPropertySaveEvent();
        }
      } else {
        this.onSave(isAutoClose, this.property.PropertyID);
        if (!this.isAnExistingProperty) {
          this.isAddPolygonButtonDisabled = false;
        }
      }
    });
  }

  // Triggered when the user changes the research status of the property
  onResearchStatusChange(PropertyResearchTypeID, PropertyResearchStatusID, status) {
    const previousResearchStatus = this.propertyResearchStatusInput.PropertyResearchTypeID ?? this.property.PropertyResearchTypeID;
     // If the property already exists and the new status is "Hidden" or previous status is hidden, show confirmation
    if (this.isAnExistingProperty && (PropertyResearchTypeID === ResearchType.Hidden || previousResearchStatus === ResearchType.Hidden)) {
      this.showHideUnhidePropertyConfirmationPopup(PropertyResearchTypeID, PropertyResearchStatusID, status);
    } 
     // For all other cases, update the research status directly
    else {
      this.updateResearchStatusInput(PropertyResearchTypeID, PropertyResearchStatusID, status)
    }
  }

  showHideUnhidePropertyConfirmationPopup(PropertyResearchTypeID, PropertyResearchStatusID, status) {
    // Fallback to property’s current status if input doesn't exist
    const previousResearchStatusId = this.propertyResearchStatusInput.PropertyResearchTypeID ?? this.property.PropertyResearchTypeID;
    // Get the names of previous and current statuses for display
    const previousResearchStatusName = this.propertyResearchStatus.find(status => status.PropertyResearchTypeID == previousResearchStatusId)?.PropertyResearchTypeName;
    const currentResearchStatus = this.propertyResearchStatus.find(status => status.PropertyResearchTypeID == PropertyResearchTypeID)?.PropertyResearchTypeName;
    // Build confirmation message
    const message =  `${CommonStrings.DialogConfigurations.Messages.UpdatePropertyResearchStatusConfirmationMessage} from <b>${previousResearchStatusName}</b> to <b>${currentResearchStatus}</b>?`;
    let dialogConfiguration: confirmConfiguration = new confirmConfiguration();
    dialogConfiguration.Message = message;
    dialogConfiguration.Title = CommonStrings.DialogConfigurations.Title.UpdatePropertyStatus
    dialogConfiguration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.UpdateStatus;
    dialogConfiguration.OkButton.Callback = () => {
      // On confirm, proceed with status update
      this.updateResearchStatusInput(PropertyResearchTypeID, PropertyResearchStatusID, status)
    };
    dialogConfiguration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.No;
    dialogConfiguration.CancelButton.Callback = () => {
      // On cancel, revert selection to previous status
      this.selectedOption = previousResearchStatusId;
    };
    this._notificationService.CustomDialog(dialogConfiguration);
  }

  // Updates the input model with the new research status
  updateResearchStatusInput(PropertyResearchTypeID, PropertyResearchStatusID, status) {
    this.propertyResearchStatusInput.EntityID = this.EntityID;
    this.propertyResearchStatusInput.PropertyID = this.property.PropertyID;
    this.propertyResearchStatusInput.IsActive = status.target.value === "on";
    this.propertyResearchStatusInput.PropertyResearchTypeID = PropertyResearchTypeID;
    this.propertyResearchStatusInput.PropertyResearchStatusID = PropertyResearchStatusID;
    this.selectedOption = PropertyResearchTypeID;
    this.clearValidator(PropertyResearchTypeID, status);
    this.researchChanged = true
  }

  clearValidator(PropertyResearchTypeID, status) {
    if (PropertyResearchTypeID === 4) {

      if (status) {
        this.propertyForm.clearValidators();
        this.propertyForm.setErrors({ 'invalid': false });
        this.propertyForm.setErrors(null);
        if (!this.isAnExistingProperty) {
          this.applyValidation('clear');
        }

      } else {
        if (!this.isAnExistingProperty) {
          this.addSpecificValidation(this.property.UseTypeID ? this.property.UseTypeID : 5);
        }
      }
      this.applyValidation('update');
    }
  }
  savePropertyResearchStatus(propertyId = undefined,fetchResearchStatusesonSave = true) {
    if ((this.propertyLocation.Condo == EnumCondoTypeName.Strata || this.propertyLocation.Condo === EnumCondoTypeName.Child_Freehold) && this.selectedOption) {
      const selectedResearchStatus = this.propertyResearchStatus.find(status => status.PropertyResearchTypeID == this.selectedOption);
      if (selectedResearchStatus) {
        selectedResearchStatus.IsActive = true;
        selectedResearchStatus.EntityID = this.EntityID;
        selectedResearchStatus.PropertyID = propertyId ? propertyId : this.property.PropertyID;
        selectedResearchStatus.ApplicationID = EnumApplication.VST;
        selectedResearchStatus.IsNewProperty = !this.isAnExistingProperty;
        const response_researchStatus = this._propertyService.propertyResearchStatusSave(selectedResearchStatus);
        response_researchStatus.subscribe(result => {
          if (!result.body.error) {
            if(fetchResearchStatusesonSave){
            this.getPropertyResearchStatusFromSummary();
            }
          }
        });
      }
    } else {
      this.propertyResearchStatus.forEach(updated => {
        if (updated.PropertyResearchTypeID == this.propertyResearchStatusInput.PropertyResearchTypeID) {
          updated.IsActive = this.propertyResearchStatusInput.IsActive;
          updated.PropertyResearchStatusID = this.propertyResearchStatusInput.PropertyResearchStatusID;
          updated.EntityID = this.EntityID;
          updated.PropertyID = propertyId ? propertyId : this.property.PropertyID;
          updated.ApplicationID = EnumApplication.VST;
          updated.IsNewProperty = !this.isAnExistingProperty;
          const response_researchStatus = this._propertyService.propertyResearchStatusSave(updated);
          response_researchStatus.subscribe(result => {

            if (result.status === 201) {
            } else if (result.status === 200) {
              if (!result.body.error) {
                if(fetchResearchStatusesonSave){
                  this.getPropertyResearchStatusFromSummary();
                  }
              }
            } else {

            }
          });
        }
      });
    }
  }
  getPropertyResearchStatusFromSummary() {
    const response_propertyResearchStatus = this._propertyService.getPropertyResearchStatusFromSummary(this.property.PropertyID);
    response_propertyResearchStatus.subscribe(result => {
      if (!result.body.error) {
        this._sharedDataService.mapSearchPropertyList?.forEach(element => {
          if (element.PropertyID == this.property.PropertyID) {
            element.PropertyResearchTypeID = result.body.responseData[0].ResearchTypeID;
            let commModel = new CommunicationModel();
            commModel.Key = 'PropertyResearchTypeChanged';
            this._communicationService.broadcast(commModel);
          }

        });
      }
    });
  }
  saveNewPropertyParcelInformation() {
    if (this.initialDetails.isNewProperty && this.initialDetails.selectedParcel) {
      const parcel = new PropertyParcel();
      parcel.ParcelNo = this.initialDetails.selectedParcel.ParcelNumber;
      parcel.ParcelSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.initialDetails.selectedParcel.Area);
      parcel.IsResearchTool = true;
      parcel.EntityID = this.EntityID;
      parcel.PropertyID = this.property.PropertyID;
      const response_parcel = this._propertyService.PropertyParcelDetailsSave(parcel);
      response_parcel.subscribe(result => {
        if (result.status === 201) {

        } else if (result.status === 200) {
        }
      });
    }
  }

  getPropertyResearchStatus(propertyId) {
    setTimeout(()=>{
      this.selectedOption = this.property.PropertyResearchTypeID
    }, 300)
  }

  copyPropertyResearchStatus() {

    this.propertyResearchStatusOriginal = new Array<PropertyResearchStatus>();
    if (this.propertyResearchStatus.length > 0) {
      let propResearch: PropertyResearchStatus;
      this.propertyResearchStatus.forEach(value => {

        propResearch = new PropertyResearchStatus();
        propResearch.CreatedBy = value.CreatedBy;
        propResearch.CreatedDate = value.CreatedDate;
        propResearch.IsActive = value.IsActive;
        propResearch.ModifiedBy = value.ModifiedBy;
        propResearch.ModifiedDate = value.ModifiedDate;
        propResearch.ModifiedPersonName = value.ModifiedPersonName;
        propResearch.EntityID = value.EntityID;
        propResearch.PropertyID = value.PropertyID;

        propResearch.PropertyResearchStatusID = value.PropertyResearchStatusID;
        propResearch.PropertyResearchTypeID = value.PropertyResearchTypeID;

        propResearch.PropertyResearchTypeName = value.PropertyResearchTypeName;

        this.propertyResearchStatusOriginal.push(propResearch);
      });
    }
  }

  onCancel() {
    if (this.isAnExistingProperty) {
      if (this.isNavigationFromSearch) {
        this.visitedPropertyIds.push(this.property.PropertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
      }
      this.clearStrataSessionStorageData();
      this.loaderMasterPIDAfterChildsVisit = false;
      this.cancelConfirmation(true, () => { this.propertySave(true); }, () => {
        this._notificationService.ShowErrorMessage("Property save cancelled");
        this.onComplete.emit(false);
        this.sharedDataService.deleteBuildingFootPrintIds = [];
      });
    } else {
      this.cancelConfirmation(true, () => {
        this.propertySave(true);
      }, () => {
        this.clearParkingRetailAndHardStandPolygons();
        this._notificationService.ShowErrorMessage("Property save cancelled");
        this.completeChanges();
        this.onComplete.emit(false);
      });
    }
  }

  private cancelConfirmation(isAutoClose: boolean, onSave: () => void, onDoNotSave: () => void, onCancel?: () => void) {
    if (this.propertyForm.dirty || this.isDeleteFloor) {
      const message: string = CommonStrings.DialogConfigurations.Messages.CancelChangesConfirmationMessage;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.SaveChanges, Callback: () => { onSave(); }};
      const cancelBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.DoNotSave, Callback: () => { onDoNotSave(); }};
      const additionalBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { if (onCancel) { onCancel(); }}, Classname: 'btn btn-danger'};
      this.showDialogConfirmation(message, title, okButton, cancelBtn, additionalBtn);
    }
    else if (isAutoClose) {
      this.clearParkingRetailAndHardStandPolygons();
      if (this.isAnExistingProperty) {
        this.onComplete.emit(false);
        this.sharedDataService.deleteBuildingFootPrintIds = [];
      } else {
        this.completeChanges();
        this.onComplete.emit(false);
      }
    } else {
      onDoNotSave();
    }
  }

  clearParkingRetailAndHardStandPolygons() {
    this.parkingPolygon = { singleSlotPolygon: { polygonId: null, area: 0, polygon: null }, parkingAreaPolygon: [], parkingSpaces: null };
    this.hardstandAreaPolygon = null;
    this.retailFrontagePolyline = null;
    let commModel = new CommunicationModel();
    commModel.Key = 'onClearFrontage';
    commModel.data = true;
    this._communicationService.broadcast(commModel);
  }

  completeChanges() {
    this.isAverageEstimationEnabled = false;
    this.isAverageEstimationEnabledForMaster = false;
    this.showContributedGBA = false;
    this.isPolygonCollapsed = true;
    this.footPrintNotAvailable = false;
    this.hasNoExistingParcelInTileLayer = false;
    this.showParcelWindow = false;
    this.disableStrataBtn = false;
    this.isMultiStrata = false;
    this.initialDetails = new mapEditPropertyDTO();
    this.pRange = { StrataMin: '', StrataMax: '' };
    this.property = new Property();
    this.propertyCopy = new Property();
    this.propertyLocation = new PropertyLocation();
    this.propertyResearchStatusInput = new PropertyResearchStatusSaveInput();
    this.property.UseTypeID = 0;
    this.selectedUseTypeID = 0;
    this.property.SpecificUseID = 0;
    this.property.SizeSourceID = null;
    this.property.BuildingClass = '';
    this.property.PropertyID = 0;
    this.isNewProperty = true;
    this.displayPropertyId = 0;
    this.selectedOption = null;
    this.selectedMasterParcel = null;
    this.LastStrataUnit = null;
    this.aggregateParcelSize = 0;
    this.parcelCount = 0;
    this.newPropertyLocation = undefined;
    this.resetControls();
    let commModel = new CommunicationModel();
    commModel.Key = 'ClearPolygonShape';
    this._communicationService.broadcast(commModel);
    this.isAddPolygonButtonDisabled = false;
    // Clear the previously stored property parcel information upon completion of saving.
    if (this._sharedDataService) {
      this._sharedDataService.parcelInfoPickedFromTileLayer = null;
      this.parcelInformation = null;
      this.selectedParcelInfo = null;
    }
    this.selectedParcels = [];
    this.initData();
  }

  setPropertyRecordDisplayNumber(index) {
    this.currentPropertyRowNumber = (index + 1).toString().padStart(3, '0');
  }

  getNextProperty(list: any[], currentIndex: number) {
    if (currentIndex >= 0 && currentIndex < list.length - 1) {
      const nextIndex = this.getNextPropertyIndex(list, currentIndex);
      if (nextIndex !== -1) {
        sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(nextIndex));
        this.setPropertyRecordDisplayNumber(nextIndex);
        return list[nextIndex];
      } else {
        this.onComplete.emit(false);
      }
    } else if (currentIndex === list.length - 1) {
      this.onComplete.emit(false);
    }
  }


  getNextPropertyIndex(list: any[], currentIndex: number): number {
    let nextIndex = currentIndex + 1;
    let propertyIdsList = this._loginService.UserInfo.RoleID == this.userRoles.Auditor ? [] : this.navigationPreference === NavigationPreferences.UnVisited ? this.visitedPropertyIds : this.editedPropertyIds;
    while (nextIndex < list.length) {
      let propertyId = list[nextIndex].PropertyId;
      if (!propertyIdsList.includes(propertyId)) {
        return nextIndex;
      }
      nextIndex++;
    }
    return -1;
  }

  getNextStrataPropertyIndex(currentPropertyIndex, strataIds) {
    let propertyIdsList = this._loginService.UserInfo.RoleID == this.userRoles.Auditor ? [] : this.isNavigationFromSearch ? this.navigationPreference === NavigationPreferences.UnVisited ? this.visitedPropertyIds : this.editedPropertyIds : this.visitedStrataIdsFromSS;
    let nextIndex = currentPropertyIndex + 1;
    while (nextIndex < strataIds.length) {
      let propertyId = strataIds[nextIndex];
      if (!propertyIdsList.includes(propertyId)) {
        return nextIndex;
      }
      nextIndex++;
    }
    return -1;
  }


  async setActiveGridRowNumber(propertyId) {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyList);
      if (searchData) {
        const propertyList = searchData.value.propertyList;
        if (propertyList) {
          const index = propertyList.findIndex(property => property.PropertyId === propertyId);
          if (index > 0) {
            this.currentPropertyRowNumber = (index + 1).toString().padStart(3, '0');
            sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(index));
          }
        }
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  handleSaveAndNextForStrataProperty(propertyID, strataIds, masterPID) {
    const currentPropertyIndex = strataIds.findIndex(id => id === propertyID);
    const nextPropertyIndex = this.getNextStrataPropertyIndex(currentPropertyIndex, strataIds);
    if (currentPropertyIndex >= 0 && !(nextPropertyIndex > (strataIds.length - 1)) && nextPropertyIndex != -1) {
      // To Load Selected Property Data, resetting the existing form
      this.propertyForm.reset();
      // Resetting  to send selected Property Change Log
      this.DataArray = [];
      this.fetchStrataProperty.emit(strataIds[nextPropertyIndex]);
      this.visitedStrataIdsFromSS.push(propertyID);
      sessionStorage.setItem(SessionStorageKeys.VisitedStrataIds, JSON.stringify(this.visitedStrataIdsFromSS));
      this.completeChanges();
      if (this.isNavigationFromSearch) {
        this.visitedPropertyIds.push(propertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
        this.setActiveGridRowNumber(strataIds[nextPropertyIndex]);
      }
    } else {
      if ((nextPropertyIndex > (strataIds.length - 1) || nextPropertyIndex == -1) && masterPID) {
        this.propertyForm.reset();
        this.DataArray = [];
        this.clearStrataSessionStorageData();
        this.loaderMasterPIDAfterChildsVisit = true;
        this.fetchProperty.emit(masterPID);
      } else {
        this.clearStrataSessionStorageData();
        if (sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch)) {
          const index = parseInt(this.activeGridRowNumber);
          const PropertyListFromSessionStorage = JSON.parse(sessionStorage.getItem(SessionStorageKeys.SearchResults)).CurrentPagePropertyList;
          const prop = PropertyListFromSessionStorage[index];
          this.handleSaveAndNextIfNavigationIsFromSearch(prop.PropertyId);
        } else {
          this.onComplete.emit(true);
        }
      }

    }
  }

  checkForParcelLayer() {
    if (!(this.selectedParcelInfo || (this.selectedParcels && this.selectedParcels.length > 0))) {
      let commModel = new CommunicationModel();
      commModel.Key = 'EnableParcel';
      commModel.data = this.propertyLocation.Condo;
      this._communicationService.broadcast(commModel);
      setTimeout(()=>{      
        this.completeChanges();
        this.onComplete.emit(false);},200)
      this.footPrintNotAvailable = false;
      this._notificationService.ShowWarningMessage(CommonStrings.DialogConfigurations.Messages.PleaseEnableAndSelectParcelLayer);
    }
  }

  handleSaveAndNextIfNavigationIsFromSearch(propertyID) {
    this.visitedPropertyIds.push(propertyID);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    const PropertyListFromSessionStorage = sessionStorage.getItem(SessionStorageKeys.SearchResults)
    if (PropertyListFromSessionStorage) {
      const propertyList = JSON.parse(PropertyListFromSessionStorage).CurrentPagePropertyList || [];
      const propertyIndex = propertyList.findIndex(property => property.PropertyId === propertyID);
      if (propertyIndex !== -1) {
        const nextProperty = this.getNextProperty(propertyList, propertyIndex);
        const nextPropertyId = nextProperty?.PropertyId;
        this.propertyForm.reset();
        this.DataArray = [];
        const propertyLocation = { Latitude: nextProperty.Latitude, Longitude: nextProperty.Longitude };
        this.fetchNextProperty.emit({ nextPropertyId, propertyLocation });
      } else {
        this.onComplete.emit(false);
      }
    } else {
      this.onComplete.emit(false);
    }
  }


  onClickNextHandler() {
    const changed = JSON.stringify(this.propertyForm.value) !== this.initialPropertyForm;
    const isValid = this.propertyForm.dirty || changed;
    if (isValid) {
      const configuration: confirmConfiguration = new confirmConfiguration();
      configuration.Message = CommonStrings.DialogConfigurations.Messages.CancelChangesConfirmationMessage;
      configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
      configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
      configuration.OkButton.Callback = () => {
        this.saveAndNextHandler(true, this.property.PropertyID);
      }
      configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.No;
      configuration.CancelButton.Callback = () => {

      }
      this._notificationService.CustomDialog(configuration);
    } else {
      this.saveAndNextHandler(true, this.property.PropertyID);
    }
  }


  saveAndNextHandler(isAutoClose: boolean = true, propertyID) {
    const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
    if (this.isNavigationFromSearch) {
      this.visitedPropertyIds.push(propertyID);
      this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    }
    if (isAutoClose) {
      if (this.isNavigationFromSearch) {
        if (strataIds && !this.loaderMasterPIDAfterChildsVisit && this.propertyLocation) {
          this.handleSaveAndNextForStrataProperty(propertyID, strataIds, this.propertyLocation.MasterPropertyId);
        } else {
          this.handleSaveAndNextIfNavigationIsFromSearch(propertyID);
        }
      } else {
        if (strataIds && !this.loaderMasterPIDAfterChildsVisit && this.propertyLocation) {
          this.handleSaveAndNextForStrataProperty(propertyID, strataIds, this.propertyLocation.MasterPropertyId);
        } else {
          this.onComplete.emit(true);
        }
      }
    }
    else {
      this.propertyForm.reset();
      this.DataArray = [];
      this.fetchProperty.emit(propertyID);
    }
  }



  onSave(isAutoClose: boolean = true, propertyID) {
    if (this.isAnExistingProperty) {
      const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
      if (strataIds) {
        this.editedStrataIds.push(propertyID);
        sessionStorage.setItem(SessionStorageKeys.EditedStrataIds, JSON.stringify(this.editedStrataIds));
      }
      if (this.isNavigationFromSearch) {
        this.editedPropertyIds.push(propertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.EditedPropertyIds, value: this.editedPropertyIds });
      }
      this.saveAndNextHandler(isAutoClose, propertyID)
    } else {
      if (isAutoClose) {
        this.completeChanges();
        if (this.initialDetails.fromMasterStrata) {
          this.fetchProperty.emit(this.initialDetails.masterStrataObj.property.PropertyID);
        } else {
          this.onComplete.emit(true);
        }
      } else {
        this.completeChanges();
        this.fetchProperty.emit(propertyID)
      }
    }
  }

  clearStrataSessionStorageData() {
    sessionStorage.removeItem(SessionStorageKeys.StrataPropertyIds);
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    sessionStorage.removeItem(SessionStorageKeys.EditedStrataIds);
  }

  addressAsPropertyName(IsChange: boolean) {
    if (this.isAnExistingProperty) {
      this.StreetMinMaxValidation();
    }
    if (this.propertyLocation.UseAddressAsPropertyName) {
      this.propertyLocation.PropertyName = '';
      if (this.propertyLocation.AddressType == this.addressTypeValues.Address) {
        if (this.propertyLocation.StreetNumberMin)
          this.propertyLocation.PropertyName = this.propertyLocation.StreetNumberMin.toString();
        if (this.propertyLocation.StreetNumberMax) {
          if (this.propertyLocation.StreetNumberMin == this.propertyLocation.StreetNumberMax) {
            this.propertyLocation.PropertyName += ' ';
          } else {
            this.propertyLocation.PropertyName += this.propertyLocation.StreetNumberMin ? ' -' : '';
            this.propertyLocation.PropertyName += ` ${this.propertyLocation.StreetNumberMax.toString()} `;
          }
        } else {
          this.propertyLocation.PropertyName += ' ';
        }

        if (this.propertyLocation.StreetPrefix1) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.streetPrefixes.find(x => x.PrefixID == this.propertyLocation.StreetPrefix1).Prefix + ' ';
        }
        if (this.propertyLocation.StreetPrefix2) {
          this.propertyLocation.PropertyName = this.propertyLocation?.PropertyName + this.streetPrefixes?.find(x => x?.PrefixID == this.propertyLocation?.StreetPrefix2)?.Prefix + ' ';
        }
        if (this.propertyLocation.AddressStreetName) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.propertyLocation.AddressStreetName + ' ';
        }
        if (this.propertyLocation.StreetSuffix1) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.streetSufixes.find(x => x.SuffixId == this.propertyLocation.StreetSuffix1).Suffix + ' ';
        }
        if (this.propertyLocation.StreetSuffix2) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.streetSufixes.find(x => x.SuffixId == this.propertyLocation.StreetSuffix2).Suffix + ' ';
        }
      }
      else {
        if (this.propertyLocation.Quadrant) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.quadrants.find(x => x.QuadrantID == this.propertyLocation.Quadrant).QuadrantName + ' ';
        }
        if (this.propertyLocation.EastWestStreet) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.propertyLocation.EastWestStreet + ' ';
        }
        if (this.propertyLocation.NorthSouthStreet) {
          this.propertyLocation.PropertyName = this.propertyLocation.PropertyName + this.propertyLocation.NorthSouthStreet + ' ';
        }
      }
      if (this.propertyLocation.Condo === EnumCondoTypeName.Strata && this.propertyLocation.CondoUnit) {
        this.propertyLocation.PropertyName = `${this.propertyLocation.CondoUnit}/${this.propertyLocation.PropertyName.slice(0, -1)}`;
      } else {
        this.propertyLocation.PropertyName = this.propertyLocation.PropertyName.slice(0, -1);
      }
      // Marking for dirty if the property name is changed on edit property
      if (this.isAnExistingProperty)
        this.onChangeSetAsDirty('PropertyName');
    }
    else if (IsChange) {
      this.propertyLocation.PropertyName = this.previousPropertyName;
    }
  }

  StreetMinMaxValidation() {
    if (this.propertyLocation.StreetNumberMin && this.propertyLocation.StreetNumberMax && parseInt(this.propertyLocation.StreetNumberMin.toString()) > parseInt(this.propertyLocation.StreetNumberMax.toString())) {
      this.streetMinMaxError = true;
    } else {
      this.streetMinMaxError = false;
    }
  }

  ValidateBuildingSize() {
    if (this.property.LargestFloor > this.property.BuildingSF) {
      this.maxFloorError = true;
    } else {
      this.maxFloorError = false;
    }
    if (this.property.LargestFloor > this.property.BuildingSF) {
      this.maxFloorError = true;
    } else {
      this.maxFloorError = false;
    }
  }

  checkValidations() {
    this.clearHeightMinError = false;
    this.dockHighError = false;
    this.floorLoadingError = false;
    this.buildingSFError = false;
    this.noOfOfficeFloorError = false;
    if ((this.property.ClearHeightMin && this.property.ClearHeightMax) && (this.property.ClearHeightMin > this.property.ClearHeightMax)) {
      this.clearHeightMinError = true;
    }

    if ((this.property.DockHigh) && (this.property.DockLevelersCapacity > this.property.DockHigh)) {
      this.dockHighError = true;
    }

    if (this.property.FloorLoading > 999999) {
      this.floorLoadingError = true;
    }

    if ((this.property.BuildingSF && this.property.OfficeSF) && (Number(this.property.BuildingSF) < Number(this.property.OfficeSF))) {
      this.buildingSFError = true;
    }

    if ((this.property.NoOfOfficeFloor && this.property.Floors) && (this.property.Floors < this.property.NoOfOfficeFloor)) {
      this.noOfOfficeFloorError = true;
    }
  }
  closeMediaUploadModal() {
    this.files = new Array<Media>();
    this.showFileUpload = false
  }
  onUploadEvent(path: string) {
    this.closeMediaUploadModal();
  }

  broadcastPropertySaveEvent() {
    let communicationModel: CommunicationModel = new CommunicationModel();
    communicationModel.Key = 'propertySaveEvent';
    communicationModel.data = this.initialDetails;
    this._communicationService.broadcast(communicationModel);
  }

  fetchParcels() {
    const response_parcel = this._propertyService.GetPropertyParcelDetails(this.property.PropertyID);
    response_parcel.subscribe(result => {
      let data = result.body;
      if (data) {
        this.propertyParcelList = new Array<PropertyParcel>();
        this.propertyParcelList = data.responseData;
        this._sharedDataService.selectedPropertyParcel = this.propertyParcelList;
        this._sharedDataService.selectedPropertyParcel.forEach(element => {
          element.PropertyID = this.property.PropertyID;
        });
        this.parcelCount = this.propertyParcelList?.length;
        if (this.propertyParcelList.length > 0) {
          this.property.ParcelNumber = this.propertyParcelList[0].ParcelNo;
          const size = this.propertyParcelList.reduce((acc, parcel) => acc + (parseFloat(parcel.ParcelSizeSM as any) || 0), 0)
          this.aggregateParcelSize = Number(size?.toFixed(2));;
          if (this.shouldUpdateLotSize) {
            this.propertyCopy.LotSizeSF = this.property.LotSizeSF;
            this.property.LotSizeSM = this.property.LotSizeSF = size;
            this.propertyForm.get('LotSizeSF').setValue(size);
            this.propertyForm.get('LotSizeSF').markAsDirty();
            this.propertySave(false);
          }
        }
      }
    });
  }

  closeNoteModal() {
    this.selectedNote = null;
    this.showNoteModal = false;
    let model = new CommunicationModel();
    model.Key = 'onNoteClose';
    this._communicationService.broadcast(model);
  }
  closeMapModal() {
    this.showMap = false;
  }

  showMapModal(isdefaultProp: boolean, lat = null, long = null, newMap = false) {
    this.propertyDetails = new Property();
    this.propertyDetails.PropertyID = this.property.PropertyID;
    if (isdefaultProp) {
      this.propertyDetails.Latitude = this.propertyLocation.Latitude;
      this.propertyDetails.Longitude = this.propertyLocation.Longitude;
    }
    else {
      this.propertyDetails.Latitude = this.property.Latitude;
      this.propertyDetails.Longitude = this.property.Longitude;
      this.propertyDetails.Floors = this.property.Floors;
    }
    if (!!lat && !!long) {
      this.propertyDetails.Latitude = lat;
      this.propertyDetails.Longitude = long;
      this.propertyDetails.zoomLevel = 14;
    }
    if (!newMap) {
      this.propertyDetails.CondoTypeID = this.property.CondoTypeID;
      this.propertyDetails.CondoTypeName = this.property.CondoTypeName;
      this.propertyDetails.UseTypeID = this.property.UseTypeID;
      let commModel = new CommunicationModel();
      commModel.Key = 'NewPropertySelected';
      commModel.data = this.propertyDetails;
      this._communicationService.broadcast(commModel);
      this._addFloorService.enableFloorLables(true);
    }
    this.showMap = true;
  }

  onSaveBuildingSize(areaSM) {
    areaSM = areaSM?.toFixed(2);
    this.propertyForm.get('TypicalFloorSizeSM') && this.propertyForm.get('TypicalFloorSizeSM').markAsDirty();
    this.propertyForm.get('BuildingSF') && this.propertyForm.get('BuildingSF').markAsDirty();
    this.updateAddPolygonButtonDisable();
  }

  getValue(controlName, value) {
    const yesNoFields = [...YesNoFields, ...IndustrialYesNoFields];
    if (yesNoFields.includes(controlName)) {
      return value === 1 ? 'Yes' : value === 0 ? 'No' : value;
    } else {
      return value;
    }
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      const list = this.propertyLookups[key] || []
      if (ContributedSourceFields.includes(field)) {
        return list.filter(source => source.SizeSourceID != EnumSizeSource.AerialMeasurement);
      }
      return list;
    }
  }

  mapChangeLog() {
    this.DataArray = mapChangeLogUtil({
      form: this.propertyForm,
      isAnExistingProperty: this.isAnExistingProperty,
      property: this.property,
      propertyCopy: this.propertyCopy,
      propertyLocation: this.propertyLocation,
      propertyLocationCopy: this.propertyLocationCopy,
      oldPolygon: this.oldPolygon,
      multifloors: this.multifloors,
      _loginService: this._loginService,
      _datePipe: this._datePipe,
      dateFormat: this.dateFormat,
      propertyResearchStatus: this.propertyResearchStatus,
      selectedOption: this.selectedOption,
      researchChanged: this.researchChanged,
      getValue: this.getValue.bind(this),
      getDropdownFromLookup: this.getDropdownFromLookup.bind(this),
      isAverageEstimationEnabled: this.isAverageEstimationEnabled
    });
  }

  onChangeSetAsDirty(control: string) {
    const data = this.propertyForm?.controls;
  
    // Traverse through the main form and handle nested forms
    Object.keys(data).forEach((key) => {
      const formControl = data[key];
      
      if (formControl instanceof FormGroup) {
        // If the control is a FormGroup, check its child controls
        const nestedControl = formControl?.get(control);
        if (nestedControl) {
          nestedControl?.markAsDirty();
        }
      } else if (key === control) {
        // Mark control directly if it's at the top level
        formControl.markAsDirty();
      }
    });
  }


  getSelectedValue(Type, event, ValueName) {
    let Value = null;
    if (Type == 'UseTypeID') {
      this.propertyTypes.forEach(element => {
        if (element.UseTypeID === event) {
          Value = element.UseTypeName;
        }
      })
    } else if (!!event) {
      Value = event[ValueName];
    } else {
      Value = null;
    }
    if (Type === 'ConstructionStatusID') {
      this.floorValidation(Value);
    }
    if ((Type === 'UseTypeID' && !Value)) {
      Value = '';
    }
    const date = new Date().toISOString();
    const id = this.propertyCopy[Type] || this.propertyLocationCopy[Type];
    let previousData;
    switch (Type) {
      case 'Condo':
        this.condos.forEach(element => {
          if (element.CondoTypeID === id) {
            previousData = element.CondoTypeName;
          }
        });
        break;
      case 'UseTypeID':
        this.propertyTypes.forEach(element => {
          if (element.UseTypeID === id) {
            previousData = element.UseTypeName;
          }
        });
        break;
      case 'ConstructionStatusID':
        this.constructStatuses.forEach(element => {
          if (element.ConstructionStatusID === id) {
            previousData = element.ConstructionStatusName;
          }
        });
        break;
      case 'ConstructionTypeID':
        this.constructTypes.forEach(element => {
          if (element.ConstructionTypeID === id) {
            previousData = element.ConstructionTypeName;
          }
        });
        break;
      case 'LotSizeSourceID':
        this.sizeSource.forEach(element => {
          if (element.SizeSourceID === id) {
            previousData = element.SizeSourceName;
          }
        });
        break;
      default:
        break;
    }
    const i = this.DataArray.findIndex(x => x.Field === Type);
    if (i !== -1) {
      this.DataArray.splice(i, 1);
    }
    this.DataArray.push({
      'Field': Type,
      'CurrentValue': Value,
      'PreviousValue': previousData,
      'LoginEntityID': this._loginService.UserInfo.EntityID,
      'DateTime': date
    });

  }

  floorValidation(value) {
    if (value === 'Existing') {
      this.propertyForm.get('Floors').setValidators([Validators.required]);
    } else {
      this.propertyForm.get('Floors').clearValidators();
    }
    this.propertyForm.get('Floors').updateValueAndValidity();
  }
  changeTab($event) {
    if (!!$event.tab.textLabel) {
      if ($event.tab.textLabel === 'Listing') {
        this.isListing = true;
      }
      if ($event.tab.textLabel === 'Media') {
        this.isMedia = true;
      }
      if ($event.tab.textLabel === 'Notes') {
        this.isNotes = true;
      }
      this.isStrata = $event.tab.textLabel === 'Strata';
    }
  }

  showPropertyResearchStatus() {
    this.researchStatusClicked = true;
    this.getPropertyResearchStatus(this.property.PropertyID);
  }
  closeRoofTop() {
    this.showRoofTop = false;
  }
  onMediaChange(path) {
    this.onComplete.emit(true);
  }
  showRoofTopModal() {
    this.rooftopUtility = new RooftopUtility();
    this.rooftopUtility.PropertyID = this.property.PropertyID;
    this.rooftopUtility.Latitude = this.property.Latitude;
    this.rooftopUtility.Longitude = this.property.Longitude;
    this.showRoofTop = true;
  }

  setAllocationChangelog() {
    const date = new Date().toISOString();
    if (this.propertyAllocationList) {
      this.propertyAllocationList.forEach(element => {
        const dataArray: Array<any> = [];
        if (!element.ChangeLogJSON && element.IsActive === 1 && element.PropertyAllocationID > 0) {
          const allocationCopy = this.propertyAllocationListCopy.filter(x => x.PropertyAllocationID === element.PropertyAllocationID)[0];
          Object.keys(element || {}).forEach(key => {
            const a = element[key];
            const b = allocationCopy[key];
            if (allocationCopy[key] !== element[key] && !Object.is(element[key], allocationCopy[key])) {
              key = (key === 'UseTypeID' || key === 'SpecificUsesID') ? null : key;
              key = key === 'UseTypeName' ? 'UseTypeID' : (key === 'SpecificUsesName' ? 'SpecificUsesID' : key);
              if (dataArray.filter(x => x.Field === key).length === 0 && !!key) {
                dataArray.push({
                  'Field': key,
                  'CurrentValue': element[key],
                  'PreviousValue': allocationCopy[key],
                  'LoginEntityID': this._loginService.UserInfo.EntityID,
                  'DateTime': date
                });
              }
            }
          });
          element.ChangeLogJSON = dataArray.length > 0 ? JSON.stringify(dataArray) : null;
        }
      }
      );
    }
  }

  async savePropertyAlloction(multifloors, propertyId) {
    let propertyAllocations = JSON.parse(JSON.stringify(this.propertyAllocationList));
    //while adding new property creating allocations directly from multifloors
    if(!propertyAllocations?.length && multifloors?.length) {
      propertyAllocations = [];
      [ ...multifloors ].forEach(floor => {
        const propertyUse = this.propertyTypes.find((useType) => useType.UseTypeID === floor.UseTypeId).UseTypeName;
          const allocation = {
            ...new PropertyAllocation(),
            PropertyAllocationID: -1,
            PropertyID: propertyId,
            UseTypeID: floor.UseTypeId,
            SpecificUsesID: null,
            UseTypeName: propertyUse,
            Floors: floor.Floors,
            FloorSizeSM: this._propertyService.convertUnit(this.CountryId, 'SF', 'SqM', Number(floor.SizeInSF)),
            Notes: floor.Description,
            IsDefault: 1,
            IsActive: 1,
            LoginEntityID: this._loginService.UserInfo.EntityID,
            ChangeLogJSON: null,
            ApplicationID: EnumApplication.VST,
            MinFloorNumber: Number(floor.PropertyMinFloor),
            MaxFloorNumber: Number(floor.PropertyMaxFloor),
            SectionID: floor.BuildingFootPrintID,
          };
          propertyAllocations.push(allocation);
      });
    }
    //Save allocations
    if (propertyAllocations) {
      this.setAllocationChangelog();
      propertyAllocations.forEach((element, index) => {
        element.LoginEntityID = this._loginService.UserInfo.EntityID;
        element.PropertyID = propertyId;
        element.ApplicationID = EnumApplication.VST;
        element.IsActive = !element.IsActive ? 0 : element.IsActive;
        element.IsDefault = !element.IsDefault ? 0 : element.IsDefault;
        const isSectionIdPresent = multifloors.some(floor => floor.BuildingFootPrintID == element.SectionID);
        //Adding sectionId if not present
        if (!!element.IsDefault && !isSectionIdPresent) {
          //Finding the footprint data matching with the allocation data
          const matchingFloor = multifloors.find(floor => {
            const floorSizeSM = this._propertyService.convertUnit(this.CountryId, 'SF', 'SqM', Number(floor.SizeInSF));
            const floorSize = parseInt(floor.Floors) * Number(floorSizeSM);
            return parseInt(floor.PropertyMinFloor) == element.MinFloorNumber && (floor.PropertyMinFloor == floor.PropertyMaxFloor ? true : parseInt(floor.PropertyMaxFloor) == element.MaxFloorNumber) && floor.UseTypeId == element.UseTypeID && Math.round(floorSize) == Math.round(Number(element.FloorSizeSM) * Number(element.Floors))
          });
          //Setting the allocation SectionID with Building footprint ID of the matching floor.
          if (matchingFloor) {
            element.SectionID = matchingFloor.BuildingFootPrintID;
          }
        }
        element.FloorSizeSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', Number(element.FloorSizeSM));

        //Saving Allocations
        const response_propertyAlloction = this._propertyService.savePropertyAlloction(element);
        response_propertyAlloction.subscribe(result => {
          if (result.status === 201) {
          } else if (result.status === 200) {
            if (result.body.error) {
              this._notificationService.ShowErrorMessage('Property Allocation Save failed');
            } else if ((index + 1) === propertyAllocations.length) {
              // Get allocation is called only after all save operations are completed
              if (this.isAnExistingProperty) {
                this.propertyAllocation.getPropertyAllocationsAndUses(this.initialDetails.propertyId);
              }
            }
          }
        });
      });
    }
  }

  showAllocationsAndUses() {
    this.allocationsAndUses = true;
    this.getPropertyResearchStatus(this.property.PropertyID);
  }

  getNewFloorSize() {
    if (this.metricUnit === this.UnitId) {
      this.buildingSizeGBACopy = this.property.BuildingSizeSM;
      this.buildingSizeNLACopy = this.property.BuildingNRASM;
      this.BldgSizeSourceIDCopy = this.property.BldgSizeSourceID;
    } else {
      this.buildingSizeGBACopy = this.property.BuildingSF;
      this.buildingSizeNLACopy = this.property.BuildingNRA;
      this.BldgSizeSourceIDCopy = this.property.BldgSizeSourceID;
    }
  }

  viewChangeLog(type, parentid) {
    this.showChangeLog = true;
    this.changelogType = type;
    this.parentId = parentid;
  }

  clearAdditionalUses() {
    let commModel = new CommunicationModel();
    commModel.Key = 'clearAdditionalUses';
    commModel.data = true;
    this._communicationService.broadcast(commModel);
  }

  showPropertySummary(propertyID) {
    this.clearAdditionalUses();
    this.clearParkingRetailAndHardStandPolygons();
    this.fetchStrataProperty.emit(propertyID);
    const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
    this.visitedStrataIdsFromSS.push(propertyID);
    sessionStorage.setItem(SessionStorageKeys.VisitedStrataIds, JSON.stringify(this.visitedStrataIdsFromSS));
    if (strataIds && this.isNavigationFromSearch) {
      this.visitedPropertyIds.push(propertyID);
      this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    }
  }

  onPropertySpacesSave({ parkingSpaces, singleSlot, parkingArea }) {
    if (parkingSpaces) {
      this.property.ParkingSpaces = parkingSpaces;
      this.propertyForm.get('ParkingSpaces').markAsDirty();
      this.parkingPolygon = { singleSlotPolygon: singleSlot, parkingAreaPolygon: parkingArea, parkingSpaces: parkingSpaces };
    }
    this.showParkingSpace = false;
  }

  fetchAndProcessStagingMedia(propertyId: any) {
    this.stagingMedia.forEach(async (mediaObj) => {
      const { attachments, fileObject, media } = mediaObj.mediaInformation
      media.PropertyID = propertyId;
      media.RelationID = propertyId;
      await this.IndexedDBService.saveMedia({
        mediaIdentifier: propertyId + '-' + fileObject.fileName,
        mediaInformation: {
          fileObject: fileObject,
          media: media,
          fileName: fileObject.fileName,
          attachments: attachments
        }
      });
    });
    this.stagingMedia.forEach((media) => {
      this.StagingIndexedDBService.removeMedia(media.mediaIdentifier, IndexedDBCollections.stagingImages);
    });
  }

  onCopyPolygonSave(polygon: MultiPolygon) {
    const id = uuidv4();
    polygon.localBuildingFootPrintID = id;
    let commModel = new CommunicationModel();
    commModel.Key = 'copyPolygon';
    commModel.data = polygon;
    this._communicationService.broadcast(commModel);
    this.showCopyPolygonModal = false;
    this.isFootprintModified = true;
    this.multifloors = JSON.parse(JSON.stringify([...this.multifloors, polygon]));
    this.updateBuildingSqmOnFloorChange();
    this.getAdditionalUses();
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
  }

  addNewStrataUnit(data) {
    this.addNewStrataUnitToMaster.emit({
      property: {
        ...this.property,
        PropertyResearchTypeID: this.selectedOption
      }, propertyLocation: this.propertyLocation, ...data
    });
  }

  addNewFreeholdUnit(data) {
    let parcelObj: any = {};
    if (data.isFreehold && !data.isMultiStrata) {
      parcelObj.ParcelNumber = this.property.ParcelNumber
    }
    this.addNewFreeholdUnitToMaster.emit({
      property: {
        ...this.property,
        PropertyResearchTypeID: this.selectedOption,
        ...parcelObj
      }, propertyLocation: this.propertyLocation, ...data
    });
  }

  onNext(isAutoClose = false, auditStatusDefinationID: number = undefined, onSaveAndNext = false) {
    const auditStatus = this.auditStatusList.find(status => status.StatusName == "Verified");
    const reqBody = { PropertyID: this.property.PropertyID, PropertyAuditStatusID: auditStatusDefinationID ? auditStatusDefinationID : auditStatus.StatusDefinationID }
    const response = this._propertyService.saveAuditStatus(reqBody);
    response.subscribe(result => {
      if (!result.body.error) {
        sessionStorage.setItem(SessionStorageKeys.FetchProperties, 'true');
        this._notificationService.ShowSuccessMessage(onSaveAndNext ? CommonStrings.SuccessMessages.PropertyDetailsAndAuditStatusSave : CommonStrings.SuccessMessages.SaveAuditStatusSuccessMessage);
      } else {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.SaveAuditStatusFailMessage);
      }
    });
    this.onSave(isAutoClose, this.property.PropertyID);

  }

  onParkingMapClicked() {
    this.latLong = {lat: this.property.Latitude ? this.property.Latitude : this.initialDetails.latLng.Latitude, lng: this.property.Longitude ? this.property.Longitude : this.initialDetails.latLng.Longitude}
    this.showParkingSpace = true;
  }

  onCopyPolygonClicked(polygon) {
    this.polygonToBeCopied = polygon;
    this.showCopyPolygonModal = true;
  }

  showStrataOrFreeHoldTab(Condo: number) {
    return (Condo === this.EnumCondoTypeNames.Strata || Condo === this.EnumCondoTypeNames.Master ||
      Condo === this.EnumCondoTypeNames.Child_Freehold || Condo === this.EnumCondoTypeNames.Master_Freehold);
  }

  getStrataOrFreeholdTabText(Condo: number) {
    if (Condo === this.EnumCondoTypeNames.Strata || Condo === this.EnumCondoTypeNames.Master) {
      return 'Strata';
    }
    if (Condo === this.EnumCondoTypeNames.Child_Freehold || Condo === this.EnumCondoTypeNames.Master_Freehold) {
      return 'Freehold';
    }
  }
  isFreeholdProp(Condo: number) {
    return Condo === this.EnumCondoTypeNames.Child_Freehold || Condo === this.EnumCondoTypeNames.Master_Freehold;
  }

  addBldgs() {
    this.addMultiFreeholdModal = true;
    let freeholdMin = undefined;
    const findLastValue = (arr: any[]): number => {
      if (!arr || arr.length === 0) {
        return 0;
      }

      let max = Number.MIN_VALUE;
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] && arr[i].StrataUnit) {
          const freeholdUnit = Number(arr[i].StrataUnit);
          if (!isNaN(freeholdUnit) && freeholdUnit > max) {
            max = freeholdUnit;
          }
        }
      }
      return max;
    };

    const lastMax = findLastValue(this.freehold.propertyFreeholdList);
    freeholdMin = `${lastMax + 1}`;
    this.childFreeholdMin = freeholdMin;
  }

  formatPropertyLocation(propertyLocation) {
    let propertyLocationCopyForChild = JSON.parse(JSON.stringify(propertyLocation));
    setPropertyName(propertyLocationCopyForChild, this.addressTypeValues, this.streetPrefixes, this.streetSufixes, this.quadrants);
    return propertyLocationCopyForChild;
  }

  saveMultiStrata(min, max, isFromFreeholdOrStrataTab = false) {
    const children = isFromFreeholdOrStrataTab ? this.initialDetails.masterStrataObj.strataList.filter(res => res.IsMaster != 1 && res.StrataType == "Strata") : (this.freehold && this.freehold.propertyFreeholdList && this.freehold.propertyFreeholdList.length > 0 ? this.freehold.propertyFreeholdList.filter(res => res && res.IsMaster != 1 && res.StrataType == EnumCondoTypeNameFromTiles.Strata) : []);
    this.propertyCopy.EntityID = this._loginService.UserInfo.EntityID;

    let parcel;
    const initialLocation = this.initialDetails
    if (initialLocation && initialLocation.selectedParcel) {
      parcel = new PropertyParcel();
      parcel.ParcelNo = initialLocation.selectedParcel.ParcelNumber;
      parcel.ParcelSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', initialLocation.selectedParcel.Area);
      parcel.IsResearchTool = true;
      parcel.EntityID = this._loginService.UserInfo.EntityID;
    }

    // convert AvgFloorSizeSF
    const additionalInfo = {
      additionalList: [],
      loginEntity: this._loginService.UserInfo.EntityID,
      ApplicationID: EnumApplication.VST,
      countryId: this.CountryId
    }

    const propertyResearch = {
      data: this.propertyResearchStatus.filter(x => x.PropertyResearchTypeID === this.selectedOption)
    }

    const propertyAllocation = {
      defaultSpaceAllocation: 0,
      allocationsToSave: []
    }
    const formattedProperty = this.formatProperty(isFromFreeholdOrStrataTab);
    const location = this.formatPropertyLocation({ ...this.propertyLocation });
    if (!isFromFreeholdOrStrataTab) {
      if (!location.MasterPropertyId && this.propertyLocation.PropertyID) {
        location.MasterPropertyId = this.property.PropertyID;
      }
      location.PropertyID = 0;
      location.Condo = EnumCondoTypeName.Child_Freehold;
      location.CondoUnit = min;
    }

    const data = {
      propertyCopy: { ...formattedProperty, UnitID: this._loginService.UserInfo.UnitID },
      pLocation: location,
      range: { pRangeMin: min, pRangeMax: max },
      children,
      parcel,
      additionalInfo,
      propertyResearch,
      propertyAllocation
    }

    const response_detailSave = this._propertyService.saveMultiStrata(data)
    response_detailSave.subscribe(result => {
      if (result.status === 201) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
        this.redirectionLoader = false;
      } else if (result.status === 200) {
        if (!result.body.error) {
          if (isFromFreeholdOrStrataTab) {
            this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull);
          } else {
            this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.ChildPropertyCreationMessage);
          }
          if (isFromFreeholdOrStrataTab) {
            Object.keys(this.propertyForm.controls).forEach(control => {
              if (this.propertyForm.controls[control].dirty) {
                this.propertyForm.controls[control].reset();
              }
            });
          }
          this.pRange = { StrataMin: '', StrataMax: '' };
          const response = result.body;
          if (!response.error) {
            response.responseData.forEach((row, index) => {
              this.savePropertyResearchStatus(row.Ret_PropId, false);
              if (data.pLocation.Condo === EnumCondoTypeName.Child_Freehold) {
                const parcel = new PropertyParcel();
                parcel.ParcelNo = this.property.ParcelNumber;
                parcel.Lot = this.property && this.property.ParcelNumber && this.property.ParcelNumber.split('//')[0];
                parcel.ApplicationID = EnumApplication.VST;
                parcel.EntityID = this.EntityID;
                parcel.PropertyID = row.Ret_PropId;
                parcel.ParcelSF = formattedProperty.LotSizeSF ? formattedProperty.LotSizeSF : null;
                const response_parcel = this._propertyService.PropertyParcelDetailsSave(parcel);
                response_parcel.subscribe(result => {
                  if (result.status === 201) {
                  } else if (result.status === 200) {
                  }
                });
              }
            })
          }
          if (isFromFreeholdOrStrataTab) {
            const propID = response.responseData[response.responseData.length - 1].Ret_PropId;
            this.completeChanges();
            this.fetchProperty.emit(propID);
            this.clearMultiStrataObj.emit(true)
          } else {
            let commModel = new CommunicationModel();
            commModel.Key = 'fetchFreeholdList';
            this._communicationService.broadcast(commModel);
            this.addMultiFreeholdModal = false;
          }
        } else {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
          if (!isFromFreeholdOrStrataTab) {
            this.addMultiFreeholdModal = false;
          }
        }
        this.redirectionLoader = false;
      }
    });
  }

  onSaveMultiFreeholdUnits(data: { min: number, max: number }) {
    this.saveMultiStrata(data.min, data.max);
  }

  onCancelMultiFreehold() {
    this.addMultiFreeholdModal = false;
  }

  formatProperty(isFromFreeholdOrStrataTab) {
    let newProperty = isFromFreeholdOrStrataTab ? JSON.parse(JSON.stringify(this.propertyCopy)) : JSON.parse(JSON.stringify(this.property));
    if (!isFromFreeholdOrStrataTab) {
      newProperty.LotSizeSF = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', newProperty.LotSizeSF);
      newProperty.BuildingSF = undefined;
      newProperty.PropertyID = 0;
      newProperty.Floors = 1;
    }
    newProperty.LastRenovationDate = null;
    newProperty.EstCompletionDate = null;
    newProperty.ConstructionStartDate = null;
    newProperty.TypicalFloorSizeSM = null;
    newProperty.LotSizeAc = null;
    newProperty.Floors = Number(newProperty.Floors);
    newProperty.EntityID = this.EntityID;
    newProperty.PropertyResearchTypeID = this.selectedOption;
    newProperty.PropertyResearchTypeName = this.propertyResearchStatus.find(status => status.PropertyResearchTypeID == this.selectedOption).PropertyResearchTypeName
    delete newProperty.ChangeLogJSON;
    if (!isFromFreeholdOrStrataTab) {
      newProperty.ConstructionStartDate = newProperty?.ConstructionStartDate ? newProperty?.ConstructionStartDate?.singleDate?.formatted : null;
      newProperty.EstCompletionDate = newProperty?.EstCompletionDate ? newProperty?.EstCompletionDate?.singleDate?.formatted : null;
      newProperty.ActualCompletion = newProperty?.ActualCompletion ? newProperty?.ActualCompletion?.singleDate?.formatted : null;
      newProperty.TitleReferenceDate = newProperty?.TitleReferenceDate ? newProperty?.TitleReferenceDate?.singleDate?.formatted : null;
      newProperty.BookValueDate = newProperty?.BookValueDate ? newProperty?.BookValueDate?.singleDate?.formatted : null;
    }
    return newProperty;
  }

  selectBuildings() {
    let commModel = new CommunicationModel();
    commModel.Key = 'mapBuildingsToMaster';
    commModel.data = this.property.ParcelInfo;
    this._communicationService.broadcast(commModel);
  }

  closeParcelWindow(event: any) {
    this.completeChanges();
    this.onComplete.emit(false);
    this.showParcelWindow = false;
    this.selectedParcels = [];
  }

  addPropertyFromParcels(parcels) {
    this.selectedParcels = [...parcels];
    this.showParcelWindow = false;
    this.setMultiCondoTypeAndUnit();
    this.setMultiParcelAndLotSize();
  }

  setMultiParcelAndLotSize() {
    if (this.selectedParcels && this.selectedParcels.length > 0) {
      const size = this.selectedParcels?.reduce((acc, parcel) => acc + (parcel?.Lot_Area || parcel?.ParcelSF || 0), 0);
      this.aggregateParcelSize = size?.toFixed(2);
      this.parcelCount = this.selectedParcels?.length;
      this.property.LotSizeSM = this.property.LotSizeSF = (size).toFixed(2);
      this.property.ParcelNumber = this.selectedParcels[0].Parcel_No;
      if (!this.property?.LotSizeSourceID) {
        this.property.LotSizeSourceID = EnumSizeSource.CountyDataSource;
      }
    }
  }

  setMultiCondoTypeAndUnit() {
    if (this.selectedParcels && this.selectedParcels.length > 0) {
      this.propertyLocation.Condo = this.getCondoID(this.selectedParcels[0].Strata_Typ);
      this.onStrataChange();
      // Assign the condoUnit value only when the condo type is 'Strata'.
      if (this.selectedParcels[0].Strata_Typ === EnumCondoTypeNameFromTiles.Strata) {
        this.propertyLocation.CondoUnit = parseInt(this.selectedParcels[0].Lot);
      }
    }
  }

  getCondoID(condoType) {
    switch (condoType) {
      case EnumCondoTypeNameFromTiles.NotStrata:
        return EnumCondoTypeName.NotStrata;
      case EnumCondoTypeNameFromTiles.Strata:
        return EnumCondoTypeName.Strata;
      case EnumCondoTypeNameFromTiles.Master:
        return EnumCondoTypeName.Master
    }
  }

  addNewProperty(parcel) {
    this.showParcelWindow = false;
    this.selectedParcelInfo = parcel;
    this.setCondoTypeAndUnit();
    this.setParcelAndLotSize()
  }

  setCondoTypeAndUnit() {
    if (this.selectedParcelInfo) {
      this.propertyLocation.Condo = this.getCondoID(this.selectedParcelInfo.Strata_Typ);
      this.onStrataChange();
      // Assign the condoUnit value only when the condo type is 'Strata'.
      if (this.selectedParcelInfo.Strata_Typ === EnumCondoTypeNameFromTiles.Strata) {
        this.propertyLocation.CondoUnit = parseInt(this.selectedParcelInfo.Lot);
      }
    }
  }

  setParcelAndLotSize() {
    this.property.LotSizeSM = this.selectedParcelInfo.Lot_Area.toFixed(2);
    this.property.LotSizeSF = this.selectedParcelInfo.Lot_Area.toFixed(2);
    this.property.ParcelNumber = this.selectedParcelInfo.Parcel_No;
    // when a sigle parcel is selected set parcel count to 1
    this.aggregateParcelSize = this.property?.LotSizeSM;
    this.parcelCount = 1;
    // Set the default value of LotSizeSource to 'County Source Data'.
    if (!this.property.LotSizeSourceID) {
      this.property.LotSizeSourceID = EnumSizeSource.CountyDataSource;
    }
  }

  //Save the parcel information associated with the property
  savePropertyParcelInformation(propertyId) {
    if (this.selectedParcels && this.selectedParcels.length > 0 && !this.isAnExistingProperty) {
      this.selectedParcels.forEach(parcel => {
        const propertyParcel = new PropertyParcel();
        const parcelSize = parcel.Lot_Area ? parcel.Lot_Area : parcel.ParcelSF;
        propertyParcel.ParcelNo = parcel.Parcel_No ? parcel.Parcel_No : parcel.ParcelNo;
        propertyParcel.Lot = parcel.Lot;
        propertyParcel.ApplicationID = EnumApplication.VST;
        propertyParcel.EntityID = this.EntityID;
        propertyParcel.PropertyID = propertyId;
        propertyParcel.Block = parcel.Block || null;
        propertyParcel.SubDivision = parcel.SubDivision || null;
        propertyParcel.ParcelSF = parcelSize ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', parcelSize) : null;
        propertyParcel.ChangeLogJSON = null;
        const response_parcel = this._propertyService.PropertyParcelDetailsSave(propertyParcel);
        response_parcel.subscribe(result => {
          if (result.body && result.body.error) {
            this._notificationService.ShowErrorMessage(`Failed to add the parcel number ${parcel.Parcel_No} to property ${propertyId}`);
          }
        });
      })

    } else if (this.selectedParcelInfo || this.property.ParcelNumber) {
      const parcel = new PropertyParcel();
      const parcelSize = this.selectedParcelInfo && this.selectedParcelInfo.Lot_Area
      parcel.ParcelNo = this.selectedParcelInfo ? (this.propertyLocation.Condo === EnumCondoTypeName.Child_Freehold ? this.selectedParcelInfo.Parcel_No : this.selectedParcelInfo.Parcel_No) : this.property.ParcelNumber;
      parcel.Lot = this.selectedParcelInfo ? this.selectedParcelInfo.Lot : null;
      parcel.ApplicationID = EnumApplication.VST;
      parcel.EntityID = this.EntityID;
      parcel.PropertyID = propertyId;
      parcel.ParcelSF = parcelSize ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', parcelSize) : this.property.LotSizeSF ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.property.LotSizeSF) : null;
      const response_parcel = this._propertyService.PropertyParcelDetailsSave(parcel);
      response_parcel.subscribe(result => {
        if (result.status === 201) {
        } else if (result.status === 200) {
        }
      });
    }
  }


  addPropertyImages = () => {
    let hasStreet = false;
    let hasAerial = false;
    this.stagingMedia.forEach(media => {
      if (media.mediaInformation.media.MediaName.includes('Streetview') || media.mediaInformation.media.MediaName.includes('Building') || media.mediaInformation.media.MediaTypeID === MediaType.Building_Image) {
        hasStreet = true;
      }
      if (media.mediaInformation.media.MediaName.includes('Aerial') || media.mediaInformation.media.MediaTypeID === MediaType.Aerial_Imagery) {
        hasAerial = true;
      }
    });
    const hasStreetViewImage = this.hasStreetView ? hasStreet : true;
    if ((!hasAerial && !hasStreetViewImage) || (hasStreetViewImage && !hasAerial)) {
      const bothImagesMissingMsg = CommonStrings.DialogConfigurations.Messages.AerialAndStreetViewImageMissing;
      const aerialImageMissingMsg = CommonStrings.DialogConfigurations.Messages.AerialViewImageMissing;
      const message = this.footPrintNotAvailable ? aerialImageMissingMsg : this.hasStreetView ? bothImagesMissingMsg : aerialImageMissingMsg;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => { this.openAerialView.emit(); } }
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    } else if (!hasStreet && hasAerial) {
      const message = CommonStrings.DialogConfigurations.Messages.AerialAndStreetViewImageMissing;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => { this.openStreetViewClicked.emit(); } }
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    }
  }

  hasbothStreetAndAerialImages = async () => {
    const stagingMedia = await this.StagingIndexedDBService.fetchAllStagingMedia()
    this.stagingMedia = stagingMedia;
    let hasStreet = false;
    let hasAerial = false;
    stagingMedia.forEach(media => {
      if (media.mediaInformation.media.MediaName.includes('Streetview') || media.mediaInformation.media.MediaName.includes('Building') || media.mediaInformation.media.MediaTypeID === MediaType.Building_Image) {
        hasStreet = true;
      }
      if (media.mediaInformation.media.MediaName.includes('Aerial') || media.mediaInformation.media.MediaTypeID === MediaType.Aerial_Imagery) {
        hasAerial = true;
      }
    });
    const hasStreetViewImage = this.hasStreetView ? hasStreet : true;
    // Images are not required when record type is master freehold and master without average estimation
    if (this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || (this.propertyLocation.Condo === EnumCondoTypeName.Master && !this.isAverageEstimationEnabled)) {
      return true;
    } else if (this.propertyLocation.Condo === EnumCondoTypeName.Master && this.isAverageEstimationEnabledForMaster) {
      return hasAerial && hasStreetViewImage;
    }  else if (this.footPrintNotAvailable) {
      return hasAerial;
    } else {
      return hasAerial && hasStreetViewImage;
    }

  }

  checkMasterSizes() {
    const isMasterFreehold = this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold;
    const isMasterStrata = this.propertyLocation.Condo === EnumCondoTypeName.Master;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;
    const hasBuildingSF = !!this.property.BuildingSF;
    const hasFloors = !!this.property.Floors;
    if (isMasterFreehold || isLand || this.footPrintNotAvailable) {
      return true;
    }
    if (isMasterStrata) {
      return this.isAverageEstimationEnabled ? hasBuildingSF : true;
    }
    return hasFloors && hasBuildingSF;
  }

  getCondoType() {
    const condoTypeID = this.propertyLocation.Condo;
    return getCondoType(condoTypeID);
  }

  showGresbScore() {
    const useTypesToShowGresbScore = [UseTypes.Office, UseTypes.Industrial, UseTypes.Retail];
    return useTypesToShowGresbScore.includes(this.selectedUseTypeID);
  }

  onReviewed () {
    if (this.property.IsReviewed) {
      this.property.IsReviewed = 0;
    } else {
      this.property.IsReviewed = 1;
    }
  }

  togglePropertyDetails(): void {
    setTimeout(()=>{
      this.isPropertyDetailsExpanded = !this.isPropertyDetailsExpanded;
      sessionStorage.setItem(SessionStorageKeys.PropertyDetailsTabStatus, JSON.stringify(this.isPropertyDetailsExpanded));
    },200);
  }

  footPrintNotAvailablePopup() {
    this.showContributedFieldsPopup = true;
    this.contributedGBAProperty = JSON.parse(JSON.stringify(this.property))
  }

  closeManualfootprintAddPopup() {
    this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBA_SF');
    this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBASource');
    this.showContributedFieldsPopup = false;
  }

  mapFormToProperty(form) {
    let formValue = {};
    Object.keys(form.value).forEach(key => {
      formValue[BindFormControlToPropertyVariable[key]] = form.value[key];
    })
    return formValue;
  }

  saveManualFootprintInfo(form) {
    const controlKeys = Object.keys(form.controls)
    this.footPrintNotAvailable = true;
    this.property.BuildingSF = null;
    this.propertyForm.get('BuildingSF').setValue(null);
    this.multifloors = [];
    this.propertyForm.addControl('ContributedGBA_SF', new FormControl(null, []));
    this.propertyForm.addControl('ContributedGBASource', new FormControl(null, []));
    this.showContributedFieldsPopup = false;
    const formValue = this.mapFormToProperty(form);
    this.property = {...this.property, ...formValue};
    controlKeys.map(key => {
      const control = form.controls[key]
      if (control.dirty) {
        this.updateControlInPropertyForm(key, control);
      }
    })
  }
  updateControlInPropertyForm(key, control) {
    if (this.propertyForm.get(key)) {
      this.propertyForm.get(key).setValue(control.value);
      this.propertyForm.get(key).markAsDirty();
    } 
    ['OfficeForm', 'IndustrialForm', 'RetailForm', 'LandForm'].forEach(groupName => {
      const nestedControl = this.propertyForm.get(`${groupName}.${key}`);
      if (nestedControl) {
        nestedControl.setValue(control.value);
        nestedControl.markAsDirty();
      }
    });
  }

  isFootprintNotAvailableDisabled() {
    if (!this.property.PropertyID) {
      // Disable Footprint Not Available button for new strata property whose master has average estimation enabled or for master record
      return (this.propertyLocation.Condo === EnumCondoTypeName.Strata && this.isAverageEstimationEnabledForMaster) || this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || this.propertyLocation.Condo === EnumCondoTypeName.Master;
    } else if (this.propertyLocation.Condo === EnumCondoTypeName.Master_Freehold || this.propertyLocation.Condo === EnumCondoTypeName.Master) {
      // Disable for master records in edit property
      return true;
    } else if (this.propertyLocation.Condo === EnumCondoTypeName.Strata && this.isAverageEstimationEnabledForMaster) {
      // Enable for strata records whose master has average estimation in edit property
      return false;
    }
    // otherwise enable only when all polygons have footprint
    return this.multifloors.some(floor => floor.floorSize);
  }
  getFormGroupName() {
    return getFormNameByUseType(this.property.UseTypeID);
  }

  showDialogConfirmation(message: string, title: string, okButton: dialogConfirmSettings, cancelButton: dialogConfirmSettings, additionalBtn?: dialogConfirmSettings) {
    let dialogConfiguration: confirmConfiguration = new confirmConfiguration();
    const { Label: oklabel, Callback: okCallback } = okButton;
    const { Visible: showCancelBtn, Label: cancelLabel, Callback: cancelCallback, Classname: cancelBtnClass } = cancelButton;
    const { Visible: showAdditionalBtn, Label: additionalBtnLabel, Classname: additionalBtnClassname, Callback: additionalBtnCallback} = additionalBtn || {};
    dialogConfiguration.Message = message;
    dialogConfiguration.Title = title;
    dialogConfiguration.OkButton.Label = oklabel;
    dialogConfiguration.OkButton.Callback = () => {
      okCallback && okCallback();
    };
    dialogConfiguration.CancelButton.Label = cancelLabel ??  dialogConfiguration.CancelButton.Label;
    dialogConfiguration.CancelButton.Callback = () => {
      cancelCallback && cancelCallback();
    };
    dialogConfiguration.CancelButton.Classname = cancelBtnClass ?? dialogConfiguration.CancelButton.Classname;
    dialogConfiguration.CancelButton.Visible = showCancelBtn;
   if (additionalBtn) {
      dialogConfiguration.AdditionalButton = new confirmSettings();
      dialogConfiguration.AdditionalButton.Visible = showAdditionalBtn;
      dialogConfiguration.AdditionalButton.Label = additionalBtnLabel;
      dialogConfiguration.AdditionalButton.Classname = additionalBtnClassname;
      dialogConfiguration.AdditionalButton.Callback = () => {
        additionalBtnCallback && additionalBtnCallback();
      };
    }
    this._notificationService.CustomDialog(dialogConfiguration);
  }

  showBuildingSizeValidationError() {
    return !(this.propertyLocation.Condo === EnumCondoTypeName.Strata && this.isAverageEstimationEnabledForMaster && this.isMultiStrata);
  }
}
