import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { PropertyParcel } from '../../models/PropertyParcel';
import { LatLng } from '../../models/LatLng';

@Component({
  selector: 'app-select-parcels-for-property',
  templateUrl: './select-parcels-for-property.component.html',
  styleUrls: ['./select-parcels-for-property.component.css']
})
export class SelectParcelsForPropertyComponent implements OnInit {

  @Input() parcelInformation: any;
  @Input() hasNoExistingParcelInTileLayer: boolean;
  @Input() selectedLatLng: LatLng;
  @Output() closeParcelWindow: EventEmitter<any> = new EventEmitter<any>();
  @Output() addPropertyFromParcels: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewProperty: EventEmitter<any> = new EventEmitter<any>();
  isMultipleParcelSelectionEnabled = false;
  selectedParcels: any[] = []
  enablePropertyCreationFromSelectedParcel = false;
  showParcelDetails = false;
  newParcelsForPropertyCreation = [];
  selectedParcel: PropertyParcel;
  showMultiParcelConfirmationModal = false;
  constructor() { }

  ngOnInit() {
  }

  closeParcelInfoWindow() {
    this.newParcelsForPropertyCreation = [];
    this.selectedParcels = [];
    this.isMultipleParcelSelectionEnabled = false;
    this.enablePropertyCreationFromSelectedParcel = false;
    this.closeParcelWindow.emit();
  }

  onToggleChange(event: boolean) {
    this.isMultipleParcelSelectionEnabled = event;
  }

  selectMultiParcel() {
    const parcelsSelectedFromTiles = this.parcelInformation ? this.parcelInformation.filter(parcel => parcel.isSelected).length : 0;
    const parcelsCreated =  this.newParcelsForPropertyCreation ? this.newParcelsForPropertyCreation.filter(parcel => parcel.isSelected).length : 0;
    this.enablePropertyCreationFromSelectedParcel = (parcelsSelectedFromTiles + parcelsCreated) > 0;
  }

  addParcelDetails() {
    this.selectedParcel = new PropertyParcel();
    this.showParcelDetails = true;
  }

  onSaveParcelInfo(parcel) {
    if (parcel) {
      parcel.isSelected = true;
      this.newParcelsForPropertyCreation.push(parcel);

      const parcelsSelectedFromTiles = this.parcelInformation ? this.parcelInformation.filter(parcel => parcel.isSelected).length : 0;
      const parcelsCreated =  this.newParcelsForPropertyCreation ? this.newParcelsForPropertyCreation.filter(parcel => parcel.isSelected).length : 0;
      this.enablePropertyCreationFromSelectedParcel = (parcelsSelectedFromTiles + parcelsCreated) > 0;
    }
    this.showParcelDetails = false;
  }

  addPropertyFromSelectedParcels() {
    const parcelsSelectedFromTiles = this.parcelInformation?.filter(parcel => parcel.isSelected);
    const parcelsCreated = this.newParcelsForPropertyCreation?.filter(parcel => parcel.isSelected);
    parcelsCreated.forEach(parcel => parcel.ParcelSF = parseFloat(parcel.ParcelSF));
    const parcels = parcelsSelectedFromTiles ? parcelsSelectedFromTiles.map(parcel => parcel.object.properties) : [];
    this.selectedParcels = [...parcels, ...parcelsCreated];
    if (this.selectedParcels.length > 0) {
      this.showMultiParcelConfirmationModal = true;
    }
  }

  addNewPropertyFromParcel(parcel) {
    this.addNewProperty.emit(parcel)
  }

  onSave() {
    this.showMultiParcelConfirmationModal = false;
    this.addPropertyFromParcels.emit(this.selectedParcels);
  }

  onCancel() {
    this.showMultiParcelConfirmationModal = false;
  }
}
