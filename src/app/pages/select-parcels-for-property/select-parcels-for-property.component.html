<div class="parcel-container">
  <div class="close-and-toggle-wrapper">
    <div (click)="closeParcelInfoWindow()" class="close-icon-container">
      <i class="fa fa-remove" title="Close"></i>
    </div>
    <div class="switch-wrapper" *ngIf="!hasNoExistingParcelInTileLayer">
      <ui-switch [checked]="isMultipleParcelSelectionEnabled" (change)="onToggleChange($event)"></ui-switch>
      Multi-Select
    </div>
  </div>
  <div class="buttons-wrapper">
    <button class="btn btn-primary" *ngIf="isMultipleParcelSelectionEnabled || hasNoExistingParcelInTileLayer" (click)="addPropertyFromSelectedParcels()" [disabled]="!enablePropertyCreationFromSelectedParcel">
      <i class="fa fa-plus" aria-hidden="true"></i>Create Property from selected Parcels
    </button>
    <button class="btn btn-primary" *ngIf="isMultipleParcelSelectionEnabled && !hasNoExistingParcelInTileLayer" (click)="addParcelDetails()">
      <i class="fa fa-plus" aria-hidden="true"></i>Add Parcel
    </button>
  </div>
  <div *ngIf="(isMultipleParcelSelectionEnabled || hasNoExistingParcelInTileLayer) && newParcelsForPropertyCreation.length > 0">
    <ng-container *ngFor="let parcel of newParcelsForPropertyCreation">
      <div class="parcel-card">
        <div class="row parcel-property">
          <div class="col-sm-6 text-right">
            <h5>Parcel :</h5>
          </div>
          <div class="col-sm-6">
            <h5>{{parcel.ParcelNo}}
            <span class="checkbox-wrapper">
              <input type="checkbox" [(ngModel)]="parcel.isSelected" (change)="selectMultiParcel()"/>
            </span>
            </h5>
          </div>
        </div>
        <div class="parcel-card-details">
          <div class="row parcel-property" *ngIf="parcel.ParcelSF">
            <div class="col-sm-6 text-right"> Parcel Size : </div>
            <div class="col-sm-6">{{parcel.ParcelSF}}</div>
          </div>
          <div class="row parcel-property" *ngIf="parcel.Lot">
            <div class="col-sm-6 text-right"> Lot : </div>
            <div class="col-sm-6">{{parcel.Lot}}</div>
          </div>
          <div class="row parcel-property" *ngIf="parcel.Block">
            <div class="col-sm-6 text-right">Block:</div>
            <div class="col-sm-6"> {{parcel.Block}}</div>
          </div>
          <div class="row parcel-property" *ngIf="parcel.SubDivision">
            <div class="col-sm-6 text-right"> Subdivision:</div>
            <div class="col-sm-6"> {{parcel.SubDivision}}</div>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
  <ng-container *ngIf="hasNoExistingParcelInTileLayer && newParcelsForPropertyCreation.length === 0">
    <div class="parcel-card no-parcel-message">
      <div class="col-sm-6 text-right">
        <h5>Parcel :</h5>
      </div>
      <div class="col-sm-6">No Parcels Found</div>
    </div>
  </ng-container>
  <button class="btn btn-primary" *ngIf="hasNoExistingParcelInTileLayer" (click)="addParcelDetails()">
    <i class="fa fa-plus" aria-hidden="true"></i>Add Missing Parcels Manually
  </button>
  <ng-container *ngFor="let parcel of parcelInformation">
    <div class="parcel-card">
      <div class="row parcel-property">
        <div class="col-sm-6 text-right">
          <h5>Parcel :</h5>
        </div>
        <div class="col-sm-6">
          <h5>{{parcel.object.properties.Parcel_No}}
          <span class="checkbox-wrapper" *ngIf="isMultipleParcelSelectionEnabled">
            <input type="checkbox" [(ngModel)]="parcel.isSelected" (change)="selectMultiParcel()"/>
          </span>
          </h5>
        </div>
      </div>
      <div class="parcel-card-details">
        <div class="row parcel-property">
          <div class="col-sm-6 text-right"> Lot : </div>
          <div class="col-sm-6">{{parcel.object.properties.Lot}}</div>
        </div>
        <div class="row parcel-property">
          <div class="col-sm-6 text-right">Lot Area:</div>
          <div class="col-sm-6"> {{parcel.object.properties.Lot_Area}}</div>
        </div>
        <div class="row parcel-property">
          <div class="col-sm-6 text-right"> Plan:</div>
          <div class="col-sm-6"> {{parcel.object.properties.Plan}}</div>
        </div>
        <div class="row parcel-property">
          <div class="col-sm-6 text-right"> Strata Type:</div>
          <div class="col-sm-6"> {{parcel.object.properties.Strata_Typ}}</div>
        </div>
      </div>
      <button *ngIf="!isMultipleParcelSelectionEnabled" class="btn btn-primary" (click)="addNewPropertyFromParcel(parcel.object.properties)">
        <i class="fas fa-plus fa-xs mr-1"></i> Create new property on this parcel
      </button>
    </div>
  </ng-container>
</div>

<!--Parcel details modal-->
<div *ngIf="showParcelDetails">
  <imperium-modal [(visible)]="showParcelDetails" [title]="'Parcel Details'" [width]="'medium'" [height]="'medium'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-property-parcel-modal [CountryId]="CountryId" [propertyParcel]="selectedParcel" [EntityID]="EntityID" [fromAddParcel]="true"
        (onSave)="onSaveParcelInfo($event)" [latLng]="selectedLatLng"></app-property-parcel-modal>
    </ng-template>
  </imperium-modal>
</div>
<div *ngIf="showMultiParcelConfirmationModal">
  <imperium-modal [(visible)]="showMultiParcelConfirmationModal" [title]="'Multi Parcel Confirmation'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-multi-parcel-confirmation-modal
        (onSave)="onSave($event)"
        (onCancel)="onCancel($event)"
        [parcels]="selectedParcels"
      ></app-multi-parcel-confirmation-modal>
    </ng-template>
  </imperium-modal>
</div>
