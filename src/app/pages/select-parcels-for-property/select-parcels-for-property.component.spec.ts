import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SelectParcelsForPropertyComponent } from './select-parcels-for-property.component';

describe('SelectParcelsForPropertyComponent', () => {
  let component: SelectParcelsForPropertyComponent;
  let fixture: ComponentFixture<SelectParcelsForPropertyComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ SelectParcelsForPropertyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SelectParcelsForPropertyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
