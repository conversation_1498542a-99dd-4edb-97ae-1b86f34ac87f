.close-and-toggle-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}
.switch-wrapper {
  display: flex;
  gap: 5px;
}
.checkbox-wrapper {
  position: absolute;
  right: 10px;
}
.checkbox-wrapper input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4180c3;
}
.buttons-wrapper {
  display: flex;
  gap: 10px;
}
i.fa-plus {
  margin-right: 4px;
}
.parcel-container {
  position: absolute;
  top: 75px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px;
  height: calc(100vh - 100px);
  width: 450px;
  background: rgba(74, 74, 74, 0.92);
  white-space: break-all;
  overflow: scroll;
  z-index: 10;
}

.parcel-container::-webkit-scrollbar {
  width: 0;
}

.close-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  padding: 6px;
  cursor: pointer;
  background: var(--primary-blue-color);
  border: 0;
  transition: all 0.5s;
  height: 40px;
  width: 40px;
}

.parcel-card {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 10px;
  color: #fff;
  font-size: 16px;
  font-weight: 400;
}

.parcel-property {
  width: 450px;
}

.no-parcel-message {
  flex-direction: row !important;
}
