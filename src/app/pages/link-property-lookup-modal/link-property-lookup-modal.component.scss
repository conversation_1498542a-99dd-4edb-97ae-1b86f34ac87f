.spinRotate{
    font-size: 24px;
    position: absolute;
    right: -15px;
    top: 6px;
}
.tab-table-wrapper .table thead th {
    border-bottom: 0 !important;
    background: #191d64 !important;
    color: #fff !important;
    position: relative !important;
  }

  .tableHeader{
    border-bottom: 0 !important;
    background: #191d64 !important;
    color: #fff !important;
    position: relative !important;
    padding:10px 0px !important;
    font-size: 13px;
  }
 .tableRow{
    padding:10px 0px !important;
    font-size: 13px;
 }
  tr:nth-child(even) { 
    background-color: #f5f5f5; 
}

  .form-group {
    margin-bottom: 10px;
}


.btn-warn {
    color: #fff !important;
    background-color: #F18A00 !important;
    border-color: #F18A00 !important;
    cursor: pointer !important;
    display:flex;
    justify-content: space-between;
    align-items: center;
    
}

.padding-left{
  padding-left: 10px !important;
}

.btn-primary {
    color: #fff !important;
    background-color:#20a8d8 !important;
    border-color: #20a8d8 !important;
    cursor: pointer !important;
    display:flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 10px;
    margin-right: 20px;
}

.tableContainer {
    max-height: 500px;
    overflow-y: scroll;
    display:flex;
    justify-content: center;
    margin-left:15px;
}
