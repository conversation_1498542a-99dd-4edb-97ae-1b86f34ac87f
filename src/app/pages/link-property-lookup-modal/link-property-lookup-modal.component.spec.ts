import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { LinkPropertyLookupModalComponent } from './link-property-lookup-modal.component';

describe('LinkPropertyLookupModalComponent', () => {
  let component: LinkPropertyLookupModalComponent;
  let fixture: ComponentFixture<LinkPropertyLookupModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ LinkPropertyLookupModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LinkPropertyLookupModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
