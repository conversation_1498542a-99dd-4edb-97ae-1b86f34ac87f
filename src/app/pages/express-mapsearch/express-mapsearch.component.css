.mapcontainer{
    width:100%;
    transition: all .4s;
    height: 100%;;
}
.map-form-container {
    height: calc(100vh - 50px);
}

.main-container{
    z-index:10000000000;
    position:relative !important;
}

.header-wrapper {
    border-bottom: 1px solid #ccc;
    box-shadow: 0px 1px 3px #ccc;
    margin-bottom: 2px; 
    background:#fff;
}
#searchbox {   
    font-size: 12px;    
    width: 250px;
    margin-top: 10px;
}
.shorten-map{
    width:50%;
    float: left;
    transition: all 1.2s;
    position: relative;
}
.full-map{
    width:100%;
    float: left;
    transition: all 1.2s;
    position: relative;
}
.postal-code-serachbox{
    margin-top:10px;
    margin-bottom:10px;
    margin-right:13px;
}
.form-container{
    width:0;
    transition: all .4s;
    height: 94vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.shorten-form{
    width:50%;
    float: right;
    transition: all 1.2s;
}
.hide-form{
    width: 0%;
}
.header-wrapper .login-log{
    background: url(../../../assets/images/primaryLogo.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 95%;
}
.admin-area {
    padding: 15px;
    padding-right: 0;
}
.admin-area .user-name-admin{
    display: inline-block;
    padding-right: 10px;
    color: var(--primary-blue);
}
.admin-area .user-name-admin span{
    font-weight: bold;
    color: var(--primary-blue);
    margin-left: 5px;
}
.admin-area .logout{   
    margin-left: 15px;
}
.form-align{
    overflow: hidden!important;
    background-color: white;
    padding: 15px 0;
}

.searchCodeBox{
    position: absolute;
    right: 192px;
    /* left: 454px; */
    width: auto;
    top: 60px;
    z-index: 0;
    display: block;
}
.hidesearchCodeBox{
    z-index: -2!important;
    display: none!important;
}

.zipcodeSpin{
    position: absolute;
    top: 12px;
    right: 16px;
}
.noMap{
    z-index: 2;
    top: 21rem;
    left: 12rem;
    position: absolute
}
.noMap label{
    color: white;
    text-align: right;
    font-size: x-large;

}
#map-canvas-express {
    transition: height 0.2s ease;
}
.layers-dropdown{
    margin-top:10px;
    margin-bottom:10px;
    margin-right:13px;
}
.canvas-container {
    position:absolute;
    top:0px;
    left:0;
    background-color:transparent;
}


#overlay {
    position: absolute; 
    top: 100px; 
    color: #FFF; 
    text-align: center;
    font-size: 20px;
    background-color: rgba(221, 221, 221, 0.3);
    width: 640px;
    padding: 10px 0;
    z-index: 2147483647;
  }
  
 
  
.disable-pointer-events {
    pointer-events: none;

}
.map-wrapper {
    cursor: crosshair !important;
}

#map-canvas-express {
    transition: height 0.4s ease;
}


.pinBox {
    display:flex;
    align-items:center;
    gap:7px;
    height:100%;
}

.pinColorWrap {
  padding: 8px;
  background-color: white;
  display: flex;
  gap:10px;
  margin-top:70px;
  height:40px;
}

.btn-fs {
    background-color: white;
    padding: 7px;
    margin-right: 13px;
    position: absolute;
    z-index: 9999;
    top: 10px;
}

#fullScreenMapContainer{
    overflow: hidden;
}

.fs-icon-pos {
    font-size: 23px;
    color: rgb(34, 34, 34);
    cursor: pointer;
}

.info-card-pos{
    margin-left:13px;   
}

.screen-mode-change-icons-pos {
    position: absolute;
    top: 20px;
    right: 13px;
    z-Index: 1;
}

.icon-pos {
    font-size: 40px;
    color: var(--primary-blue-color);
    cursor: pointer;
}

.cam-icons-pos {
    position: absolute;
    top: 20px;
    right: 70px;
    z-index: 2147483646;
}

.expressMap {
    width: 100%;
}

.clear-canvas {
    font-size: 30px;
    background-color: white !important;
    color: rgb(34, 34, 34);
    position: absolute;
    top: 20px;
    right: 13px;
    z-index: 2147483647;
    outline: none;
    border: 0 !important;
}

.save-and-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 165px;
    border-radius: 10px;
    height: 40px;
    font-size: 20px !important;
    color: white;
    background-color: var(--primary-blue-color);
    margin-right: 10px;
    cursor: pointer;
}

.cam-icon-pos {
    margin-right: 10px;
    margin-bottom: 10px;
}

.fs-buttons-margin {
    margin-top: 10px;
}

.save-btn-pos {
    position: absolute;
    top: 20px;
    right: 110px;
    z-index: 2147483647;
}

.street-view-btns-bg {
    background-color: rgb(34, 34, 34) !important;
}

.fs-icon-pos-sv {
    color: #d3d3d3;
}

.fs-icon-pos-sv :hover {
    color: #fff !important;
}

.search-box {
    width: 160px;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-right: 13px;
}

