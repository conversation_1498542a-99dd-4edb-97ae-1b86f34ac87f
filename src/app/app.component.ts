import { Component } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { environment } from '../environments/environment';
import { LoginService } from './services/login.service';
import { SessionStorageKeys } from './enumerations/sessionStorageKeys';

export let browserRefresh = false;
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  subscription: Subscription;
  title = 'app';
  CryptoJS : any;

  constructor(private _loginService: LoginService
    , private router: Router) {
    this.subscription = router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        browserRefresh = !router.navigated;
        if (browserRefresh) {
          this.refreshToken();
        }
      }
    });
    setInterval(() => this.refreshToken(), 20700000); // refresh on 5h 45mts

  }
  refreshToken() {
    if (sessionStorage.getItem(SessionStorageKeys.LogInData)) {
      var loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
      if (loginData !== '' && !!loginData) {
        this.CryptoJS = require("crypto-js");
        const bytes = this.CryptoJS.AES.decrypt(loginData.toString(), environment.EncryptionKey);
        const loggedinData = JSON.parse(bytes.toString(this.CryptoJS.enc.Utf8));
        const userData = {
          EntityID: loggedinData.EntityID,
          RoleID: loggedinData.RoleID,
          PersonName: loggedinData.PersonName,
          Token: (sessionStorage.getItem(SessionStorageKeys.AccessToken))
        };
        const response_token = this._loginService.refreshToken(userData);
        response_token.subscribe(result => {
          const Token = result.body;
          sessionStorage.setItem(SessionStorageKeys.AccessToken, Token);
          if (!Token) {
            this.router.navigate(['/login']);
          }
        }, error => {
          this.router.navigate(['/login']);
        });
      }
    }
  }
}