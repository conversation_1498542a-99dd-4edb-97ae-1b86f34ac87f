import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import { LoginService } from './services/login.service';
import { SessionStorageKeys } from './enumerations/sessionStorageKeys';
import { EnumApplication } from './enumerations/application';


@Component({
  template: '',
})
export class AuthComponent implements OnInit {
    SSOToken: string;
    propertyId: number;
    address: string = '';
    sourceApplicationId:number;
    latitude: string = null;
    longitude: string = null;
    isCreateProperty: boolean = false;
    isEditProperty: boolean = false;
    cityName: string;
    sourceAddress: string;
    searchLocation: string = '';

    constructor(
        private router: Router
        , private activatedRouter: ActivatedRoute
        ,private _loginService: LoginService
        ) { 
          this.activatedRouter.queryParams.subscribe(params => {
            this.sourceApplicationId = params['sourceApplicationId'] ? parseInt(params['sourceApplicationId']) : null;
            this.cityName = params['cityName'] || '';
            this.sourceAddress = params['sourceAddress'] || 0;
            this.propertyId = parseInt(params['propertyId']) || 0;
            
            if(params['action'] == 'create'){
              this.isCreateProperty = true;
            }else if(params['action'] == 'edit'){
              this.isEditProperty = true;
            }

            const addressParam = params['address'];
            if(addressParam && addressParam != 'undefined' && addressParam != 'null'){
              this.address = decodeURIComponent(addressParam);
            }
            const lat = params['lat'];
            const lng = params['lng'];
            if(lat && lat != 'undefined' && lat != 'null' && lng && lng != 'undefined' && lng != 'null'){
              this.latitude = decodeURIComponent(lat);
              this.longitude = decodeURIComponent(lng);
            }
          });
        
          this.activatedRouter.params.subscribe(params => {
            const token = params['token'];
            this.SSOToken = token && token != 'undefined' && token != 'null' ? token : null;
            const sourceApplicationId = params['sourceApplicationId'];
            this.sourceApplicationId = sourceApplicationId && sourceApplicationId != 'undefined' && sourceApplicationId != 'null' ? parseInt(sourceApplicationId) : null;
          });
        }
  ngOnInit(): void {
    if (this.SSOToken && this.sourceApplicationId) {
      this.SSOValidateToken();
    }else{
      this.handleLoginFailure();
    }
  }

  SSOValidateToken() {
    const response_userLogin = this._loginService.SSOValidateToken(this.SSOToken);
    response_userLogin.subscribe(result => {
      if (result) {
        this.handleLoginSuccess();
      } else {
        this.handleLoginFailure();
      }
    });
  }

    handleLoginSuccess() {
      const userData = JSON.parse(sessionStorage.getItem(SessionStorageKeys.LoggedInUserData));
      const isNavigatedFromERC = this.sourceApplicationId == EnumApplication.ERC;
      
      const isNavigatedFromFAC = this.sourceApplicationId == EnumApplication.FAC;
      const isNavigatedFromLIT = this.sourceApplicationId == EnumApplication.LIT;
      if (isNavigatedFromERC) {
        sessionStorage.setItem(SessionStorageKeys.IsNavigatedFromERC, 'true');
        if (this.isEditProperty && this.propertyId && userData) {
          sessionStorage.setItem(SessionStorageKeys.IsEditProperty, 'true');
          sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(this.propertyId));
        } else if (this.isCreateProperty && userData) {
          sessionStorage.setItem(SessionStorageKeys.IsCreateProperty, 'true');
          sessionStorage.setItem(SessionStorageKeys.Address, this.address);
          sessionStorage.setItem(SessionStorageKeys.Latitude, this.latitude);
          sessionStorage.setItem(SessionStorageKeys.Longitude, this.longitude);
        }
        this.router.navigate(['/expressmapsearch']);
      } else if (isNavigatedFromFAC) {
        sessionStorage.setItem(SessionStorageKeys.isNavigatedFromFAC, 'true');
        if (this.propertyId && userData) {
          sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(this.propertyId));
        }
        this.router.navigate(['/expressmapsearch']);
      } else if (isNavigatedFromLIT) {
        sessionStorage.setItem(SessionStorageKeys.isNavigatedFromLIT, 'true');
        if (this.propertyId && userData) {
          sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(this.propertyId));
        }
        else if ((this.sourceAddress || this.cityName) && userData) {
          sessionStorage.setItem(SessionStorageKeys.SourceAddress, this.sourceAddress);
          sessionStorage.setItem(SessionStorageKeys.CityName, this.cityName);
          sessionStorage.setItem(SessionStorageKeys.IsFromMapSearch, 'true');
        }
        this.router.navigate(['/expressmapsearch']);
      } else{
        this.handleLoginFailure();
      }
    }
    
    
    handleLoginFailure() {      
      this.router.navigate(['/login']);
    }
}
