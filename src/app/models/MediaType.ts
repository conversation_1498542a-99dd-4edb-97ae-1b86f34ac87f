export class MediaType {
    MediaTypeID: number;
    MediaTypeName: string;
    IsActive: boolean;
    CreatedDate: any;
}

export class MediaSubType {
    MediaSubTypeID: number;
    MediaSubTypeName: string;
    IsActive: boolean;
}

export class Media {
    MediaID: number = null;
    PropertyID: number = null;
    RelationID: number = null;
    RelationshipTypeID: number = null;
    MediaName: string = null;
    Height: number = null;
    Width: number = null;
    Size: number = null;
    Path: string = null;
    Ext: string = null;
    URL: any = null;
    UploadPathURL: string = null;
    OrginalURL: string = null;
    Description: string = null;
    CreatedDate: string = null;
    CreatedBy: number = null;
    ModifiedDate: string = null;
    ModifiedBy: number = null;
    ModifiedByName: string = null;
    MediaRelationshipID: number = null;
    MediaRelationTypeID: number = null;
    MediaRelationTypeName: string = null;
    MediaTypeID: number = null;
    MediaTypeName: string = null;
    MediaSubTypeID: number = null;
    MediaSubTypeName: string = null;
    IsActive = true;
    IsDefault = 0;
    File: File = null;
    IsOwnMedia: any = null;
    ChangeLogJSON: any = null;
    ApplicationID: number = null;
    MediaSourceID: number = null;
    SourceComments: string = null;
    fileSize: any = null;
    fileObject: any = null;
    fileArray = [];
    uploadingFileArray = [];
    IsUploaded = false;
    fileObjectAttach: Array<any> = [];
    attachfileSize: any = null;
    multipleArrayCount: number = null;
    mediaWEBsource = false;
    DataArray: Array<any> = [];
    IsLoading = false;
}
export enum MediaTypeEnum {
    ArtistDrawing = 1,
    FlyerBrochure = 2,
    BuildingImage = 3,
    StatisticsAnalytics = 4,
    LegalDocs = 5,
    AerialImagery = 6,
    PropertyManager = 7,
    LandLotImage = 8,
    NewsPublication = 9,
    Interior = 10,
    ObliqueAerial = 11,
    Title = 12,
    ListingSign = 13,
    SuiteFloorPlan = 14,
    TenantRoster = 15,
    OtherMedia = 16,
    SitePlan = 17,
    Signage = 19,
    RegisteredLease = 20,
    RegisteredSublease = 21,
    ParcelPlat = 22,
    LeaseFolio = 23,
    MapImage = 24
}
export class MediaSource {
    MediaSourceID: number;
    MediaSourceName: string;
    IsActive: boolean;
}
export enum MediaSubTypeEnum {
    MainPhoto = 1,
    RightSide = 2,
    Lobby = 3,
    Rear = 4,
    LeftSide = 5,
    OutBuilding = 6,
    Front = 7,
}
export enum MediaSourceTypeEnum {
    EmpiricalPhotography = 1,
    Streetview = 2,
    OwnerWebsite = 3,
    BuildingWebsite = 4,
    BrokerWebsite = 5,
    BrokerEmail = 6,
    Brochure = 7,
    BrokerPDF = 8,
    ThridPartyWebsite = 9,
}
export enum MediaRelationTypeEnum {
    Property = 1,
    Listing = 2,
    Suite = 3,
    Sale = 4,
    Company = 5,
    Branch = 6,
    Person = 7,
    Entity = 8,
    AllMedia = 9,
    Lease = 11,
    Media = 12
}