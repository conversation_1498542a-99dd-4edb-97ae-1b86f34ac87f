export class PropertyAdditionalAddress {

    public CountryID: number;
    public Latitude: number;
    public Longitude: number;
    public PropertyID: number;
    public AddressID: number=0;
    public IsActive: boolean;
    public Address1: string;
    public Address2: string;
    public StateID: number;
    public CityID: number;
    public ZipCode: string;
    public StreetNumber: string;
    public AddressStreetName: string;
    public AddressStreetNumber: string;
    public SuffixID: number;
    public Suffix2ID: number;
    public CountyID: number;
    public StreetNumberMin: string;
    public StreetNumberMax: string;
    public Zip4: string;
    public FloorNumber: string;
    public PrefixID: number;
    public Prefix2ID: number;
    public QuadrantID: number;
    public BuildingNumber: string;
    public PartOfCenterComplex: number;
    public ComplexName: string;
    public PrimaryStreet: string;
    public PrimaryAccess: string;
    public PrimaryTrafficCount: string;
    public PrimaryTrafficCountDate: Date;
    public PrimaryFrontage: string;
    public SecondaryStreet: string;
    public SecondaryAccess: string;
    public SecondaryTrafficCount: string;
    public SecondaryTrafficCountDate: Date;
    public SecondaryFrontage: string;
    public EastWestSt: string;
    public NorthSouthSt: string;
    public PersonID: number;
    public EntityID: number;
    public StateDisplayName: string;
    public CityDisplayName: string;
    public CountyDisplayName: string;
    public AddressType = 0;
    public ChangeLogJSON: string = null;
    public ApplicationID: any = null;
}