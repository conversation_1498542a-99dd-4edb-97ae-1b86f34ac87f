
export class Property {
    PropertyID: number=0;
    PropertyName: string=null;
    PropertyUse: any=null;
    EntityID:number=null;
    Floors: any=null;
    ConstructionStatusID: any=null;
    AdditionalGeneralUse1: any=null;
    AdditionalGeneralUse2: any=null;
    //ParkingSpaces: any=null;
    //ParkingRatio: any=null;
    LEEDStatusID: any=null;
    ConstructionTypeID: any=null;
    HVACTypeID: any=null;
    AddlSpecificUse: any=null;
    SprinklerTypeID: any=null;
    EstimatedCompletionDate: Date=null;
    IsADAAccessible: any=null;
    IsVented: any=null;
    IsCondo: any=null;
    IsOwnerOccupied: any=null;
    TenancyTypeID: any=null;
    IsEnergyStar: any=null;
    UseTypeID: any=null;
    //AdditionalUse1: any=null;
    //LotSizeAcres: any=null;
    //AdditionalUse2: any=null;
    SizeSourceID: number=null;
    ClassTypeID: any=null;
    BuildingComments: any=null;
    CoreFactor: any=null;
    Amenities: any=null;
    Quadrant: any=null;
    MetroId: any=null;
    MarketId: any=null;
    GovernmentInterestID: any=null;
    ConstructionStartDate: any=null;
    EstCompletionDate: any=null;
    NoOfOfficeFloor: any=null;
    PassengerElevators: any=null;
    ParkingElevators: any=null;
    ElevatorBanks: any=null;
    FreighElevators: any=null;
    HasRetailSF: any=null;
    DockHigh: any=null;
    DockComments: any=null;
    GradeLevelDriveIn: any=null;
    GradeComments: any=null;
    Truckwell: any=null;
    TruckwellComments: any=null;
    DockLevelers: any=null;
    DockLevelersCapacity: any=null;
    WirePanel1: any=null;
    PowerComments: any=null;
    BayWidth: any=null;
    BayDepth: any=null;
    Unit: any=null;
    IncludeinAnalytics: any=null;
    GBASizeSource: any=null;
    OfficeAC: any=null;
    OfficeACId: any=null;
    OfficeHeat: any=null;
    OfficeHeatId: any=null;
    FinishedOfcMezzTotalAvail: any=null;
    FinishedOfcMezzSF: any=null;
    UnfinishedOfcMezzTotalAvail: any=null;
    UnfinishedOfcMezzSF: any=null;
    LotSizeSourceID: any=null;
    LotSizeSourceName: any=null;
    ZoningClassID: any=null;
    ZoningClassName: string=null;
    ZoningCode: any=null;
    ZoningCodeName: string=null;
    PotentialZoningID: number=null;
    PotentialZoningClassName: string=null;
    SurroundingLandUse: any=null;
    SurroundingLandUseText: string=null;
    RailServed: any=null;
    RailProvider: any=null;
    IsFloodPlain: any=null;
    GasProvider: any=null;
    Gas: any=null;
    ElectricProvider: any=null;
    Electric: any=null;
    Water: any=null;
    WaterProvider: any=null;
    Sewer: any=null;
    SewerProvider: any=null;
    FiberOptOrBroadbandProvider: any=null;
    BuildingNRA: any=null;
    NRASizeSourceId: any=null;
    SmallestFloor: any=null;
    LargestFloor: any=null;
    QuarterBuilt: any=null;
    EnergyProviderID: any=null;
    HasRenovationStatus: any=null;
    RenovationStatusName: string=null;
    RoofTypeID: any=null;
    BTSSpec: any=null;
    Reservedcoveredparkingspaces: any=null;
    ResCoveredParkingDollerPerMonth: any=null;
    ResSurfaceParkingSpace: any=null;
    ResSurfaceParkingDollerPerMonth: any=null;
    NoOfOffices: any=null;
    OfficeSFPercentage: any=null;
    CraneServed: any=null;
    CraneComments: any=null;
    HasYardFenced: any=null;
    HasPavedYard: any=null;
    HasYardUnfenced: any=null;
    YardPaved: any=null;
    WetLab: any=null;
    DryLab: any=null;
    WetLabSF: any=null;
    DryLabSF: any=null;
    InternalComments: any=null;
    CleanRoom: any=null;
    CleanRoomSF: any=null;
    CleanRoomClassID: any=null;
    HasReservedCoveredParking: any=null;
    HasReservedSurfaceParking: any=null;
    UnreservedCoveredParkingSpaces: any=null;
    UnreservedCoveredParkingDollerPerMonth: any=null;
    UnreservedSurfaceParkingSpaces: any=null;
    UnreservedSurfaceParkingDollerPerMonth: any=null;
    HasFreeParking: number=null;
    HasUnreservedCoveredParking: any;
    HasUnreservedSurfaceParking: any=null;
    HasPaidParking: number=null;
    Depth: any=null;
    EarthquakeZoneID: any=null;
    BuildingSF: any=null;
    LotSizeAc: any=null;
    NLAac: any=null;
    OfficeSF: any=null;
    Anchors: any=null;
    TotalAnchorSF: any=null;
    LotSizeSF: number=null;
    NLA_SF: any=null;
    SpecificUseID: number=null;
    BuildSpecStatusID: any=null; 
    BuildSpecStatusName: string=null;   
    PropertyComments: string=null;
    UtilityComments: string=null;
    PropertyId: number=null;
    Address: string=null;
    City: string=null;
    CityName: string=null;
    State: string=null;
    StateAbbr: string=null;
    Zip: string=null;
    ZipCode: string=null;
    GeneralUse: string=null;
    SpecificUse: string=null;
    BuildingSize: number=null;
    GrossBuildingArea: number=null;
    YearBuilt: number=null;
    YearRenovated: number=null;
    NumberOfFloor: number=null;
    SizeSource: string=null;
    MixedUseAllocation: string=null;
    ConstructionType: string=null;
    LotSizeAcres: number=null;
    Zoning: any=null;
    ParkingSpaces: number=null;
    ParkingRatio: number=null;
    AdditionalUse1: string=null;
    AdditionalUse2: string=null;
    Condo: string=null;
    OwnerOccupied: string=null;
    Tenancy: string=null;
    Class: string=null;
    HVACType: string=null;
    SprinklerType: string=null;
    ADAAccessible: string=null;
    Quardrant: number=null;
    ParkComplexName: string=null;
    ParcelInformation: string=null;
    ParcelInfo: string = null;
    Latitude: number=null;
    Longitude: number=null;
    Metro: string=null;
    Market: string=null;
    SubMarket: string=null;
    PropertyManagerName: string=null;
    PropertyManagerPhone: number=null;
    PropertyManagerEmail: string=null;
    PropertyManagerWebsite: string=null;
    PropertyManagerAddress: string=null;
    ImageUrl: string=null;
    ConstructionStatus: string=null;
    RetailFrontage: string=null;
    IsMixedUse: string=null;
    TotalOfficeSF: number=null;
    IsCraneServed: string=null;
    IsRailServed: string=null;
    NumberOfDockDoor: number=null;
    NumberOfTruckkDoors: number=null;
    Phase: number=null;
    Volts: number=null;
    Amps: number=null;
    NumberOfOffices: number=null;
    NumberOfRestRooms: number=null;
    YardFencedStatus: string=null;
    YardFencedArea: number=null;
    YardUnFencedStatus: string=null;
    YardUnFencedArea: number=null;
    YardPavedStatus: string=null;
    YardPavedArea: number=null;
    HasPortAccess: number=null;
    HasSprinkler: number=null;
    CommonAreaFactor: number=null;
    RetailSF: number=null;
    MinFloorSize: number=null;
    MaxFloorSize: number=null;
    AvgFloorSize: number=null;
    NumberOfOfficeFloors: number=null;
    BayDepthWidth: string=null;
    VerifiedStatus: string=null;
    TrafficCount: number=null;
    ListingType: string=null;
    
    // MinAvailableSpace: number=null;
    // MaxAvailableSpace: number=null;
    // MinLeaseRate: number=null;
    // MaxLeaseRate: number=null;
    // MinBuildingSize: number=null;
    // MaxBuildingSize: number=null;
    District: number=null;
    BuildingClass: string=null;
    ParcelNumber: string=null;
    TypicalFloorArea: number=null;
    NumberOfCommercialUnits: number=null;
    NumberOfElevators: number=null;
    NumberOfDockDoors: number=null;
    NumberOfDriveInDoors: number=null;
    NumberOfTruckwellDoors: number=null;
    Rail: string=null;
    LotSizeSM: number=null;
    Sprinkler: string=null;
    PropertyImageUrl1: string=null;
    PropertyImageUrl2: string=null;
    MapImageUrl: string=null;

    AvailableSpace: number=null;
    LeaseRate: number=null;
    SalePrice: number=null;
    DateAvailable: string=null;
    ExcludeNegotiableRate: string=null;
    TermType: string=null;
    OccupancyPercent: number=null;

    ClearHeightMin: number=null;
    ClearHeightMax: number=null;
    ColumnSpacingLen: number=null;
    ColumnSpacingWidth: number=null;
    LGA: string=null;
    GeoscapePropertyID: string=null;
    CounsilTaxID: string=null;
    ValuerGeneralID: string=null;
    FloorLoading: number=null;
    HasYard: any=null;
    MarketName:string=null;
    LoadingNotes:string=null;
    LastRenovationDate:any=null;
    SpecificUseID1:number=null;
    SpecificUseID2:number=null;
    SpecificUseName:string=null;
    ClassTypeName:string=null;
    TenancyName:string=null;
    GovernmentInterestName:string=null;
    ConstructionStatusName:string=null;
    ConstructionTypeName:string=null;
    SprinklerTypeName:string=null;
    HVACTypeName:string=null;
    LeedStatusName:string=null;
    MainPhotoUrl:string=null;

    EnergyStarRatingID: number;
    EnergyStarRatingName: string;
    HardstandArea: number = null;
    HardstandAreaSM: number = null;
    ChangeLogJSON :any =null;
    ApplicationID:any =null;
    zoomLevel:any = null;
    
    PropertyResearchTypeID: any;
    UseNLAForAllocation: boolean;
    BldgSizeSourceID: any;
    BuildingSizeSM: number;
    BuildingNRASM: number;
    IsSkipped: boolean;
    IsMultiplePolygonsNeeded: boolean;
    NeedsResearchComments: string;
    TypicalFloorSize: any;
    TypicalFloorSizeSM: number;
    CondoTypeID: number;
    CondoTypeName: string;
    IsSelected: boolean;
    AddressStreetName: string;
    StreetNumberMin: string;
    StreetNumberMax: string;
    PropertyType: string;
    GenUseId: string;
    SpecificUsesId: number;
    UseTypeId: number;
    ResearchTypeName: string;
    ResearchTypeID: number;
    StrataProperties: any;
    MasterPropertyID: any;
    GRESBScore:any =null;
    SubMarketID: string = null;
    Vacancy: string;
    HVAC: 0 | 1;
    BookValueDate: any;
    BookValue: number;
    HasSolar: 1 | 0;
    ActualCompletion: any;
    CurrentTitle: any;
    TitleReferenceDate: any;
    BuildingWebsite: any;
    AmenitiesComments: any;
    AmenitiesTypeIDs: any;
    CountOfPassenger: any;
    CountOfFreight: any;
    CountOfParking: any;
    AmenitiesType: any;
    HardstandAreaSourceID: any;
    OfficeHVAC: any;
    Lifts: any;
    LiftsCount: any;
    Features: any;
    PowerType: any;
    OccupiedPercentage: any;
    ReservedParkingSpaces:number = null;
    UnreservedParkingSpaces:number = null;
    HasReservedParkingSpaces:number = null;
    HasUnreservedParkingSpaces:number = null;
    ReservedParkingSpacesRatePerMonth:number = null;
    UnreservedParkingSpacesRatePerMonth:number = null;
    ContributedGBA_SF: number=null;
    GRESBScoreMin: number=null;
    GRESBScoreMax: number=null;
    OfficeMezzanine: any;
    OfficeMezzanineSize: any;
    Awnings: any;
    AwningsCount: any;
    AwningsSize: any;
    WaterStarRatingID: any;
    GreenStarRatingID: any;
    ContributedGBA_SM: number = null;
    ContributedGBASizeSourceID: any;
    IndustrialGLASource: any;
    RetailGLARSource: any;
    NRASizeSourceID: any;
    GLARSizeSourceID: any;
    GLASizeSourceID: any;
    Mezzanine_Size_SF: any;
    Awnings_Size_SM: any;
    Awnings_Size_SF: any;
    GLA_SF: any;
    GLA_SM:any;
    GLAR_SF: any;
    GLAR_SM: any;
    IsReviewed: any;
    LastReviewedBy: string;
    LastReviewedDate: string;
    ReviewedEntityId: any;
    PropertyReviewedByName: string;
    PropertyModifiedByName: string;
    PropertyModifiedDate: string;
    LandUse: number;
    Width: number;
    CountryName: string | null = null;
    TypicalFloorSizeSourceID : any;
    HasNoExistingParcelInTileLayer: 1 | 0;
    HasNoBuildingFootprints : 1 | 0;
    ContributedSourceComments: string | null = null;
    TIAllowance: any = null;
    NoOfUnits: any = null;
    IsAverageEstimationEnabled: 1 | 0 = 0;
    IsLettableOverrideEnabled: 1 | 0 = 0;
    TotalLettableSourceID: number = null;
    TotalLettableSize_SF: number = null;
    TotalLettableSize_SM: number = null;
}

export class Prefix {
    PrefixName: string=null;
    PrefixID: number=null;
    IsActive: number=null;
}

export class ParcelDetails {
    PropertyID: number=null;
    ParcelID: number = 0;
    ParcelNo: string=null;
    Lot: string=null;
    Block: string=null;
    SubDivision: string=null;
    ParcelSF: number=null;
    EntityID:number=null;
}

