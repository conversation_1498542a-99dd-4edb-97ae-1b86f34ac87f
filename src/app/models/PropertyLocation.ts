export class PropertyLocation {


    PropertyID: number = 0;
    EntityID:number;
    PropertyName: string = null;
    StreetName: string = null;
    AddressType: number = 0;
    StreetNumberMin: string = null;
    StreetNumberMax: string = null;
    Direction: any = null;
    StreetPrefix1: string = null;
    StreetPrefix2: string = null;
    StreetSuffix1: string = null;
    StreetSuffix2: string = null;
    State: any = null;
    City: any = null;
    County: number = null;
    CountryID: any = null;
    Country: any = null;
    Condo: any = null;
    CondoUnit: any = null;
    MasterPropertyId :any =null;
    PartOfComplex: any = null;
    Complex: any = null;
    Latitude: any = null;
    Longitude: any = null;
    RoofTopGeo: any = null;
    RoofTypeID: any = null;
    RooftopSourceID: any = null;
    Quadrant: any = null;
    EastWestStreet: any = null;
    NorthSouthStreet: any = null;
    PrimaryStreet: any = null;
    PrimaryAccess: any = null;
    PrimaryTrafficCount: any = null;
    PrimaryTrafficCountDate: any = null;
    PrimaryFrontage: any = null;
    SecondaryStreet: any = null;
    SecondaryAccess: any = null;
    SecondaryTrafficCount: any = null;
    SecTrafficCntDate: any = null;
    SecondaryTrafficCountDate: any = null;
    SecondaryFrontage: any = null;
    Lot: any = null;
    Block: any = null;
    Subdivision: any = null;
    LegalDesc: any = null;
    Zip: any = null;
    GISShapeID:number = null;;

    public Address1: string = null;
    public Address2: string = null;
    public AddressStreetNumber: string = null;
    public AddressStreetName: string = null;
    public BuildingNumber: string = null;
    public FloorNumber: string = null;
    public Zip4: string = null;
    public ZCoordinate: number = null;
    CountyId: number = null;
    UseAddressAsPropertyName:boolean=true;
    StateDisplayName:string;
    CityDisplayName:string;
    CountyDisplayName:string;
    ChangeLogJSON :any =null;
    ApplicationID:any =null;
    LGA: string;
    GeoscapePropertyID: string;
    CounsilTaxID: string;
    ValuerGeneralID: string;


}

