export class PropertyMasterRollup {
    // Keys for aggregating sum values
    BuildingSF: any = null;
    BuildingSizeSM: any = null;
    aggregateSumKeys: any = null;
    GradeLevelDriveIn: any = null;
    DockHigh: any = null;
    Truckwell: any = null;
    BuildingNRA: any = null;
    BuildingNRASM: any = null;
    WetLabSF: any = null;
    WetLabSM: any = null;
    DryLabSF: any = null;
    DryLabSM: any = null;
    NoOfAnchor: any = null;
    TotalAnchorSF: any = null;
    TotalAnchorSM: any = null;
    ParkingSpaces: any = null;
    Reservedcoveredparkingspaces: any = null;
    ResSurfaceParkingSpace: any = null;
    UnreservedCoveredParkingSpaces: any = null;
    UnreservedSurfaceParkingSpaces: any = null;
    PassengerElevators: any = null;
    ParkingElevators: any = null;
    RetailFrontage: any = null;
    RetailFrontageM: any = null;
    ElevatorBanks: any = null;
    FreighElevators: any = null;
    ParkingRatio: any = null;
    ReservedParkingSpaces: any = null;
    UnreservedParkingSpaces: any = null;
    AwningsCount: any = null;
    LiftsCount: any = null;
    TrafficCount: any = null;
    
    // Keys for range values
    SmallestFloor: any = null;
    SmallestFloorSM: any = null;
    LargestFloor: any = null;
    LargestFloorSM: any = null;
    NoOfOfficeFloors: any = null;
    OfficeSF: any = null;
    OfficeSM: any = null;
    TypicalFloorSize: any = null;
    TypicalFloorSizeSM: any = null;
    TypicalFloorSizeSourceID: any = null;
    TIAllowance: any = null;
    Floors: any = null;
    YearBuilt: any = null;
    YearRenovated: any = null;
    QuarterBuilt: any = null;
    ClearHeightMin: any = null;
    ClearHeightMinM: any = null;
    ClearHeightMax: any = null;
    ClearHeightMaxM: any = null;
    ColumnSpacingLen: any = null;
    DockLevelers: any = null;
    EnergyStarRatingName: any = null;
    WaterStarRatingName: any = null;
    GreenStarRatingName: any = null;
    CleanRoomClass: any = null;
    ResCoveredParkingDollerPerMonth: any = null;
    ResSurfaceParkingDollerPerMonth: any = null;
    UnreservedCoveredParkingDollerPerMonth: any = null;
    UnreservedSurfaceParkingDollerPerMonth: any = null;
    ReservedParkingSpacesRatePerMonth: any = null;
    UnreservedParkingSpacesRatePerMonth: any = null;
    ContributedGBA_SF: any = null;
    ContributedGBA_SM: any = null;
    GRESBScoreMin: any = null;
    GRESBScoreMax: any = null;
    Vacancy: any = null;
    BookValue: any = null;
    Mezzanine_Size_SF: any = null;
    Mezzanine_Size_SM: any = null;
    GLA_SF: any = null;
    GLA_SM: any = null;
    GLAR_SF: any = null;
    GLAR_SM: any = null;
    Awnings_Size_SF: any = null;
    Awnings_Size_SM: any = null;
    HardstandArea: any = null;
    HardstandAreaSM: any = null;
    ConstructionStartDate: any = null;
    EstCompletionDate: any = null;
    ActualCompletion: any = null;
    TitleReferenceDate: any = null;
    BookValueDate: any = null;
    NoOfUnits: any = null;

    // Keys for boolean (Yes/No) values
    RailServed: any = null;
    CraneServed: any = null;
    HasPortAccess: any = null;
    WetLab: any = null;
    DryLab: any = null;
    IsOwnerOccupied: any = null;
    HasFrontDeskStaff: any = null;
    HasGroundFloorCafeSpace: any = null;
    HasRenovationStatus: any = null;
    HasResCoveredParking: any = null;
    HasResSurfaceParking: any = null;
    HasUnreservedCoveredParking: any = null;
    HasUnreservedSurfaceParking: any = null;
    HasFreeParking: any = null;
    HasPaidParking: any = null;
    HasRetailSF: any = null;
    IsVented: any = null;
    IsADAAccessible: any = null;
    HasSprinkler: any = null;
    HasYardFenced: any = null;
    HasPavedYard: any = null;
    HasYardUnfenced: any = null;
    YardPaved: any = null;
    HasSolar: any = null;
    HVAC: any = null;
    IncludeinAnalytics: any = null;
    Mezzanine: any = null;
    Awnings: any = null;
    Lifts: any = null;

    // Keys for unique identifiers or attributes
    SpecificUseName: any = null;
    TenancyName: any = null;
    ClassTypeName: any = null;
    BldgSizeSourceName: any = null;
    RoofTypeName: any = null;
    ConstructionTypeName: any = null;
    Phase: any = null;
    Volts: any = null;
    Amps: any = null;
    WirePanel1: any = null;
    PowerComments: any = null;
    FloorLoading: any = null;
    SprinklerTypeName: any = null;
    HVACTypeName: any = null;
    GovernmentInterestName: any = null;
    GovernmentInterestID: any = null;
    BuildSpecStatusID: any = null;
    BuildSpecStatusName: any = null;
    PropertyUse: any = null;
    UseTypeID: any = null;
    AmenitiesTypeIDs:any = null;
    BuildingWebsite: any = null;
    ConstructionStatusName: any = null;
    ZoningCode: any = null;
    CurrentTitle: any = null;
    FeatureIDs: any = null;
    PowerTypeName: any = null;
    SizeSourceName: any = null;
    ContributedGBASizeSourceName: any = null;
    HardstandAreaSourceName: any = null;
    GLASizeSourceName: any = null;
    GLARSizeSourceName: any = null;
    OfficeHVAC: any = null;
    TotalLettableSourceName: any = null;
    IsLettableOverrideEnabled: any = null;
    TotalLettableSize_SF: any = null;
    TotalLettableSize_SM: any = null;
    childFreeHoldsAdditionalUses: {
        Section: string;
        UseTypeID: number;
        UseTypeName: string;
        SpecificUsesID: number;
        SpecificUsesName: string;
        Floors: number;
        FloorSizeSF: any;
        FloorSizeSM: any;
        MinFloor: number | string;
        MaxFloor: number | string;
    }[] = [];
    
}
