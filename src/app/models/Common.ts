export enum Generaluse {
    Retail = 2,
    Industrial = 3,
    Apartments = 4,
    Office = 5,
    Land = 7,
    SpecialUse = 9
}

export enum SpecificUses {
    MixedUse = 96
}

export interface MultiPolygon {
    specificUse?: number;
    minFloor?: number;
    maxFloor?: number;
    floorCount?: number;
    floorSize?: number;
    shape?: string;
    BuildingFootPrintID?: number;
    localBuildingFootPrintID?: string;
    description?: string;
    additionalUse?: number;
    mainSpecificUseTypeId?: number;
    additionalSpecificUseTypeId?: number;
    isAdditionalUse?: boolean;
}

export enum ConstructTypeStatus {
    Retired = 'Retired',
    InUse = 'In Use',
}

export interface CondoTypeChangeListenerType {
    isFloorBtnDisable: boolean;
    Condo: any;
    isAverageEstimationEnabled?: boolean;
}
