export class SaleComp {
  SaleID: number;
  PropertyID: number;
  ParcelID: number;
  StructureID: number;
  LotID: number;
  ListingID: number;
  ArmsLengthTypeID: number;
  RightsIncludedTypeID: number;
  ConveyedID: number;
  LotSizeSF: number;
  LotSizeAc: number;
  LotSizeHec: number;
  LegalDescription: string;
  IsCondoSale: number;
  CondoUnit: string;
  CondoTypeID: number;
  CondoTypeName: string;
  CondoTypeDisplayText: string;
  EncumbranceTypeID: number;
  EncumbranceNote: string;
  DeedOrSaleDate: any;
  DateRecorded: any;
  InstrumentDocTypeID: number;
  InstrumentNotes: string;
  DeedDocNumber: string;
  TransferTax: number;
  SoldPrice: number;
  SaleTypeID: number;
  PriceConfirmationStatusID: number;
  TransactionNotes: string;
  Units: number;
  PercentageOccuppied: number;
  IsPortfolio: number;
  PortfolioSalePrice: number;
  PortfolioID: number;
  PortfolioNotes: string;
  ListingNotes: string;
  AskingPrice: number;
  ListingDate: any;
  DateOnMarket: any;
  DaysOnMarket: number;
  SalePriceSourceID: number;
  ScheduledGrossIncome: number;
  VacancyAllowancePercentage: number;
  EffectiveGrossIncome: number;
  Expenses: number;
  IncomeSourceID: number;
  CapRateSourceID: number;
  CapRatePercentage: number;
  CapRateTypeID: number;
  IncomeNotes: string;
  CashflowTypeID: number;
  NOISourceID: number;
  NOITypeID: number;
  SaleConditions: string;
  LoginEntityID: number;
  SalePricePerHec: number;
  SalePricePerSM: number;
  SalePricePerUnit: number;
  PortfolioName: string;
  SoldSF: number;
  SoldSM: number;
  SalePricePerSF: number;
  Buyer: string;
  Seller: string;
  RelationType: string;
  RelationID: number;
  ArmsLengthTypeName: string;
  RightsIncludedTypeName: string;
  ConveyedName: string;
  LotSizeSM: number;
  EncumbrancesTypeName: string;
  InstrumentDocTypeName: string;
  SaleTypeName: string;
  CreatedByName: string;
  CreatedDate: any;
  ModifiedBy: string;
  ModifiedByName: string;
  ModifiedDate: any;
  IncomeSourceName: string;
  CapRateSourceName: string;
  CapRateTypeName: string;
  CashFlowTypeName: string;
  NOISourceName: string;
  NOITypeName: string;
  SaleConditionNames: string;
  JurisdictionId: string;
  VGZone: string;
  ZoneDesc: string;
  VGPurpose: string;
  UCV: string;
  UCVDate: any;
  PlanNumber: number;
  IsNewSale: number;
  HasNewChanges: number;
  IsChangeProcessed: number;
  VerificationComments: string;
  ChangeLogJSON: any;
  ApplicationID: any;
  SharesSoldPercentage: number;
  NetScheduledIncome: number;
  TransactionOriginationTypeName: string;
  TransactionOriginationTypeID: number;
  SalePricePerLotSizeSF: any;
  SalePricePerLotSizeSM: any;
  AddressText: any;
  Yield: number;
  NetOperatingIncome: number;
  GrossLeaseActualIncome: number;
  NetLeaseActualIncome: number;
  OutgoingsExpensesPerSF: number;
  IsDeedOrSaleDateConfirmed: any;
  VolumeNumber: number;
  Folio: string;
  SaleMethodID: number;
  IncomeClassID: number;
  LeaseIncomeClassID: number;
  Income: number;
  LeaseIncome: number;
  LeaseIncomeTypeID: number;
  YieldSourceID: number;
  YieldTypeID: number;
  YieldCashflowTypeID: number;
  BuyerPendingConfirmation: number;
  SellerPendingConfirmation: number;
  Address: string;
  TransferType: string;
  GstPayable: number;
  StateAbbr: string;
  ZipCode: string;
  PropertyUseTypeName: string;
  IsRuleOverridden = false;
  IsPortfolioSale: number;
}
