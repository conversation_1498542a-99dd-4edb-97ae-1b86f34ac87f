export class PropertySearchCriteria {
    public PropertyType: string;
    public ListingType: string;
    public CountryId: number;
    public CityId: any;
    public NeighbourhoodId: number;
    public ZipCode: string;
    public PropertyName: string;
    public StreetMin: string;
    public StreetMax: string;
    public StreetName: string;
    public StartingIndex: number;
    public OffsetValue: number;
    public SortParam: string;
    public SortDirection: string;
    public LoginEntityID: number;
    public SortBy: string = null;
    public PageNo: number;
    public PageSize: number;
    public specificUseArray: any;
    public researchTypeArray: any;
    public State: any = null;
    public StateID: any = null;
    public IsSkipped: boolean;
    public ExcludeHidden: boolean;
    public ResearchStatusIDs: any;
    public StrataTypeArray: any;
    public StrataTypeIDs: any = null;
    public NotStrata: boolean = false;
    public IncludeStrataPropertyForMaster: any;
    public StrataFilter: any;
    public FrontEndStartingIndex: number;
    public FrontEndOffsetValue: any;
    public ResearcherIDs: string;
    public PersonIDArray: any = [];
    public AuditStatusArray: any = [];
    public AuditStatus: string;
    public PropertyId: string;
    public IsNotReviewed: number = null;
    public LastReviewedDateMin: any = null;
    public LastReviewedDateMax: any = null;
    public LastReviewedDateMinFormatted: any = null;
    public LastReviewedDateMaxFormatted: any = null;
    public HasNoExistingParcelInTileLayer: number = null;
    public HasNoBuildingFootprints: number = null;  
}
