import { Address } from '../models/Address'

export class Parcel {
    ParcelID: number;
    ParcelPolyID: number;
    ParcelNumber: string;
    BuildingSize: any;
    LotSize: any;
    ParcelAddress: Address;
    ParcelOwner: string;
    OwnerAdress: Address;

    AddressStreetName: string;
    CRE_PropertyID: number;
    CityName: string;
    ParcelShape: string;
    PropertyName: string;
    PropertyUse: number;
    ShapeID: number;
    State: string;
    StreetNumberMax: number;
    StreetNumberMin: number;
    Zipcode: number;
    Area: number;
    latitude: number;
    longitude: number;
    CD_PLY_PID: string;
    CountryCode:string;
    RowID:number;

    constructor() {
        this.ParcelAddress = new Address();
        this.OwnerAdress = new Address();
    }
}
