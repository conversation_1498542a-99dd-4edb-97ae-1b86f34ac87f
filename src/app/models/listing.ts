export class Listing{

    VSTListingID:number;
    PropertyID:number;
    LoginEntityID:number;
    
    HasForLeaseListing:boolean = false;
    LeasingCompany:string = null;
    LeasingAgent1:string = null;
    LeasingAgent1Phone:string = null;
    LeasingAgent1Email:string = null;;
    LeasingAgent2:string = null;
    LeasingAgent2Phone:string = null;
    LeasingAgent2Email:string = null;

    SecondLeasingCompany:string = null;
    SecondLeasingAgent1:string = null;
    SecondLeasingAgent1Phone:string = null;
    SecondLeasingAgent1Email:string = null;;
    SecondLeasingAgent2:string = null;
    SecondLeasingAgent2Phone:string = null;
    SecondLeasingAgent2Email:string = null;


    HasForSaleListing:boolean = false;
    SellingCompany:string = null;
    SellingAgent1:string = null;
    SellingAgent1Phone:string = null;
    SellingAgent1Email:string = null;
    SellingAgent2:string = null;
    SellingAgent2Phone:string = null;
    SellingAgent2Email:string = null;

    SecondSellingCompany:string = null;
    SecondSellingAgent1:string = null;
    SecondSellingAgent1Phone:string = null;
    SecondSellingAgent1Email:string = null;
    SecondSellingAgent2:string = null;
    SecondSellingAgent2Phone:string = null;
    SecondSellingAgent2Email:string = null;
    
    
    HasSubleaseListing:boolean = false;
    SubleaseCompany:string = null;
    SubleaseAgent1:string = null;
    SubleaseAgent1Phone:string = null;
    SubleaseAgent1Email:string = null;
    SubleaseAgent2:string = null;
    SubleaseAgent2Phone:string = null;
    SubleaseAgent2Email:string = null;
}