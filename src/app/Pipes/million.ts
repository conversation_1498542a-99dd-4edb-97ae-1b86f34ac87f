import { Pipe, PipeTransform } from '@angular/core';
import { DecimalPipe } from '@angular/common';



// tslint:disable-next-line:use-pipe-transform-interface
@Pipe({
  name: 'million'
})
export class MillionPipe extends DecimalPipe {
  transform(Num: string, args: string): any {

if (Num.length > 0 || Number(Num)  > 0)
// tslint:disable-next-line:one-line
{
    Num += '';
Num = Num.replace(',', ''); Num = Num.replace(',', ''); Num = Num.replace(',', '');
Num = Num.replace(',', ''); Num = Num.replace(',', ''); Num = Num.replace(',', '');
const x = Num.split('.');
let x1 = x[0];
const x2 = x.length > 1 ? '.' + x[1] : '';
const rgx = /(\d+)(\d{3})/;
// tslint:disable-next-line:curly
while (rgx.test(x1))
    x1 = x1.replace(rgx, '$1' + ',' + '$2');
return x1 + x2;

}

  }
}
