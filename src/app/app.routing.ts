import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginComponent } from './pages/login/login.component';
import { ExpressMapsearchComponent } from "./pages/express-mapsearch/express-mapsearchcomponent";
import { PropertySearchComponent } from './pages/property-search/property-search.component';
import {AuthComponent} from './auth.component'
export const routes: Routes = [
    {
        path: '',
        redirectTo: 'search',
        pathMatch: 'full'
    },
    {
        path: 'login',
        component: LoginComponent,
        data: {
            title: 'Login'
        }
    },
    { 
        path: 'login/:sourceApplicationId/:token/:propertyId', 
        component: AuthComponent,
        data: {
            title: 'SSO Login from ERC', 
            preload: true
        }
    },
    {
        path: 'login/:sourceApplicationId/:token',
        component: AuthComponent,
        data: {
            title: 'SSO Login from ERC'
        }
    },
    {
        path: 'pages/login/:sourceApplicationId/:token',
        component: AuthComponent,
        data: {
            title: 'SSO Login'
        }
    },
    {
        path: 'expressmapsearch',
        component: ExpressMapsearchComponent,
        data: {
            title: 'Express Map Search'
        }
    },
    {
        path: 'search',
        component: PropertySearchComponent,
        data: {
            title: 'Search Form'
        }
    }
];

@NgModule({
    imports: [RouterModule.forRoot(routes, { useHash: true, relativeLinkResolution: 'legacy' })],
    exports: [RouterModule]
})
export class AppRoutingModule { 
constructor(){
}
}
