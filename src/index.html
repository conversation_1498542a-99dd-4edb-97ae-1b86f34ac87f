<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Arealytics Virtual Site Tool</title>
  <base href="./">

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <!-- <script src="assets/js/jquery.3.5.1.min.js"></script>-->
  <script src="assets/js/popper.min.js"></script>
  <!-- <script src="assets/js/bootstrap.min.js"></script>  -->

  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"></script>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script> -->


  <!-- <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.9/css/all.css" integrity="sha384-5SOiIsAziJl6AWe0HWRKTXlfcSHKmYV4RBF18PPJ173Kzn7jzMyFuTtk8JA7QQG1" crossorigin="anonymous"> -->
  <!-- <script src="./config.js"></script> -->
  <!-- <script src="https://maps.googleapis.com/maps/api/js?libraries=geometry,drawing,places&key=AIzaSyD7FVSBhfAXavkv3PbsQHGWPkq7ggj0gyI"></script> -->

    <script src="https://greggman.github.io/webgl-helpers/webgl-force-preservedrawingbuffer.js"></script>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <!-- Font Awesome (External) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css">
    <!-- Font Awesome (External Version 4.7.0) -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Google+Sans+Text:400&text=%E2%86%90%E2%86%92%E2%86%91%E2%86%93&lang=en">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans:400,500,700|Google+Sans+Text:400&lang=en">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">
  <script>
    var global = global || window;
  </script>
  <script id="embedded-jira" src="https://jsd-widget.atlassian.com/assets/embed.js" async="" data-jsd-embedded=""
    data-key="a64c24ac-7494-48d8-9e05-a51adcee578e" data-base-url="https://jsd-widget.atlassian.com"></script>
</head>

<body>
  <app-root></app-root>
</body>

</html>
