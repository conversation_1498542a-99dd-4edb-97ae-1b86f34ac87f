


/* 7.13 Page - Login & Register V3 (Added in V1.7) */

.login.login-with-news-feed,
.register.register-with-news-feed {
	width: 100%;
	margin: 0;
	padding: 0;
	top: 0;
}
.login.login-with-news-feed .news-feed,
.register.register-with-news-feed .news-feed {
	position: fixed;
	left: 0;
	right: 500px;
	top: 0;
	bottom: 0;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	overflow: hidden;
}
.login.login-with-news-feed .news-image,
.register.register-with-news-feed .news-image {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	top: 0;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
}
.login.login-with-news-feed .news-image img,
.register.register-with-news-feed .news-image img {
	position: absolute;
	right: 0;
	top: 0;
	left: 0;
	bottom: 0;
	max-height: 100%;
	min-width: 100%;
	top: -1960px;
	bottom: -1960px;
	left: -1960px;
	right: -1960px;
	margin: auto;
}
.login.login-with-news-feed .news-caption,
.register.register-with-news-feed .news-caption {
	color: rgba(255, 255, 255, 0.75);
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 60px 60px 30px;
	font-size: 18px;
	z-index: 20;
	font-weight: 300;
	background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%);
	background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%,rgba(0,0,0,1) 100%);
	background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,1) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#000000',GradientType=0 );
}
.login.login-with-news-feed .news-caption .caption-title,
.register.register-with-news-feed .news-caption .caption-title {
	color: #fff;
	font-weight: 300;
	font-size: 36px;
}
.login.login-with-news-feed .right-content,
.register.register-with-news-feed .right-content {
	float: right;
	width: 500px;
}
.login.login-with-news-feed .login-content,
.register.register-with-news-feed .register-content {
	width: auto;
	padding: 30px 60px;
}
.register.register-with-news-feed .register-content {
	padding: 20px 60px 30px;
}
.login.login-with-news-feed .login-header,
.register.register-with-news-feed .register-header {
	position: relative;
	top: 0;
	margin: 0;
	left: 0;
	right: 0;
	padding: 100px 60px 0;
}
.register.register-with-news-feed .register-header {
	padding: 60px 60px 0;
	font-size: 32px;
}
.register.register-with-news-feed .register-header small {
	color: #707478;
	display: block;
	font-size: 14px;
	margin-top: 10px;
	line-height: 20px;
}
.login.login-with-news-feed .login-header .brand {
	color: #242a30;
}
.login.login-with-news-feed .login-header .brand small {
	color: #707478;
}
.login.login-with-news-feed .login-header .icon {
	top: 98px;
	right: 10px;
}
.login.login-with-news-feed .login-content, .register.register-with-news-feed .register-content {
    width: auto;
    padding: 30px 60px;
}
.upload-scroll .modal-body {
	height: 550px;
	overflow-y: scroll;
    overflow-x: hidden;
}
@media (max-width: 768px)
{
.login.login-with-news-feed .right-content, .register.register-with-news-feed .right-content {
    float: none !important;
    width: auto !important;
}
}
@media (max-width: 1024px)
{
.login.login-with-news-feed .right-content, .register.register-with-news-feed .right-content {
		width: 360px;	
	}
.login.login-with-news-feed .login-content, .login.login-with-news-feed .login-header, .register.register-with-news-feed .register-content, .register.register-with-news-feed .register-header {
    padding-left: 45px;
    padding-right: 45px;
	}
}
@media (max-width: 480px)
{
.login.login-with-news-feed .login-content, .register.register-with-news-feed .register-content {
    padding: 20px 40px  !important;
}
}

.bounce {

	animation: bounce 2s infinite;

	-webkit-animation: bounce 2s infinite;

	-moz-animation: bounce 2s infinite;

	-o-animation: bounce 2s infinite;

}

@-webkit-keyframes bounce {
	0%,

	20%,

	50%,

	80%,

	100% {

		-webkit-transform: translateY(0);

	}


	40% {

		-webkit-transform: translateY(-30px);
	}

	60% {

		-webkit-transform: translateY(-15px);

	}
}

@-moz-keyframes bounce {
	0%,

	20%,

	50%,

	80%,

	100% {

		-moz-transform: translateY(0);
	}

	40% {
		-moz-transform: translateY(-30px);
	}

	60% {
		-moz-transform: translateY(-15px);
	}
}

@-o-keyframes bounce {

	0%,

	20%,

	50%,

	80%,

	100% {

		-o-transform: translateY(0);
	}

	40% {

		-o-transform: translateY(-30px);
	}

	60% {

		-o-transform: translateY(-15px);

	}

}

@keyframes bounce {
	0%,

	20%,

	50%,

	80%,

	100% {

		transform: translateY(0);
	}

	40% {
		transform: translateY(-30px);
	}

	60% {
		transform: translateY(-15px);
	}
}


.drop {
	animation: drop 0.6s ease-out forwards;
	-webkit-animation: drop 0.6s ease-out forwards;
	-moz-animation: drop 0.6s ease-out forwards;
	-o-animation: drop 0.6s ease-out forwards;
}

@-webkit-keyframes drop {
	0% {
		-webkit-transform: translateY(-100px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateY(0);
		opacity: 1;
	}
}

@-moz-keyframes drop {
	0% {
		-moz-transform: translateY(-100px);
		opacity: 0;
	}
	100% {
		-moz-transform: translateY(0);
		opacity: 1;
	}
}

@-o-keyframes drop {
	0% {
		-o-transform: translateY(-100px);
		opacity: 0;
	}
	100% {
		-o-transform: translateY(0);
		opacity: 1;
	}
}

@keyframes drop {
	0% {
		transform: translateY(-100px);
		opacity: 0;
	}
	100% {
		transform: translateY(0);
		opacity: 1;
	}
}
