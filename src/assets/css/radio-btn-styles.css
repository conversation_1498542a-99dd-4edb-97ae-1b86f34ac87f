.type-btn {
  background: var(--primary-white-color);
  min-width: 37px;
  height: 39px;
  font-weight: bold;
  border: 1px solid var(--primary-blue-color);
  color: var(--primary-blue-color);
}

.type-btn.blue {
  border: 1px solid var(--primary-blue-color);
  color: var(--primary-blue-color);
}

.blueCheckedLabel {
  background: var(--primary-blue-color);
  border: 1px solid var(--primary-blue-color);
  color: var(--primary-white) !important;
}

.type-btn.red {
  border: 1px solid var(--red-color);
  color: var(--red-color);
}

.redCheckedLabel {
  background: var(--red-color);
  border: 1px solid var(--red-color);
  color: var(--primary-white) !important;
}

.type-btn.green {
  border: 1px solid var(--green-color);
  color: var(--green-color);
}

.greenCheckedLabel {
  background: var(--green-color);
  border: 1px solid var(--green-color);
  color: var(--primary-white) !important;
}

.type-btn.orange {
  border: 1px solid var(--primary-orange);
  color: var(--primary-orange);
}

.orangeCheckedLabel {
  background: var(--primary-orange);
  border: 1px solid var(--primary-orange);
  color: var(--primary-white) !important;
}

.type-btn.magenta {
  border: 1px solid var(--magenta-color);
  color: var(--magenta-color);
}

.magentaCheckedLabel {
  background: var(--magenta-color);
  border: 1px solid var(--magenta-color);
  color: var(--primary-white) !important;
}

.type-btn.brown {
  border: 1px solid var(--brown-color);
  color: var(--brown-color);
}

.brownCheckedLabel {
  background: var(--brown-color);
  border: 1px solid var(--brown-color);
  color: var(--primary-white) !important;
}

.ashCheckedLabel {
  background: var(--primary-light-ash);
  border: 1px solid var(--primary-light-ash);
  color: var(--primary-white) !important;
}

.type-btn.ash {
  border: 1px solid var(--primary-light-ash);
  color: var(--primary-light-ash);
}
.floor-radio {
  margin-right: 5px;
}
.center-aligned-radio-button {
  padding: 6px !important;
}
.radio-toolbar:not(:first-child) {
  margin-left: 5px;
}

.radio-toolbar input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}
.radio-toolbar label {
  display: inline-block;
  cursor: pointer;
}

.radio-toolbar-custom input[type=radio] {
  transform: scale(1.5);
}
