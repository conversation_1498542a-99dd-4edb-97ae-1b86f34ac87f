/* You can add global styles to this file, and also import other style files */

@import "~@angular/material/prebuilt-themes/indigo-pink.css";
@import "~@ng-select/ng-select/themes/default.theme.css";
@import 'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700';
@import 'https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css';
@import 'https://use.fontawesome.com/releases/v5.3.1/css/all.css';
@import '~font-awesome/css/font-awesome.min.css';
@import '~@fortawesome/fontawesome-free/css/all.min.css';
@import 'https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css';
@import "assets/css/style.css";
@import "~primeng/resources/primeng.min.css";
@import "~bootstrap/dist/css/bootstrap.min.css";
@import "~primeng/resources/themes/saga-blue/theme.css";  /* or another theme */
@import "~primeng/resources/primeng.min.css";
@import "~primeicons/primeicons.css";

:root {
    --primary-color: #50bf3f;
    --secondary-color: #25591d;
    --headerText-color: #25591d;
    --tableHead-color: #25591D;
    --shaded-secondary-color: #9dd195;
    --property-block-bgColor: #ffffff;
    --property-block-color: #50bf3f;
    --property-list-header-color: #50bf3f66;
    --primary-orange: #ea7727;
    --primary-blue: #50a7f3;
    --cardInfoBgColor: #2d353c;
    --cardTitleFontColor: #242a30;
    --cardInfoFontColor: #fff;
    --red-color: #ea483f;
    --green-color: #61b922;
    --orange-color: #d08931;
    --yellow-color: #f3dc12;
    --magenta-color: rgba(216, 33, 134, 0.973);
    --brown-color: #d08931;
    --color-ash: #ccc;
    --color-darkAsh: #dadada;
    --primary-ash: #455463;
    --primary-darkAsh: #242a30;
    --primary-light-ash: #6a7179;
    --primary-white: #fff;
    --btn-primary-blue: #191970;
    --btn-primary-light-blue: #50a7f3;
    --primary-blue-color: #4180c3;
    --primary-brightBlue: #1e4b7b;
    --table-color-lightGrey: #d9e0e7;
    --primary-lightColor: #e8e8e8;
    --primary-body-lightBlue: #edf6ff;
    --primary-white: #fff;
    --light-blue: #92bdea;
    --card-overlay: #4180c3cf;
    --hover-light-blue: #92bdea2a;
    --btn-disabled-color: #bab5b5;
}

/* :root { --Office-color: #14558f;
    --Industrial-color:  #ea483f;
    --Retail-color: #d08931;
    --Multi-Family Housing-color
    --primary-color-hover: #216fb3;
     --bg-dark-light: rgba(0, 0, 0, 0.7);
      --primary-white-light: rgba(255, 255, 255, 0.9);
     
      --default: #000;
      --light-bg: #EFEFEF;
      --primary-white: #fff;
      --light-ash: #ccc;
      --primary-grey: #222524;
      --anchor-color: #007bff;} */
.error-text {
  color: red;
}

button:focus{
    outline: 0 !important;
}
a {
    cursor: pointer;
}

.app-header.navbar .navbar-brand {
    width: 200px;
}

.main .container-fluid {
    padding: 30px;
}
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    text-align: left;
}

.mandatory {
    color: #FF0000;
}

.login-bg {
    background-image: url(./assets/images/header-bg.png);
    background-color: white;
    background-repeat: no-repeat;
    background-position: center center;
    background-attachment: fixed;
    height: 100%;
}

.hidden {
    display: none;
}

.login-logo {
    padding-top: 10px;
    padding-top: 0px;
}

.login-log {
    background-color: rgba(0, 0, 0, 0.4588235294117647);
    text-align: center;
    width: 15%;
    padding: 10px 0;
}

.no-padding {
    padding-left: 0px;
    padding-right: 0px;
}

.logo-color {
    color: #28557e;
}

.margin-top-20 {
    margin-top: 20px;
}

.text-muted {
    color: #536c79 !important;
}

.app, app-dashboard, app-root {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 100vh;
}

/* .btn-warn {
    color: #fff;
    background-color: #F18A00;
    border-color: #F18A00;
}
.btn-warn:hover {
    color: #fff;
    background-color: #f9a22e;
    border-color: #f9a22e;
} */

.property-detail .form-group {
    margin-bottom: 0px;
}

.form-group {
    margin-bottom: 10px;
}

.box-bg {
    background-color: #efeff1;
    padding: 10px;
}

.no-pad-left {
    padding-left: 0px;
}

.no-pad-right {
    padding-right: 0px;
}

.right {
    float: right;
}

.mar-left3 {
    margin-left: 3px;
}

/* TODO: Move this to map specific style */

.map-main {
    padding: 15px;
    padding-top: 0;
    /* background: #b1b1b1; */
}

.header-container {
    background: #fff;
    padding-top: 0px;
    padding-bottom: 10px;
    margin-bottom: 10px;
    color: var(--primary-blue-color);
    border-bottom: 1px solid #e8e7eb;
    display: flex;
    align-items: center;
}

.searchBtnActions {
    color: var(--primary-blue-color);
    background: none;
    padding: 5px 10px;
    width: 100%;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: left;
    margin-bottom: 0;
    border: 0px;
    font-size: 1.1rem;
}

.expandBox {
    border: 2px dotted var(--light-blue);
    display: block;
    height: auto;
    margin: 0px;
    /* padding: 0; */
    font-size: 13px;
    margin-bottom: 10px;
}

.selectMoreToggle i, .selectExpandToggle i {
    margin-right: 10px;
}

.selectExpandToggle:after {
    font-family: "Font Awesome 5 Free";
    content: "\f105";
    float: right;
    font-size: 18px;
    color: #4180c3;
    position: absolute;
    right: 25px;
}

.selectExpandToggle[aria-expanded=true]:after {
    font-family: "Font Awesome 5 Free";
    content: "\f107";
    color: #4180c3;
}

.selectMoreToggle:after {
    font-family: "Font Awesome 5 Free";
    content: "\f067";
    float: right;
    font-size: 18px;
    color: #4180c3;
    position: absolute;
    right: 25px;
}

.selectMoreToggle[aria-expanded=true]:after {
    font-family: "Font Awesome 5 Free";
    content: "\f068";
    color: #4180c3;
}

.mat-tab-header, .mat-tab-nav-bar {
    border-bottom: 1px solid rgba(0, 0, 0, .12);
    margin-top: 10px;
}

.header-container h4 {
    font-size: 1.3rem;
}

.map-main .mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination {
    display: none !important;
}

.map-main .mat-tab-label {
    opacity: unset;
    background-color: var(--primary-white);
    font-size: 16px;
    color: var(--primary-blue-color);
    border: 1px dotted var(--primary-blue-color);
    height: 38px;
    min-width: 150px;
}

.map-main .mat-tab-label.mat-tab-label-active {
    background-color: var(--primary-blue-color);
    color: var(--primary-white);
}

.map-main .mat-tab-label:hover {
    background-color: var(--btn-primary-light-blue) !important;
    color: var(--primary-white);
}

.mat-tab-group.mat-primary .mat-tab-label:not(.mat-tab-disabled):focus {
    background-color: var(--primary-blue-color) !important;
    color: var(--primary-white) !important;
}

.error-field {
    border: 1px solid #ff0066;
    border-radius: 6px;
}

.map-main .mat-tab-label:hover {
    background-color: var(--primary-blue-color);
}

.map-main hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid #b7b5b5;
}

.mb-3, .my-3 {
    margin-bottom: 1rem !important;
}

.input-group {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%;
}

.mb-3, .my-3 {
    margin-bottom: 1rem!important;
}

.input-group {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%;
}

.input-group-addon {
    border: 1px solid;
    border-left: 0;
    border-color: #d6dbe0;
    background-color: #ecf2f9;
    border-radius: 0.2rem;
    height: 100%;
    padding-top: 0.3rem;
}

.prop-validator {
    position: absolute;
    right: 0px;
    top: -35px;
    z-index: 100;
    color: #fff !important;
    border-radius: 5px;
    padding: 1px 3px;
    font-size: 13px;
}

.top-area .btn {
    background: var(--primary-blue-color);
    border: 1px solid var(--primary-blue-color);
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.9;
    min-width: 85px;
}

.btn {
    padding: 0 10px !important;
}

.brder-btm {
    border-bottom: 1px solid rgb(240, 240, 240) !important;
}

.file-input-btn {
    border: 1px dotted var(--primary-blue-color) !important;
    color: var(--primary-blue-color);
    padding: 0 15px !important;
    background: none !important;
}

.file-input-btn:hover {
    background: #d9f1fa !important;
    color: var(--primary-blue-color) !important;
}

.top-area .btn.btn-primary {
    background: var(--primary-blue-color);
    border: 1px solid var(--primary-blue-color);
}

.login-btn {
    background: var(--primary-blue-color);
    border: 1px solid var(--primary-blue-color);
}
.switch-medium{
    width: 40px !important;
    height: 22px !important;
}
.switch-medium small {
    width: 20px !important;
    height: 21px !important;
}
.form-control-label {
    font-size: 15px;
}

.mat-tab-body-content{
    overflow: hidden !important;
}

.prop-validator:after {
    content: "";
    border-left: solid 10px transparent;
    border-right: solid 10px transparent;
    position: absolute;
    left: 10px;
    bottom: -9px;
}

.prop-validator.error {
    background: red;
    /* box-shadow: -2px 1px 5px #921e1e; */
}

#map-canvas {
    height: calc(100vh - 50px) !important;
}

.thumb-style {
    border: 1px solid #ccc;
    margin-right: 10px;
}

#previews label {
    font-weight: bold;
    font-size: 85%;
}

.cursor-pointer{
    cursor: pointer;
}

.dataKey{
 font-size: 0.8rem;
}

.dataLabelValue{
    font-size: 0.9rem;
    font-weight: 500;
}

p.name {
    word-wrap: break-word;
}

ul.size-info {
    display: -webkit-box;
    list-style: none;
    padding: 0;
}

ul.size-info li {
    flex: 3;
}

ul.size-info li h3 {
    color: var(--primary-brightBlue);
    font-size: 20px;
}

h4.set-as {
    font-size: 16px;
    font-weight: normal;
}

#previews label.radio-inline {
    margin-right: 10px;
}

#previews h2 {
    margin-bottom: 10px;
    margin-top: 15px;
    font-size: 22px;
    color: var(--primary-brightBlue);
    border-bottom: 2px solid var(--primary-brightBlue);
    display: block;
    width: 100%;
    padding-bottom: 10px;
}

.progress {
    background: none;
    border: 0;
    background: #b1b3da;
    height: 10px;
}

.intrepid-modal .modal-header {
    background: var(--primary-brightBlue);
    color: #fff;
    padding: 5px 15px !important;
}

#previews label.radio-inline input {
    margin-right: 10px;
}

#previews h2 {
    margin-bottom: 10px;
    margin-top: 10px;
}

/*.input-group-addon:not(:last-child) {
    border-right: 0;
}
.input-group-addon:not(:last-child) {
    border-right: 0;
}
.input-group .form-control:not(:last-child), .input-group-addon:not(:last-child), .input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.btn, .input-group-btn:not(:first-child)>.btn:not(:last-child):not(.dropdown-toggle), .input-group-btn:not(:last-child)>.btn, .input-group-btn:not(:last-child)>.btn-group>.btn, .input-group-btn:not(:last-child)>.dropdown-toggle {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.input-group-addon, .input-group-btn {
    min-width: 40px;
    white-space: nowrap;
    vertical-align: middle;
}
.input-group-addon {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #3e515b;
    text-align: center;
    background-color: #f0f3f5;
    border: 1px solid #c2cfd6;
}
.input-group-addon, .input-group-btn {
    white-space: nowrap;
}
.input-group-addon, .input-group-btn, .input-group .form-control {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.input-group-addon {
    padding: .375rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    text-align: center;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: .25rem;
}
.input-group-addon, .input-group-btn {
    white-space: nowrap;
}
.input-group .form-control, .input-group-addon, .input-group-btn {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
}

.icon-user, .icon-people, .icon-user-female, .icon-user-follow, .icon-user-following, .icon-user-unfollow, .icon-login, .icon-logout, .icon-emotsmile, .icon-phone, .icon-call-end, .icon-call-in, .icon-call-out, .icon-map, .icon-location-pin, .icon-direction, .icon-directions, .icon-compass, .icon-layers, .icon-menu, .icon-list, .icon-options-vertical, .icon-options, .icon-arrow-down, .icon-arrow-left, .icon-arrow-right, .icon-arrow-up, .icon-arrow-up-circle, .icon-arrow-left-circle, .icon-arrow-right-circle, .icon-arrow-down-circle, .icon-check, .icon-clock, .icon-plus, .icon-minus, .icon-close, .icon-event, .icon-exclamation, .icon-organization, .icon-trophy, .icon-screen-smartphone, .icon-screen-desktop, .icon-plane, .icon-notebook, .icon-mustache, .icon-mouse, .icon-magnet, .icon-energy, .icon-disc, .icon-cursor, .icon-cursor-move, .icon-crop, .icon-chemistry, .icon-speedometer, .icon-shield, .icon-screen-tablet, .icon-magic-wand, .icon-hourglass, .icon-graduation, .icon-ghost, .icon-game-controller, .icon-fire, .icon-eyeglass, .icon-envelope-open, .icon-envelope-letter, .icon-bell, .icon-badge, .icon-anchor, .icon-wallet, .icon-vector, .icon-speech, .icon-puzzle, .icon-printer, .icon-present, .icon-playlist, .icon-pin, .icon-picture, .icon-handbag, .icon-globe-alt, .icon-globe, .icon-folder-alt, .icon-folder, .icon-film, .icon-feed, .icon-drop, .icon-drawer, .icon-docs, .icon-doc, .icon-diamond, .icon-cup, .icon-calculator, .icon-bubbles, .icon-briefcase, .icon-book-open, .icon-basket-loaded, .icon-basket, .icon-bag, .icon-action-undo, .icon-action-redo, .icon-wrench, .icon-umbrella, .icon-trash, .icon-tag, .icon-support, .icon-frame, .icon-size-fullscreen, .icon-size-actual, .icon-shuffle, .icon-share-alt, .icon-share, .icon-rocket, .icon-question, .icon-pie-chart, .icon-pencil, .icon-note, .icon-loop, .icon-home, .icon-grid, .icon-graph, .icon-microphone, .icon-music-tone-alt, .icon-music-tone, .icon-earphones-alt, .icon-earphones, .icon-equalizer, .icon-like, .icon-dislike, .icon-control-start, .icon-control-rewind, .icon-control-play, .icon-control-pause, .icon-control-forward, .icon-control-end, .icon-volume-1, .icon-volume-2, .icon-volume-off, .icon-calendar, .icon-bulb, .icon-chart, .icon-ban, .icon-bubble, .icon-camrecorder, .icon-camera, .icon-cloud-download, .icon-cloud-upload, .icon-envelope, .icon-eye, .icon-flag, .icon-heart, .icon-info, .icon-key, .icon-link, .icon-lock, .icon-lock-open, .icon-magnifier, .icon-magnifier-add, .icon-magnifier-remove, .icon-paper-clip, .icon-paper-plane, .icon-power, .icon-refresh, .icon-reload, .icon-settings, .icon-star, .icon-symbol-female, .icon-symbol-male, .icon-target, .icon-credit-card, .icon-paypal, .icon-social-tumblr, .icon-social-twitter, .icon-social-facebook, .icon-social-instagram, .icon-social-linkedin, .icon-social-pinterest, .icon-social-github, .icon-social-google, .icon-social-reddit, .icon-social-skype, .icon-social-dribbble, .icon-social-behance, .icon-social-foursqare, .icon-social-soundcloud, .icon-social-spotify, .icon-social-stumbleupon, .icon-social-youtube, .icon-social-dropbox, .icon-social-vkontakte, .icon-social-steam {
    font-family: 'simple-line-icons';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}*/

.custom-absolute-alert {
    position: fixed;
    left: 34%;
    width: auto;
    min-width: 30%;
    max-width: 75%;
    text-align: center;
    margin: auto;
    top: 4rem;
    box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.3);
    list-style-type: none;
    padding: 5px 15px;
    border: 1px solid rgba(0, 0, 0, 0);
    border-radius: 4px;
    z-index: 99999;
}

.prop-validator {
    position: absolute;
    right: 0px;
    top: -25px;
    z-index: 100;
    color: #fff !important;
    border-radius: 5px;
    padding: 1px 5px;
    font-size: 11px;
    display: inline-block;
    width: auto;
    text-align: center;
    min-width: 100px;
    left: 97px;
}

.img-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* /////////////////////////////// Login Design/////////////////////////////// */

.labelNameClass {
    color: rgb(134, 134, 134);
    margin-bottom: 10px;
    font-weight: 600
}

.login.login-with-news-feed, .register.register-with-news-feed {
    /* // width: 100%;
           // margin: 0;
           // padding: 0;
           // top: 0; */
}

.login {
    /* // margin: -168px 0 0;
           // position: absolute;
           // left: 0;
           // right: 0;
           // top: 50%; */
}

.login.login-with-news-feed .news-feed, .register.register-with-news-feed .news-feed {
    position: absolute;
    height: 100%;
    /* // left: 0;
           // right: 500px;
           // top: 0;
           // bottom: 0; */
    float: left;
    /* // -webkit-transform: translateZ(0);
           // transform: translateZ(0);
           // overflow: hidden; */
    width: calc(100% - 500px);
}

.login.login-with-news-feed .news-image, .register.register-with-news-feed .news-image {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    height: 100%;
}

.login.login-with-news-feed .news-caption, .register.register-with-news-feed .news-caption {
    color: rgba(255, 255, 255, .75);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 60px 60px 30px;
    font-size: 18px;
    z-index: 20;
    font-weight: 300;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, 1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#000000', GradientType=0);
}

.login.login-with-news-feed .right-content, .register.register-with-news-feed .right-content {
    float: right;
    max-width: 500px;
}

.login.login-with-news-feed .login-header, .register.register-with-news-feed .register-header {
    position: relative;
    top: 0;
    margin: 0;
    left: 0;
    right: 0;
    padding: 100px 60px 0;
    font-weight: 300;
}

.loginMsgBox {
    display: block;
    position: relative;
    margin: 0 30px;
    text-align: center;
}

.login .login-header .brand {
    padding: 0;
    font-size: 28px;
    color: #242a30;
}

#loginMessage {
    position: absolute;
    width: 100%;
    top: -7px;
}

.login.login-with-news-feed .login-content, .register.register-with-news-feed .register-content {
    width: auto;
    padding: 30px 60px;
    color: #999;
    margin: 0 auto;
}

.m-b-15 {
    margin-bottom: 15px !important;
}

.login.login-with-news-feed .news-caption .caption-title, .register.register-with-news-feed .news-caption .caption-title {
    color: #fff;
    font-weight: 300;
    font-size: 36px;
}

.gm-style .gm-style-iw-c {
    border-radius: 0;
    padding: 0;
}

.gm-style .gm-style-iw-t::after {
    background: none;
    box-shadow: none;
}

.gm-style-iw {
    padding: 0;
}

.gm-style .gm-style-iw-d {
    box-sizing: border-box;
    overflow: auto !important;
}

.gm-style .gm-style-iw-t {
    padding-bottom: 10px;
}

.iwBox-img-wrap {
    width: 100px;
    height: 100px;
    cursor: pointer;
    display: inline-flex;
    float: left;
    display: flex;
    vertical-align: middle;
    background: url("https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/ajax-loader.gif") no-repeat center center, rgb(247, 252, 245);
}

.iwTextBox {
    display: inline-block;
    position: relative;
    list-style: none;
    left: 0;
    padding: 0px;
    margin: 0;
    background: #fff;
    min-height: 50px;
    margin: 5px;
    margin-bottom: 0;
    margin-top: 2px;
    font-size: 10px;
    float: right;
    width: 247px;
}

.mapiw-text {
    display: block;
    color: #000;
    font-weight: bold;
    font-size: 13px;
}

.gm-ui-hover-effect:focus, .gm-ui-hover-effect:hover {
    outline: none !important;
    border: none !important;
}

.btn-warn.active, .btn-warn.active.focus, .btn-warn.active:focus, .btn-warn.active:hover, .btn-warn:active, .btn-warn:active.focus, .btn-warn:active:focus, .btn-warn:active:hover, .btn-warn:focus, .btn-warn:hover, .btn-warn:not(:disabled):not(.disabled).active, .btn-warn:not(:disabled):not(.disabled):active, .open>.dropdown-toggle.btn-warn, .open>.dropdown-toggle.btn-warn:focus, .open>.dropdown-toggle.btn-warn:hover, .show>.btn-warn.dropdown-toggle {
    background: var(--primary-blue-color)!important;
    border-color: var(--primary-blue-color)!important;
    color: var(--primary-white) !important;
}

.btn-primary-blue, .btn-primary, .btn-warn {
    /* color: var(--primary-white);
    background-color: var(--btn-primary-blue);
    border-color: var(--btn-primary-blue); */
    background-color: var(--primary-white) !important;
    color: var(--primary-blue-color)!important;
    border-color: var(--primary-blue-color)!important;
    padding-left: 10px;
    padding-right: 10px;
}

.btn-primary-blue:hover, .btn-primary:hover {
    background: var(--primary-blue-color)!important;
    border-color: var(--primary-blue-color)!important;
    color: var(--primary-white) !important;
}

.dropdown-item:focus, .dropdown-item:hover {
    color: #16181b !important;
    text-decoration: none;
    background-color: #f8f9fa !important;
}

body .p-datatable-table .p-datatable-thead>tr>th {
    font-weight: 600;
    color: #ffffff;
    background-color: #455463;
    font-size: 13px;
    position: relative;
    padding: 1em .5em;
    border-right: 1px solid #455463;
}
body .p-datatable-table .p-datatable-tbody>tr>td {
    font-size: 13px;
    padding: 1em .5em;
    border-right: 1px solid #ececec;
}
.p-datatable-tbody tr {
    border-bottom: 1px solid #ececec;
}
.p-component-overlay {
    background-color: transparent !important;
}

.tableText td,
.tableText th {
    text-align: left;
}
.tableText td,
.tableText th {
    text-align: left;
}

.error-message {
    color: red;
    margin-top: 4px;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 5rem auto !important;
    }
}
.modal-content {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .2);
}
.close {
    background-color: transparent;
    border: transparent;
    font-size: 20px;;
}
.modal-body {
    padding: 25px;
}

.text-right {
    text-align: right !important;
}
.ng-select {
    font-size: 16px;
}
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}
.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}
.p-progress-spinner-svg {
    width: 50px !important;
    height: 50px !important;
}
.radio-inline {
    font-size: 15px !important;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
