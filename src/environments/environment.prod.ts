import 'zone.js/plugins/zone-error';
export const environment = {
  production: true,

  baseUrl:  'https://api-pro.empiricalcre.com/api/',
  tilesBaseUrl: 'https://tiles.arealytics.com.au/data',
  GoogleMapApiKey: 'AIzaSyA99CTod-W0iO4RqR9SrbifJgziiFB0HTw&v=3.33',

  MediaS3DynamicImageBase: 'https://media.empiricalcre.com',
  MediaS3DynamicImageSize: '/Media/Thumbnail/300x300/',
  MediaS3Path: '/EmpiricalCRE/PROD/Media',
  MediaS3Base: 'https://media.empiricalcre.com',
  MediaS3ThumbnailPath: 'Thumbnail',
  MediaS3ThumbResolution: '300x300',
  MediaDefaultImage: 'NoPhoto.jpg',

  EncryptionKey: 'empiricaluser',  
      
  fusionTableId1: '1IvEweto_KH8tyuR31ec8RoNDOxGt4MukgSKozjB9',
  fusionTableId2: '1gHEE8rslRogFV0Y_KSQ_RJGPo-YRRmEkY0H962Cz',

  MapIconBaseURL:'assets/images/googleMapPins/',
  MapIconNeedsResearch: 'assets/images/googleMapPins/marker_pink.png',
  MapIconBaseResearchComplete: 'assets/images/googleMapPins/marker_yellow.png',
  MapIconFieldResearchComplete:'assets/images/googleMapPins/marker_green.png',
  MapIconHidden:'assets/images/googleMapPins/marker_black.png',
  MapIconNotStarted:'assets/images/googleMapPins/marker_red.png',
  MapMUHIcon:'assets/images/googleMapPins/marker_White.png',
  MapIconExpressComplete: 'assets/images/googleMapPins/marker_orange.png',
  MapIconExpressIncomplete: 'assets/images/googleMapPins/marker_red.png',

  EnableBackgroundMediaUpload: true,
  MasterWorkerExecutionDuration: 5000,
  UploadWorkersFrequency: 20,

  AzureMapProxyURL: 'https://vst.arealytics.com.au/map/tile',
  AzureMapBaseURL: 'https://atlas.microsoft.com/map/tile',
  AzureMapApiKey: '1I463Dq3ZTDwUTdHOQGZmk7DRUQ99OZxVoB35km8mX1yzwLikfCcJQQJ99AJACYeBjF1EniUAAAgAZMPasVA',
  AzureMapApiVersion: '2024-04-01',
  enableAzureMap :true,
  
  // SSO 
  ERCReviewTool: 'https://erc.empiricalcre.com',

  //Jira support desk
  EnableJiraSupportDesk: true,

  //Average Estimation Flag
  EnableAverageEstimationMethod: false
}
