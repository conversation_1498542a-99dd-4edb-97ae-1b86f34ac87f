// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.
import 'zone.js/plugins/zone-error';
export const environment = {
  production: true,

  baseUrl: 'https://api-training.empiricalcre.com/api/',
  tilesBaseUrl: 'https://tiles-dev.arealytics.com.au/data',
  GoogleMapApiKey: 'AIzaSyBKW1Pe5bJ8lUB9oJ5GE9f1hisJD_2qtdM&v=3.33',  

  MediaS3DynamicImageBase: 'https://media.empiricalcre.com',
  MediaS3DynamicImageSize: '/Media/Thumbnail/300x300/',
  MediaS3Path: '/EmpiricalCRE/UAT/Media',
  MediaS3Base: 'https://media.empiricalcre.com',
  MediaS3ThumbnailPath: 'Thumbnail',
  MediaS3ThumbResolution: '300x300',
  MediaDefaultImage: 'NoPhoto.jpg',

  EncryptionKey: 'empiricaluser',
      
  fusionTableId1: '1IvEweto_KH8tyuR31ec8RoNDOxGt4MukgSKozjB9',
  fusionTableId2: '1gHEE8rslRogFV0Y_KSQ_RJGPo-YRRmEkY0H962Cz',

  MapIconBaseURL:'assets/images/googleMapPins/',
  MapIconNeedsResearch: 'assets/images/googleMapPins/marker_pink.png',
  MapIconBaseResearchComplete: 'assets/images/googleMapPins/marker_yellow.png',
  MapIconFieldResearchComplete:'assets/images/googleMapPins/marker_green.png',
  MapIconHidden:'assets/images/googleMapPins/marker_black.png',
  MapIconNotStarted:'assets/images/googleMapPins/marker_red.png',
  MapMUHIcon:'assets/images/googleMapPins/marker_White.png',
  MapIconExpressComplete: 'assets/images/googleMapPins/marker_orange.png',
  MapIconExpressIncomplete: 'assets/images/googleMapPins/marker_red.png',

  EnableBackgroundMediaUpload: true,
  MasterWorkerExecutionDuration: 5000,
  UploadWorkersFrequency: 20,

  AzureMapProxyURL: 'https://vst-training.arealytics.com.au/map/tile',
  AzureMapBaseURL: 'https://atlas.microsoft.com/map/tile',
  AzureMapApiKey: '5T4dia56lOeSZbOsmPqDw495TV5BLJlJpNRdC8U8Wu2uMzL2R47FJQQJ99AIACYeBjF1EniUAAAgAZMPZEVR',
  AzureMapApiVersion: '2024-04-01',
  enableAzureMap :true,

  // SSO 
  ERCReviewTool: 'https://erc-training.empiricalcre.com',

  //Jira support desk
  EnableJiraSupportDesk: false,
  
  //Average Estimation Flag
  EnableAverageEstimationMethod: false
};
