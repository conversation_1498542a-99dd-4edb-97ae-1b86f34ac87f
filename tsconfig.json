{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "allowJs": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "target": "es2015",
    "types": [
      "node"
    ],
    "typeRoots": [
      "node_modules/@types"
    ],
    "lib": [
      "es2018",
      "dom",
      "WebWorker"
    ],
    "skipLibCheck": true,
    "module": "esnext",
    "baseUrl": "./",
    "forceConsistentCasingInFileNames": false,
    "strict": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "noImplicitAny": false,
    "strictPropertyInitialization": false
  },
  "angularCompilerOptions": {
    "preserveWhitespaces": "off"
  },
  "exclude": [
    "test.ts",
    "config.js",
    "node_modules",
    "src/typings/main",
    "src/typings/main.d.ts"
  ],
  "files": [
    "src/main.ts",
    "src/polyfills.ts"
  ],
  "include": [
    "src/**/*.d.ts"
  ],
}
