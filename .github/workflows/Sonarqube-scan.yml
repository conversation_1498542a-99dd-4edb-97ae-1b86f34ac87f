name: Sonarqube scan for vst
on:
  workflow_dispatch:
jobs:
  sonarqube:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: SonarQube Scan
      uses: sonarsource/sonarqube-scan-action@master
      with:
          projectBaseDir: .
          args: >
            -Dsonar.organization=arealytics
            -Dsonar.projectKey=Empirical_Virtual_Site_Tool
            -Dsonar.sources=src,e2e
            -Dsonar.exclusions=src/app/api-client/**
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
