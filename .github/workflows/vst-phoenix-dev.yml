name: VST phoenix dev Deployment

on:
  workflow_dispatch:
    inputs:
      workflow_mode:
        description: 'Select workflow execution mode'
        required: true
        default: 'full_pipeline'
        type: choice
        options:
          - full_pipeline
          - tests_only
          - deploy_only

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    if: ${{ inputs.workflow_mode == 'full_pipeline' || inputs.workflow_mode == 'tests_only' }}
    outputs:
      test-status: ${{ steps.test-result.outputs.status }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: |
          cd ./e2e
          # Clean install with legacy peer deps to resolve dependency conflicts
          npm cache clean --force
          # Install sass instead of node-sass for better compatibility
          npm install --legacy-peer-deps

      - name: Install Playwright Browsers
        run: |
          cd ./e2e
          npx playwright install chromium --with-deps

      - name: Run E2E Tests
        id: run-tests
        run: |
          cd ./e2e
          ENV=dev npx playwright test
        continue-on-error: true

      - name: Generate Playwright HTML Report
        if: always()
        run: |
          cd ./e2e
          npx playwright show-report --output playwright-report
          echo "Playwright report generated at ./e2e/playwright-report"

      - name: Upload Test Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-reports
          path: |
            e2e/test-results/
            e2e/playwright-report/
            reports/
            cypress/screenshots/
            cypress/videos/
            **/*test-report*
            **/*junit*.xml
            **/*allure*
          retention-days: 30

      - name: Deploy Playwright Report to GitHub Pages
        if: always()
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./e2e/playwright-report
          destination_dir: playwright-report
          keep_files: false
          commit_message: Deploy Playwright report for run #${{ github.run_number }}

      - name: Set Test Result Status
        id: test-result
        run: |
          if [ ${{ steps.run-tests.outcome }} == 'success' ]; then
            echo "status=passed" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

      - name: Fail Job if Tests Failed
        if: steps.test-result.outputs.status == 'failed'
        run: |
          echo "E2E tests failed. Deployment will be skipped."
          exit 1

  deploy:
    name: Deploy to VST Dev
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: |
      always() &&
      (
        (inputs.workflow_mode == 'full_pipeline' && needs.e2e-tests.outputs.test-status == 'passed') ||
        inputs.workflow_mode == 'deploy_only'
      )
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '10.24.1'

      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Build project
        run: |
          cd ./
          npm install node-sass --save
          npm install
          npm rebuild node-sass
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c phoenixdev
          cp -rvfap ./src/assets ./dist
          cd ./dist
          zip -r dist.zip ./*
        continue-on-error: true

      - name: Copy files via ssh rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PHOENIX_REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.PHOENIX_REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.PHOENIX_USER }}

      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PHOENIX_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.PHOENIX_HOSTNAME }}
          REMOTE_USER: ${{ secrets.PHOENIX_USER }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
            cd /var/www/vst-phoenix-dev.arealytics.com.au/
            sudo unzip -o -d "/var/www/vst-phoenix-dev.arealytics.com.au/" /home/<USER>/dist.zip
            sudo chown -R ubuntu:ubuntu /var/www/vst-phoenix-dev.arealytics.com.au
            sudo rm -rf /home/<USER>/dist.zip
          '

  notify:
    name: Notify Slack for Deployment
    runs-on: ubuntu-latest
    needs: [deploy, e2e-tests]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          message: |
            *${{ github.repository }} - #${{ github.run_number }}*
            Started by user ${{ github.actor }}
            *Deployment Status:* ${{ needs.deploy.result }}
            *Test Status:* ${{ needs.e2e-tests.outputs.test-status || 'skipped' }}
            *Changes in Current Deployment:* ${{ steps.get-changes.outputs.changes }}
            *Deployment URL:* https://vst-phoenix-dev.arealytics.com.au
            *Playwright Report URL:* https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/playwright-report/
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
