name: VST beta Deployment 
on:
  workflow_dispatch

jobs:
  deploy:
    name: Deploy to vst beta on develop branch
    runs-on: ubuntu-latest
    outputs:
      error_message: ${{ steps.capture_error.outputs.error }}
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - uses: actions/setup-node@v2
        with:
          node-version: 'v10.24.1'
      
      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT       
      
      - name: commands     
        id: build
        run: |
          cd ./ &&
          npm install node-sass --save &&
          npm install &&
          npm rebuild node-sass &&
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c beta &&
          cp -rvfap ./src/assets ./dist &&
          cd ./dist &&
          zip -r dist.zip ./*
        continue-on-error: true
      
      - name: Capture build error
        id: capture_error
        if: steps.build.outcome == 'failure'
        run: |
          ERROR="Build failed: npm install or build process failed"
          echo "error=$ERROR" >> $GITHUB_OUTPUT
      
      - name: Copy files via ssh rsync
        id: rsync
        if: steps.build.outcome == 'success'
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr 
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: /dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.BETA_REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.REMOTE_USER }}
        continue-on-error: true
      
      - name: Capture rsync error
        if: steps.rsync.outcome == 'failure'
        id: rsync_error
        run: |
          ERROR="File transfer failed: Unable to copy files to remote server"
          echo "error=$ERROR" >> $GITHUB_OUTPUT
      
      - name: Build & Deploy
        id: deploy
        if: steps.rsync.outcome == 'success'
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.BETA_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} ' 
          cd /var/www/vst-beta.arealytics.com.au/ &&
          sudo unzip -o -d "/var/www/vst-beta.arealytics.com.au/" /home/<USER>/dist.zip &&
          sudo chown -R build.automation:ec2-user /var/www/vst-beta.arealytics.com.au &&
          sudo rm -rf /home/<USER>/dist.zip 
          '
        continue-on-error: true
      
      - name: Capture deployment error
        if: steps.deploy.outcome == 'failure'
        id: deploy_error
        run: |
          ERROR="Deployment failed: Unable to deploy files to server"
          echo "error=$ERROR" >> $GITHUB_OUTPUT

  notify:
    name: Notify Slack for Deployment
    runs-on: ubuntu-latest
    needs: deploy
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Get current deployment changes
        id: get-changes
        run: |
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      
      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          message: |
            *${{ github.repository }} - #${{ github.run_number }}*
            Started by user ${{ github.actor }}
            *Deployment Status:* ${{ job.status }}
            ${{ needs.deploy.outputs.error_message != '' && format('*Error Details:* {0}', needs.deploy.outputs.error_message) || '' }}
            *Changes in Current Deployment:* ${{ steps.get-changes.outputs.changes }}
            *Deployment URL:* https://vst-beta.arealytics.com.au
          color: ${{ job.status == 'success' && 'good' || 'danger' }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
