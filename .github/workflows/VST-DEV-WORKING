name: Deploy to aws Empirical_Virtual_Site_Tool

on:
   workflow_dispatch:
      inputs:
         target-branch:
          description: 'Branch to deploy'
          default: 'develop'
          required: true
jobs:
  job_one:
      name: Deploy to Empirical_Virtual_Site_Tool
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v2 
        - name: Build & Deploy
          env:
            SSH_PRIVATE_KEY: ${{ secrets.DEV_UAT_EC2_PRIVATE_KEY }}
            REMOTE_HOST: ${{ secrets.DEV_UAT_HOSTNAME }}
            REMOTE_USER: ${{ secrets.DEV_UAT_USERNAME }}
          run: |
            echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
            ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
         
              # Now we have got the access of EC2 and we will start the deploy .
              cd /home/<USER>/Empirical_Virtual_Site_Tool && git stash && git pull &&
              git checkout ${{ inputs.target-branch }} &&
              git pull origin ${{ inputs.target-branch }} && git stash apply &&
              npm install &&
              npm install node-sass --save  &&
              npm rebuild node-sass &&
              node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build --env dev
              rm -rf ./dist/assets &&
              mkdir ./dist/assets &&
              cp -rvfp ./src/assets ./dist/assets  &&
              zip -r dist.zip ./dist/* &&
              sudo unzip -o -d "/var/www/vst-dev.arealytics.com.au" ./dist.zip
              sudo chown -R build.automation:build.automation /var/www/vst-dev.arealytics.com.au/dist
              rm -rf dist.zip
              '
