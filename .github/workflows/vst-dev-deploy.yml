name: VST DEV Deployment

on:
  workflow_dispatch:
    inputs:
      workflow_mode:
        description: 'Select workflow execution mode'
        required: true
        default: 'full_pipeline'
        type: choice
        options:
          - full_pipeline
          - tests_only
          - deploy_only

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    if: ${{ inputs.workflow_mode == 'full_pipeline' || inputs.workflow_mode == 'tests_only' }}
    outputs:
      test-status: ${{ steps.test-result.outputs.status }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: |
          cd ./e2e &&
          # Clean install with legacy peer deps to resolve dependency conflicts
          npm cache clean --force &&
          # Install sass instead of node-sass for better compatibility
          #npm install sass --save-dev --legacy-peer-deps &&
          npm install --legacy-peer-deps
      
      - name: Install Playwright Browsers
        run: |
          cd ./e2e &&
          npx playwright install chromium --with-deps
    
      - name: Run E2E Tests
        id: run-tests
        run: |
          cd ./e2e &&
          # Configure Playwright to generate reports
          ENV=dev npx playwright test --reporter=html,junit,json
        continue-on-error: true

      - name: Set Test Result Status
        id: test-result
        run: |
          if [ ${{ steps.run-tests.outcome }} == 'success' ]; then
            echo "status=passed" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

      - name: List generated files (for debugging)
        if: always()
        run: |
          echo "=== Listing e2e directory structure ==="
          find ./e2e -type f -name "*report*" -o -name "*result*" -o -name "*.xml" -o -name "*.json" -o -name "*.html" | head -20
          echo "=== Current directory structure ==="
          ls -la ./e2e/

      - name: Upload Test Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-reports
          path: |
            e2e/playwright-report/
            e2e/test-results/
            e2e/results.xml
            e2e/results.json
            e2e/junit.xml
            e2e/**/*-results.json
            e2e/**/*-report.html
            e2e/**/*.xml
          retention-days: 30
          if-no-files-found: warn

      - name: Fail Job if Tests Failed
        if: steps.test-result.outputs.status == 'failed'
        run: |
          echo "E2E tests failed. Deployment will be skipped."
          exit 1

  deploy:
    name: Deploy to VST Dev
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: |
      always() &&
      (
        (inputs.workflow_mode == 'full_pipeline' && needs.e2e-tests.outputs.test-status == 'passed') ||
        inputs.workflow_mode == 'deploy_only'
      )
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '14'

      - name: Get Deployment Changes
        id: get-changes
        run: |
          BRANCH_NAME="develop"
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      
      - name: Build Application
        run: |
          npm ci &&
          npm install sass --save-dev &&
          npm rebuild sass &&
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c dev &&
          cp -rvfap ./src/assets ./dist &&
          cd ./dist &&
          zip -r dist.zip ./*
      
      - name: Copy Files via SSH rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: /dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.REMOTE_USER }}

      - name: Deploy to Remote Server
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.DEV_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
            cd /var/www/vst-dev.arealytics.com.au &&
            sudo rm -rf ./dist_backup.zip &&
            sudo zip -r dist_backup.zip ./* &&
            sudo unzip -o -d /var/www/vst-dev.arealytics.com.au /home/<USER>/dist.zip &&
            sudo rm -rf /home/<USER>/dist.zip
          '
  
  notify:
    name: Notify Slack
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ needs.deploy.result }}
          message: |
            *${{ github.repository }} - Run #${{ github.run_number }}*
            Started by: ${{ github.actor }}
            *Deployment Status:* ${{ needs.deploy.result }}
            *Changes:* ${{ needs.deploy.outputs.changes }}
            *Deployment URL:* https://vst-dev.arealytics.com.au
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
