name: VST training Deployment 

on:
   workflow_dispatch

jobs:
  deploy:
    name: Deploy to vst training on develop branch
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v2
        with:
          node-version: 'v10.24.1'
      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT    
      - name: commands     
        run: |
          cd ./ &&
          npm install node-sass --save &&
          npm install &&
          npm rebuild node-sass &&
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c training &&
          cp -rvfap ./src/assets ./dist &&
          cd ./dist &&
          zip -r dist.zip ./*
        continue-on-error: true
      - name: Copy files via ssh rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr 
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: /dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.TRAINING_REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.TRAINING_REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.TRAINING_USER }}
      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.TRAINING_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.TRAINING_HOSTNAME }}
          REMOTE_USER: ${{ secrets.TRAINING_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} ' 
          cd /var/www/vst-training.arealytics.com.au/ &&
          #sudo rm -rf ./dist_backup.zip
          sudo zip -r dist_backup.zip ./*&&
          sudo unzip -o -d "/var/www/vst-training.arealytics.com.au/" /home/<USER>/dist.zip
          sudo chown -R ec2-user:empirical-users /var/www/vst-training.arealytics.com.au
          sudo rm -rf /home/<USER>/dist.zip
           '
  notify:
    name: Notify Slack for Deployment
    runs-on: ubuntu-latest
    needs: deploy
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          message: |
            *${{ github.repository }} - #${{ github.run_number }}*
            Started by user ${{ github.actor }}
            *Deployment Status:* ${{ job.status }}
            *Changes in Current Deployment:* ${{ steps.get-changes.outputs.changes }}
            *Deployment URL:* https://vst-training.arealytics.com.au
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()           
