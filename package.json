{"name": "empirical-research-tool", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~12.2.17", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.17", "@angular/compiler": "~12.2.17", "@angular/core": "~12.2.17", "@angular/forms": "~12.2.17", "@angular/material": "^12.2.13", "@angular/platform-browser": "~12.2.17", "@angular/platform-browser-dynamic": "~12.2.17", "@angular/platform-server": "~12.2.17", "@angular/router": "~12.2.17", "@fortawesome/fontawesome-free": "^5.15.4", "@googlemaps/markerclusterer": "2.5.3", "@ng-select/ng-select": "^6.1.0", "@terraformer/wkt": "^2.2.0", "@types/bootbox": "^4.4.36", "@types/jquery": "^3.5.29", "angular-mydatepicker": "^0.10.1", "aws-sdk": "^2.1516.0", "bootbox": "^4.4.0", "bootstrap": "5.3.3", "core-js": "^3.38.1", "crypto-js": "^3.1.9-1", "dateformat": "^3.0.3", "deck.gl": "8.9.35", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.8", "jquery": "^3.3.1", "ng2-file-upload": "^1.3.0", "ngx-bootstrap": "^7.1.0", "ngx-currency": "^2.5.3", "ngx-gallery-9": "^1.0.6", "ngx-image-viewer": "^1.0.6", "ngx-ui-switch": "^1.6.0", "primeicons": "^4.1.0", "primeng": "^12.2.0", "rxjs": "6.5.5", "rxjs-compat": "^6.2.0", "sass": "^1.89.2", "underscore": "^1.9.2", "uuid": "^10.0.0", "zone.js": "^0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.18", "@angular/cli": "~12.2.18", "@angular/compiler-cli": "~12.2.17", "@angular/language-service": "^12.2.17", "@types/aws-sdk": "^2.7.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "^2.0.3", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "^7.0.0", "rxjs-tslint": "^0.1.8", "ts-node": "~8.3.0", "tslint": "^6.1.3", "typescript": "~4.3.5", "webpack": "^4.47.0"}, "resolutions": {"@types/node": "12.12.62"}}