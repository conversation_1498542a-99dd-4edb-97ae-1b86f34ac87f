{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"empirical-research-tool": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", "src/config.js"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/bootbox/bootbox.min.js"], "allowedCommonJsDependencies": ["long", "pbf", "fast-xml-parser", "<PERSON><PERSON><PERSON>"], "aot": false, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "dev": {"optimization": true, "outputHashing": "all", "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.ts"}]}, "training": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.training.ts"}]}, "basetraining": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.basetraining.ts"}]}, "validatetraining": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.validatetraining.ts"}]}, "beta": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}]}, "phoenixdev": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.phoenix.dev.ts"}]}, "uat": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "empirical-research-tool:build"}, "configurations": {"production": {"browserTarget": "empirical-research-tool:build:production"}, "local": {"browserTarget": "empirical-research-tool:build:local"}, "beta": {"browserTarget": "empirical-research-tool:build:beta"}, "training": {"browserTarget": "empirical-research-tool:build:training"}, "basetraining": {"browserTarget": "empirical-research-tool:build:basetraining"}, "validatetraining": {"browserTarget": "empirical-research-tool:build:basetraining"}, "phoeniz-dev": {"browserTarget": "empirical-research-tool:build:phoenixdev"}, "uat": {"browserTarget": "empirical-research-tool:build:uat"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "empirical-research-tool:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/bootbox/bootbox.min.js"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "assets": ["src/assets", "src/favicon.ico", "src/config.js"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "empirical-research-tool-e2e": {"root": "e2e", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "empirical-research-tool:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "empirical-research-tool", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "css"}, "@schematics/angular:directive": {"prefix": "app"}, "@schematics/angular:application": {"strict": false}}, "cli": {"analytics": false}}