import { test, expect, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config'; 

import Login from '../pages/login.page';
import formSearch from '../pages/formSearch';


test.describe('Form Search Tests', () => {
  let pagelogin: Page;

  test.beforeEach(async ({ page }) => {
    // @ts-ignore
    const environment = process.env.ENV || 'dev';
    const baseURL = environmentConfig[environment]?.baseURL;
    await page.goto(baseURL);
    await page.setViewportSize({ width: 1920, height: 1040 });

    const loginPage = new Login(page);
    await loginPage.login();

     pagelogin = page;
  });

  // Test to validate all the fields in form search
  test('Check the search form', async () => {
    const formsearch = new formSearch(pagelogin);
    await formsearch.validateFormFields();
  });

  // Test to validate that a property with the given PID is in the search results, expected count be greater than 0
  test('Should search for PID and validate results', async () => {
    const formsearch = new formSearch(pagelogin);
    const results = await formsearch.propertyFormSearch();
    await expect(results.resultsCount).toBeGreaterThan(0);
    await expect(results.success).toBeTruthy();
  });

  // Test: Verify that property search returns results when using a valid postal code, expected count be greater than 0
  test('Should search with postal code', async () => {
    const formsearch = new formSearch(pagelogin);
    const result = await formsearch.postalCodeSearch();
    await expect(result.success).toBeTruthy();
    await expect(result.resultsCount).toBeGreaterThan(0);
  });

})


