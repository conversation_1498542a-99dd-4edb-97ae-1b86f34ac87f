#!/bin/bash

# Check if environment argument is provided
if [ -z "$1" ]; then
  ENV="dev"
else
  ENV="$1"
fi

# Check if headed mode is requested
if [ "$2" == "headed" ]; then
  HEADED="--headed"
else
  HEADED=""
fi

# Check Node.js version
NODE_VERSION=$(node -v)
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1 | tr -d 'v')

if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
  echo "Warning: Playwright requires Node.js v18 or higher. You're using $NODE_VERSION."
  echo "Tests may not run correctly. Please upgrade Node.js."
  echo ""
fi

# Set environment variable and run tests
echo "Running tests in $ENV environment..."
ENV=$ENV npx playwright test $HEADED

# Check exit code
if [ $? -eq 0 ]; then
  echo "Tests completed successfully!"
else
  echo "Tests failed. Check the output above for details."
fi
