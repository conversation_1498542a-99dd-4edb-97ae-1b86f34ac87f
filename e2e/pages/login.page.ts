import { Page } from '@playwright/test'; // Importing Playwright types

// You need to make sure `credentials`, `selectors`, and `urls` are imported correctly from your config.
import { login } from '../fixtures/test-data';

export default class Login {
  constructor(private page: Page) {}  // Use TypeScript for typing

  async login() {
    const page = this.page;
  await page.getByRole('textbox', { name: 'Username' }).click();
  await page.getByRole('textbox', { name: '<PERSON>rna<PERSON>' }).fill(login.username);
  await page.getByRole('textbox', { name: 'Password' }).click();
  await page.getByRole('textbox', { name: 'Password' }).fill(login.password);
  await page.getByRole('button', { name: 'Login' }).click();

    // await page.waitForURL(urls.postLoginUrl);  
  }
}


