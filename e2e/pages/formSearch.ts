import { Page, expect } from '@playwright/test';
import { PropertyUse } from '../enumerations/propertyUse';

export default class FormSearch {
  private page: Page;
  private pid = '672654';
  private postalCode = '3000';

  constructor(page: Page) {
    this.page = page;
  }

  //validate all fields in form search
  async validateFormFields() {
    await this.page.waitForTimeout(5000);
    await expect(this.page.getByText('Property Audit Tool')).toBeVisible();
    await expect(this.page.getByTitle('New Updates').locator('i')).toBeVisible();
    await expect(this.page.getByText('PROPERTY SEARCH')).toBeVisible();

    await expect(this.page.getByText('PID')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Property ID' })).toBeVisible();

    await expect(this.page.getByText('Property Use')).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Office })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Industrial })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Retail })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Apartments })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Special })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: PropertyUse.Land })).toBeVisible();
    await expect(this.page.getByText('Street Number')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Min', exact: true })).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Max', exact: true })).toBeVisible();

    await expect(this.page.getByText('Street Name')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Street Name' })).toBeVisible();

    await expect(this.page.getByText('Postal Code')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Postal Code' })).toBeVisible();

    await expect(this.page.getByText('State')).toBeVisible();
    // 1. Click on the dropdown
    const dropdown = this.page.locator('input[role="combobox"]#State');
    await expect(dropdown).toBeVisible();
    await dropdown.click(); // This opens the dropdown
    await this.page.waitForTimeout(1000);
    // 2. Locate the options container and fetch options
    const options = this.page.locator('[role="option"]');
    await expect(options.first()).toBeVisible();
    await expect(options).toHaveCount(6);

    await expect(this.page.getByText('City')).toBeVisible();
    await expect(this.page.locator('#City')).toBeVisible();

    await expect(this.page.getByText('Research Status')).toBeVisible();
    await expect(this.page.locator('#researchType').first()).toBeVisible();

    await expect(this.page.getByText('Without Footprint')).toBeVisible();
    await expect(this.page.getByTestId('withoutFootprint-checkbox')).toBeVisible();

    await expect(this.page.getByText('Without Parcel')).toBeVisible();
    await expect(this.page.getByTestId('withoutParcel-checkbox')).toBeVisible();

    await expect(this.page.getByText('Not Strata Only')).toBeVisible();
    await expect(this.page.getByTestId('notStrata-checkbox')).toBeVisible();

    await expect(this.page.getByText('Exclude Hidden')).toBeVisible();
    await expect(this.page.getByTestId('excludeHidden-checkbox')).toBeVisible();

    await expect(this.page.getByText('Last Modified By')).toBeVisible();
    await expect(this.page.locator('#researcher')).toBeVisible();

    await expect(this.page.getByText('Audit Status')).toBeVisible();
    await expect(this.page.locator('#researchType').nth(1)).toBeVisible();

    await expect(this.page.getByText('Not Reviewed')).toBeVisible();
    await expect(this.page.getByTestId('notReviewed-checkbox')).toBeVisible();

    await expect(this.page.getByText('Last Reviewed')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Min Date' })).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Max Date' })).toBeVisible();

    await expect(this.page.getByRole('button', { name: 'Reset' })).toBeVisible();
    await expect(this.page.locator('.btn.search-btn')).toBeEnabled();
    await expect(this.page.getByRole('button', { name: 'Add Property' })).toBeVisible();
    await expect(this.page.getByRole('button', { name: 'Map Search' })).toBeVisible();
  };

  // Search for property using PID from the form search 
  async propertyFormSearch(): Promise<{ success: boolean; resultsCount: number }> {
    try {
      // Enter Property ID and search
      await this.page.getByRole('textbox', { name: 'Property ID' }).click();
      await this.page.getByRole('textbox', { name: 'Property ID' }).fill(this.pid);
      await this.page.locator('.btn.search-btn').click();
      await this.page.waitForTimeout(5000);

      // Locate the cell with expected PID
      const cell = this.page.getByRole('cell', { name: this.pid });
      const count = await cell.count();

      if (count > 0) {
        await expect(cell.first()).toHaveText(this.pid);
        return { success: true, resultsCount: count };
      } else {
        return { success: false, resultsCount: 0 };
      }
    } catch (error) {
      console.error('Exception during property id search:', error);
      return { success: false, resultsCount: 0 };
    }
  }

  // Search for property using postalcode from the form search 
  async postalCodeSearch() {
    try {
      // Click and fill the postal code input
      await this.page.getByRole('textbox', { name: 'Postal Code' }).click();
      await this.page.getByRole('textbox', { name: 'Postal Code' }).fill(this.postalCode);
      await this.page.locator('.btn.search-btn').click();
      await this.page.waitForTimeout(5000);
      // Get matching result cell
      const cell = this.page.getByRole('cell', { name: '001' });
      await cell.first().waitFor({ timeout: 5000 });
      const count = await cell.count();

      // Return result status and count
      if (count > 0) {
        return { success: true, resultsCount: count };
      } else {
        return { success: false, resultsCount: 0 };
      }
    } catch (error) {
      console.error('Exception during postal code search:', error);
      return { success: false, resultsCount: 0 };
    }
  }

}


