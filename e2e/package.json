{"name": "e2e-playwright", "version": "1.0.0", "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:report": "npx playwright show-report", "test:local": "ENV=local npx playwright test", "test:dev": "ENV=dev npx playwright test", "test:uat": "ENV=uat npx playwright test", "test:local:headed": "ENV=local npx playwright test --headed", "test:dev:headed": "ENV=dev npx playwright test --headed", "test:uat:headed": "ENV=uat npx playwright test --headed", "codegen:local": "npx playwright codegen http://localhost:4200/", "codegen:dev": "npx playwright codegen https://vst-dev.arealytics.com.au/", "codegen:uat": "npx playwright codegen https://vst-uat.arealytics.com.au", "codegen:dev:login": "npx playwright codegen https://vst-dev.arealytics.com.au/#/login", "codegen:uat:login": "npx playwright codegen https://vst-uat.arealytics.com.au/#/login"}, "devDependencies": {"@playwright/test": "^1.43.1", "@types/node": "^22.15.11", "typescript": "^5.3.3"}}